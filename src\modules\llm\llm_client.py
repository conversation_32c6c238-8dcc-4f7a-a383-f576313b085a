"""
LLMClient - 统一的大模型访问客户端（Claude API 兼容: 智谱 BigModel Anthropic Endpoint）
- 支持 provider/api_base/model/timeout/retries
- generate_mapping_suggestions: 输入标准化结构，返回结构化建议或 None（失败回退）
"""
from __future__ import annotations

import json
import os
import re
from typing import Any, Dict, List, Optional

import urllib.request
import urllib.error

from src.modules.logging.setup_logger import setup_logger


class LLMClient:
    def __init__(self, *, provider: str = "anthropic", api_base: str = "", model: str = "glm-4.5",
                 timeout_seconds: int = 15, max_retries: int = 1):
        self.logger = setup_logger(__name__)
        self.provider = (provider or "anthropic").lower()
        # 优先环境变量，其次使用传入 api_base，最后使用各 provider 默认
        env_base = os.getenv("ANTHROPIC_BASE_URL", "").strip()
        self.api_base = (env_base or api_base or self._default_base())
        self.model = model or "glm-4.5"
        self.timeout_seconds = int(timeout_seconds or 15)
        self.max_retries = int(max_retries or 1)

    def _default_base(self) -> str:
        if self.provider == "anthropic":
            return "https://open.bigmodel.cn/api/anthropic"
        return "https://open.bigmodel.cn/api/anthropic"

    def _get_api_key(self) -> Optional[str]:
        if self.provider == "anthropic":
            return os.getenv("ANTHROPIC_API_KEY")
        return None

    def _build_prompt(self, payload: Dict[str, Any]) -> str:
        table_type = payload.get("table_type") or ""
        headers = payload.get("headers_norm") or []
        sample = payload.get("sample_data") or {}

        instruction = (
            "你是一个企业薪酬/异动数据的智能字段映射助手。根据提供的 Excel 列名和已脱敏的样本数据，"
            "为每个源列生成目标数据库字段名、显示名、数据类型、置信度和理由。严格输出 JSON。"
        )

        schema = {
            "type": "object",
            "properties": {
                "results": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "source_field": {"type": "string"},
                            "target_field": {"type": "string"},
                            "display_name": {"type": "string"},
                            "data_type": {"type": "string"},
                            "confidence": {"type": "number"},
                            "confidence_level": {"type": "string"},
                            "reasoning": {"type": "string"},
                            "is_required": {"type": "boolean"}
                        },
                        "required": [
                            "source_field", "target_field", "display_name", "data_type",
                            "confidence", "confidence_level", "reasoning", "is_required"
                        ]
                    }
                }
            },
            "required": ["results"]
        }

        sample_preview = json.dumps({k: (v[:5] if isinstance(v, list) else []) for k, v in sample.items()}, ensure_ascii=False)
        headers_preview = json.dumps(headers, ensure_ascii=False)
        schema_preview = json.dumps(schema, ensure_ascii=False)

        prompt = (
            f"任务: 依据表类型与列名进行字段映射并返回 JSON 结构化结果。\n"
            f"表类型: {table_type}\n"
            f"Excel列名(已标准化): {headers_preview}\n"
            f"样本数据(已脱敏，占位符 NUM/DATE/NAME/ID/TEXT): {sample_preview}\n"
            "规则:\n"
            "- target_field 需语义明确，风格一致且适用于数据库命名(英文、下划线、不可有空格)\n"
            "- display_name 为面向用户的中文名\n"
            "- data_type 可为 string/int/float/date/decimal 等通用类型即可\n"
            "- confidence 0-1 之间，confidence_level 取 high/medium/low\n"
            "- is_required 对姓名、工号、异动类型等应为 True\n"
            "- 严格输出 JSON，不要包含解释性文字\n"
            f"JSON Schema: {schema_preview}\n"
            "请仅输出 JSON，不要添加额外内容。"
        )
        return prompt

    def _parse_response_text_to_items(self, text: str) -> Optional[List[Dict[str, Any]]]:
        if not text:
            return None
        # 提取 ```json ... ``` 或第一个 {...} JSON 块
        fence = re.search(r"```json\s*(\{[\s\S]*?\})\s*```", text, re.IGNORECASE)
        raw = fence.group(1) if fence else None
        if not raw:
            # 尝试匹配第一个 JSON 对象
            obj = re.search(r"\{[\s\S]*\}", text)
            if obj:
                raw = obj.group(0)
        if not raw:
            return None
        try:
            data = json.loads(raw)
            if isinstance(data, dict) and isinstance(data.get("results"), list):
                return data["results"]
            # 兼容顶层直接为 list 的情况
            if isinstance(data, list):
                return data
            return None
        except Exception:
            return None

    def generate_mapping_suggestions(self, payload: Dict[str, Any]) -> Optional[List[Dict[str, Any]]]:
        """
        期望 payload 结构：
        {
          'table_type': str,
          'headers_norm': List[str],
          'sample_data': Optional[Dict[str, List[Any]]],
          'history_summary': Optional[Dict[str, Any]]
        }
        成功返回：List[Dict]，元素字段包含 source_field/target_field/display_name/data_type/confidence/confidence_level/reasoning/is_required
        失败返回：None（由调用方回退）
        """
        try:
            if self.provider != "anthropic":
                self.logger.info(f"[LLMClient] 未实现的provider: {self.provider}，返回None以回退")
                return None

            api_key = self._get_api_key()
            if not api_key:
                self.logger.warning("[LLMClient] 未发现 ANTHROPIC_API_KEY 环境变量，返回None以回退")
                return None

            # 构造请求
            url = self.api_base.rstrip("/") + "/v1/messages"
            prompt = self._build_prompt(payload)
            req_body = {
                "model": self.model,
                "max_tokens": 1024,
                "stream": False,
                "messages": [
                    {"role": "user", "content": prompt}
                ]
            }
            body_bytes = json.dumps(req_body).encode("utf-8")

            request = urllib.request.Request(url, data=body_bytes, method="POST")
            request.add_header("x-api-key", api_key)
            request.add_header("content-type", "application/json")
            # Anthropic 兼容头，部分代理/网关要求
            request.add_header("anthropic-version", "2023-06-01")
            request.add_header("accept", "application/json")
            # 某些实现可能需要 beta 头；若后端忽略也不会报错
            request.add_header("anthropic-beta", "messages-2023-12-15")

            # 尝试重试
            last_err: Optional[Exception] = None
            for _ in range(max(1, self.max_retries)):
                try:
                    with urllib.request.urlopen(request, timeout=self.timeout_seconds) as resp:
                        if resp.status != 200:
                            last_err = Exception(f"HTTP {resp.status}")
                            continue
                        raw_text = resp.read().decode("utf-8", errors="ignore")
                        # 兼容标准Anthropic响应：content: [{type: 'text', text: '...'}]
                        try:
                            data = json.loads(raw_text)
                            content = data.get("content")
                            text = None
                            if isinstance(content, list) and content:
                                block = content[0]
                                if isinstance(block, dict):
                                    text = block.get("text") or block.get("content") or raw_text
                            else:
                                text = raw_text
                        except Exception:
                            text = raw_text

                        items = self._parse_response_text_to_items(text)
                        if items:
                            return items
                        # 若解析失败，作为失败处理以便回退或重试
                        last_err = Exception("解析响应失败")
                except urllib.error.HTTPError as e:
                    last_err = e
                except urllib.error.URLError as e:
                    last_err = e
                except Exception as e:
                    last_err = e

            if last_err:
                self.logger.warning(f"[LLMClient] 调用失败，最后错误: {last_err}")
            return None
        except Exception as e:
            self.logger.warning(f"[LLMClient] 生成建议失败: {e}")
            return None
