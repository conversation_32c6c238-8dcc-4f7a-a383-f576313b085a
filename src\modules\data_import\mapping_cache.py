"""
MappingCache - LLM 映射结果缓存（Phase 1 MVP）
- 以 headers_norm + table_type + sample_signature 为键
- JSO<PERSON> 文件持久化，容忍失败
"""
from __future__ import annotations

from typing import Any, Dict, List, Optional
import json
import os
import hashlib
import time

from src.modules.logging.setup_logger import setup_logger


class MappingCache:
    def __init__(self, cache_file: str = os.path.join("cache", "config_cache", "llm_mapping_cache.json")):
        self.logger = setup_logger(__name__)
        self.cache_file = cache_file
        self._cache: Dict[str, Any] = {}
        self._load()

    def _load(self):
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, "r", encoding="utf-8") as f:
                    self._cache = json.load(f)
        except Exception as e:
            self.logger.warning(f"[MappingCache] 加载缓存失败: {e}")
            self._cache = {}

    def _save(self):
        try:
            os.makedirs(os.path.dirname(self.cache_file), exist_ok=True)
            with open(self.cache_file, "w", encoding="utf-8") as f:
                json.dump(self._cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.warning(f"[MappingCache] 保存缓存失败: {e}")

    @staticmethod
    def _signature(headers_norm: List[str], table_type: str, sample_data: Optional[Dict[str, List[Any]]]) -> str:
        # 仅使用列名与采样占位符的hash作为签名，避免敏感数据落盘
        payload = {
            "table_type": table_type,
            "headers": headers_norm,
            "sample": {k: (v[:8] if isinstance(v, list) else []) for k, v in (sample_data or {}).items()}
        }
        raw = json.dumps(payload, ensure_ascii=False, sort_keys=True)
        return hashlib.sha256(raw.encode("utf-8")).hexdigest()

    def get(self, headers_norm: List[str], table_type: str, sample_data: Optional[Dict[str, List[Any]]], ttl_minutes: Optional[int] = None) -> Optional[List[Dict[str, Any]]]:
        try:
            key = self._signature(headers_norm, table_type, sample_data)
            entry = self._cache.get(key)
            if entry is None:
                return None
            # 兼容老格式：直接存 List
            if isinstance(entry, list):
                return entry
            # 新格式：{'v': value, 'ts': epoch_seconds}
            if isinstance(entry, dict):
                value = entry.get('v')
                ts = entry.get('ts')
                if isinstance(ttl_minutes, int) and isinstance(ts, (int, float)):
                    if ttl_minutes > 0:
                        if (time.time() - float(ts)) > ttl_minutes * 60:
                            return None
                return value
            return None
        except Exception:
            return None

    def set(self, headers_norm: List[str], table_type: str, sample_data: Optional[Dict[str, List[Any]]], value: List[Dict[str, Any]]):
        try:
            key = self._signature(headers_norm, table_type, sample_data)
            self._cache[key] = {"v": value, "ts": int(time.time())}
            self._save()
        except Exception as e:
            self.logger.warning(f"[MappingCache] 设置缓存失败: {e}")
