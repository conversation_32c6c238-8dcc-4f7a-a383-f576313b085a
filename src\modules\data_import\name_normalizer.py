"""
NameNormalizer
- normalize_for_matching: 温和标准化（用于匹配，不改变语义）
- enforce_db_field_naming: 结果命名规范（用于数据库字段名，保证可用与一致）
"""
from __future__ import annotations

import re
from typing import Optional


class NameNormalizer:
    @staticmethod
    def normalize_for_matching(name: str) -> str:
        """用于匹配的温和标准化：
        - 去首尾空白
        - 合并多空格为一个空格
        - 小写化（仅用于匹配，不改变结果呈现）
        - 移除噪声符号（保留中文/字母/数字/下划线/连字符/空格）
        """
        if name is None:
            return ""
        s = str(name).strip()
        s = re.sub(r"\s+", " ", s)
        s = s.lower()
        s = re.sub(r"[^\w\-\u4e00-\u9fa5 ]", "", s)
        return s

    @staticmethod
    def enforce_db_field_naming(name: str, *, max_len: int = 64) -> str:
        """用于数据库字段名的规范化：
        - 去空白、转小写
        - 将空白转换为下划线
        - 仅保留字母/数字/下划线（中文转保留为原字符，按平台实际策略再决定）
        - 以数字开头时加前缀 'f_'
        - 统一多下划线为一个
        - 截断至 max_len
        """
        if name is None:
            return ""
        s = str(name).strip().lower()
        s = re.sub(r"\s+", "_", s)
        s = re.sub(r"[^\w\u4e00-\u9fa5]", "_", s)
        s = re.sub(r"_+", "_", s)
        if s and s[0].isdigit():
            s = f"f_{s}"
        if max_len and len(s) > max_len:
            s = s[:max_len]
        return s.strip("_")
