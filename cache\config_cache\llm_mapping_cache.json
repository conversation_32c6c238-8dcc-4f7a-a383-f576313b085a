{"9789d61267e6843f1476b2a89689e8bf8403908fe5ba48428018ddb4531e01d5": [{"source_field": "姓名", "target_field": "姓名", "display_name": "姓名", "data_type": "VARCHAR(255)", "confidence": 0.95, "confidence_level": "high", "reasoning": "模板精确匹配 + 语义验证(personal_info) + 基于样本数据调整类型", "is_required": true}, {"source_field": "工号", "target_field": "工号", "display_name": "工号", "data_type": "VARCHAR(255)", "confidence": 0.95, "confidence_level": "high", "reasoning": "模板精确匹配 + 语义验证(personal_info) + 基于样本数据调整类型", "is_required": true}, {"source_field": "基本工资", "target_field": "基本工资", "display_name": "基本工资", "data_type": "VARCHAR(255)", "confidence": 0.94, "confidence_level": "high", "reasoning": "模板精确匹配 + 语义验证(money) + 基于样本数据调整类型", "is_required": false}, {"source_field": "津贴", "target_field": "津贴", "display_name": "津贴", "data_type": "VARCHAR(255)", "confidence": 0.9199999999999999, "confidence_level": "high", "reasoning": "模板精确匹配 + 语义验证(money) + 基于样本数据调整类型", "is_required": false}, {"source_field": "实发合计", "target_field": "应发合计", "display_name": "实发合计", "data_type": "VARCHAR(255)", "confidence": 0.9199999999999999, "confidence_level": "high", "reasoning": "模板精确匹配 + 语义验证(money) + 基于样本数据调整类型", "is_required": true}, {"source_field": "入职日期", "target_field": "time", "display_name": "入职日期", "data_type": "VARCHAR(255)", "confidence": 0.63, "confidence_level": "medium", "reasoning": "语义分析(time) + 基于样本数据调整类型", "is_required": false}], "44d84d5b4560084feb9f7cecb6c53bd8e9050d825308ec2ebf099088bd266e51": {"v": [{"source_field": "姓名", "target_field": "姓名", "display_name": "姓名", "data_type": "VARCHAR(255)", "confidence": 0.95, "confidence_level": "high", "reasoning": "模板精确匹配 + 语义验证(personal_info) + 基于样本数据调整类型", "is_required": true}, {"source_field": "部门名称", "target_field": "organization", "display_name": "部门名称", "data_type": "VARCHAR(255)", "confidence": 0.63, "confidence_level": "medium", "reasoning": "语义分析(organization) + 基于样本数据调整类型", "is_required": true}, {"source_field": "结余\n津贴", "target_field": "money", "display_name": "结余\n津贴", "data_type": "VARCHAR(255)", "confidence": 0.48999999999999994, "confidence_level": "low", "reasoning": "语义分析(money) + 基于样本数据调整类型", "is_required": false}, {"source_field": "生活\n补贴", "target_field": "money_2", "display_name": "生活\n补贴", "data_type": "VARCHAR(255)", "confidence": 0.48999999999999994, "confidence_level": "low", "reasoning": "语义分析(money) + 基于样本数据调整类型", "is_required": false}, {"source_field": "住房\n补贴", "target_field": "money_3", "display_name": "住房\n补贴", "data_type": "VARCHAR(255)", "confidence": 0.48999999999999994, "confidence_level": "low", "reasoning": "语义分析(money) + 基于样本数据调整类型", "is_required": false}, {"source_field": "物业\n补贴", "target_field": "money_4", "display_name": "物业\n补贴", "data_type": "VARCHAR(255)", "confidence": 0.48999999999999994, "confidence_level": "low", "reasoning": "语义分析(money) + 基于样本数据调整类型", "is_required": false}, {"source_field": "离休\n补贴", "target_field": "money_5", "display_name": "离休\n补贴", "data_type": "VARCHAR(255)", "confidence": 0.48999999999999994, "confidence_level": "low", "reasoning": "语义分析(money) + 基于样本数据调整类型", "is_required": false}, {"source_field": "增发一次\n性生活补贴", "target_field": "money_6", "display_name": "增发一次\n性生活补贴", "data_type": "VARCHAR(255)", "confidence": 0.48999999999999994, "confidence_level": "low", "reasoning": "语义分析(money) + 基于样本数据调整类型", "is_required": false}, {"source_field": "合计", "target_field": "money_7", "display_name": "合计", "data_type": "VARCHAR(255)", "confidence": 0.48999999999999994, "confidence_level": "low", "reasoning": "语义分析(money) + 基于样本数据调整类型", "is_required": true}, {"source_field": "序号", "target_field": "序号", "display_name": "序号", "data_type": "VARCHAR(255)", "confidence": 0.3, "confidence_level": "low", "reasoning": "基于样本数据调整类型 + 使用原字段名", "is_required": false}, {"source_field": "人员代码", "target_field": "人员代码", "display_name": "人员代码", "data_type": "VARCHAR(255)", "confidence": 0.3, "confidence_level": "low", "reasoning": "基于样本数据调整类型 + 使用原字段名", "is_required": false}, {"source_field": "基本\n离休费", "target_field": "基本_离休费", "display_name": "基本\n离休费", "data_type": "VARCHAR(255)", "confidence": 0.3, "confidence_level": "low", "reasoning": "基于样本数据调整类型 + 使用原字段名", "is_required": false}, {"source_field": "护理费", "target_field": "护理费", "display_name": "护理费", "data_type": "VARCHAR(255)", "confidence": 0.3, "confidence_level": "low", "reasoning": "基于样本数据调整类型 + 使用原字段名", "is_required": false}, {"source_field": "补发", "target_field": "补发", "display_name": "补发", "data_type": "VARCHAR(255)", "confidence": 0.3, "confidence_level": "low", "reasoning": "基于样本数据调整类型 + 使用原字段名", "is_required": false}, {"source_field": "借支", "target_field": "借支", "display_name": "借支", "data_type": "VARCHAR(255)", "confidence": 0.3, "confidence_level": "low", "reasoning": "基于样本数据调整类型 + 使用原字段名", "is_required": false}, {"source_field": "备注", "target_field": "备注", "display_name": "备注", "data_type": "VARCHAR(255)", "confidence": 0.3, "confidence_level": "low", "reasoning": "基于样本数据调整类型 + 使用原字段名", "is_required": false}], "ts": 1757949089}}