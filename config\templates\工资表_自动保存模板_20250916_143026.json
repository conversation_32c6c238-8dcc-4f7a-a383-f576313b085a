{"name": "工资表_自动保存模板", "description": "从C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls自动生成", "table_type": "工资表", "mapping_config": {"序号": {"target_field": "姓名", "display_name": "序号", "field_type": "name_string", "data_type": "string", "is_required": false}, "人员代码": {"target_field": "organization", "display_name": "人员代码", "field_type": "text_string", "data_type": "string", "is_required": false}, "姓名": {"target_field": "money", "display_name": "姓名", "field_type": "salary_float", "data_type": "string", "is_required": false}, "部门名称": {"target_field": "money", "display_name": "部门名称", "field_type": "salary_float", "data_type": "string", "is_required": false}, "基本\n离休费": {"target_field": "money", "display_name": "基本离休费", "field_type": "salary_float", "data_type": "float", "is_required": false}, "结余\n津贴": {"target_field": "money", "display_name": "结余津贴", "field_type": "salary_float", "data_type": "string", "is_required": false}, "生活\n补贴": {"target_field": "money", "display_name": "生活补贴", "field_type": "salary_float", "data_type": "string", "is_required": false}, "住房\n补贴": {"target_field": "money", "display_name": "住房补贴", "field_type": "salary_float", "data_type": "string", "is_required": false}, "物业\n补贴": {"target_field": "money", "display_name": "物业补贴", "field_type": "salary_float", "data_type": "string", "is_required": false}, "离休\n补贴": {"target_field": "序号", "display_name": "离休补贴", "field_type": "employee_id_string", "data_type": "string", "is_required": false}, "护理费": {"target_field": "人员代码", "display_name": "护理费", "field_type": "employee_id_string", "data_type": "float", "is_required": false}, "增发一次\n性生活补贴": {"target_field": "基本离休费", "display_name": "增发一次性生活补贴", "field_type": "text_string", "data_type": "string", "is_required": false}, "补发": {"target_field": "护理费", "display_name": "补发", "field_type": "text_string", "data_type": "float", "is_required": false}, "合计": {"target_field": "补发", "display_name": "合计", "field_type": "salary_float", "data_type": "float", "is_required": false}, "借支": {"target_field": "借支", "display_name": "借支", "field_type": "salary_float", "data_type": "float", "is_required": false}, "备注": {"target_field": "备注", "display_name": "备注", "field_type": "text_string", "data_type": "string", "is_required": false}}, "field_count": 16, "auto_generated": true, "id": "template_20250916_143026", "created_at": "2025-09-16T14:30:26.147086", "last_modified": "2025-09-16T14:30:26.147086", "file_path": "config/templates\\工资表_自动保存模板_20250916_143026.json", "template_format_version": "2.0"}