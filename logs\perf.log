2025-09-16 14:27:40.480 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-09-16 14:27:40.480 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-09-16 14:27:40.480 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-09-16 14:27:40.480 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-09-16 14:27:40.480 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-09-16 14:27:40.480 | INFO     | src:<module>:20 | 月度工资异动处理系统 v2.0.0-refactored 初始化完成
2025-09-16 14:27:44.213 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-09-16 14:27:44.213 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-09-16 14:27:44.213 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-09-16 14:27:44.213 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-09-16 14:27:44.213 | INFO     | __main__:setup_app_logging:423 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-09-16 14:27:44.213 | INFO     | __main__:main:487 | 初始化核心管理器...
2025-09-16 14:27:44.213 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-16 14:27:44.229 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:27:44.229 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:27:44.229 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:27:44.244 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-09-16 14:27:44.291 | INFO     | src.modules.data_storage.database_manager:_initialize_database:166 | 数据库初始化完成
2025-09-16 14:27:44.291 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-09-16 14:27:44.291 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-16 14:27:44.307 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:117 | 动态表管理器初始化完成
2025-09-16 14:27:44.307 | INFO     | __main__:main:492 | 核心管理器初始化完成。
2025-09-16 14:27:44.307 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-09-16 14:27:44.307 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-09-16 14:27:44.307 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-09-16 14:27:44.307 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-09-16 14:27:44.307 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-09-16 14:27:44.307 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-09-16 14:27:44.307 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-09-16 14:27:44.307 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:12209 | 🔧 [P2-3] 错误恢复策略注册完成
2025-09-16 14:27:44.307 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-09-16 14:27:44.307 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:12064 | 🔧 [P2-3] 错误处理机制设置完成
2025-09-16 14:27:44.307 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:12102 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-09-16 14:27:44.545 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-09-16 14:27:44.545 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-09-16 14:27:44.545 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-09-16 14:27:44.561 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:27:44.561 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:27:44.561 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:27:44.561 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:27:44.576 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:27:44.576 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:27:44.576 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:27:44.576 | INFO     | src.modules.system_config.config_manager:_create_backup:510 | 配置文件备份已创建: C:\test\salary_changes\salary_changes\config_backups\config_20250916_142744.json
2025-09-16 14:27:44.576 | INFO     | src.modules.system_config.config_manager:save_config:413 | 正在保存配置文件: C:\test\salary_changes\salary_changes\config.json
2025-09-16 14:27:44.623 | INFO     | src.modules.system_config.config_manager:save_config:418 | 配置文件保存成功
2025-09-16 14:27:44.623 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-09-16 14:27:44.623 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-16 14:27:44.623 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-16 14:27:44.623 | INFO     | src.core.field_mapping_manager:_load_config:93 | 🔧 [P3优化] 创建默认字段映射配置
2025-09-16 14:27:44.623 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-09-16 14:27:44.623 | INFO     | src.core.unified_mapping_service:__init__:52 | UnifiedMappingService 初始化完成
2025-09-16 14:27:44.623 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-09-16 14:27:44.623 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-09-16 14:27:44.623 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-09-16 14:27:44.623 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-16 14:27:44.623 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-09-16 14:27:44.623 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 77.9ms
2025-09-16 14:27:44.654 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-09-16 14:27:44.654 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-09-16 14:27:44.654 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-09-16 14:27:44.654 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-09-16 14:27:44.654 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-09-16 14:27:44.654 | INFO     | src.gui.prototype.prototype_main_window:__init__:3702 | 🚀 性能管理器已集成
2025-09-16 14:27:44.654 | INFO     | src.gui.prototype.prototype_main_window:__init__:3704 | ✅ 新架构集成成功！
2025-09-16 14:27:44.654 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3817 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-09-16 14:27:44.654 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3782 | ✅ 新架构事件监听器设置完成
2025-09-16 14:27:44.654 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-09-16 14:27:44.654 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-09-16 14:27:44.654 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-09-16 14:27:44.921 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2778 | 菜单栏创建完成
2025-09-16 14:27:44.921 | INFO     | src.gui.prototype.prototype_main_window:__init__:2753 | 菜单栏管理器初始化完成
2025-09-16 14:27:44.921 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-09-16 14:27:44.921 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5510 | 管理器设置完成，包含增强版表头管理器
2025-09-16 14:27:44.936 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5515 | 🔧 开始应用窗口级Material Design样式...
2025-09-16 14:27:44.936 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-09-16 14:27:44.936 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-09-16 14:27:44.936 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5522 | ✅ 窗口级样式应用成功
2025-09-16 14:27:44.936 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5563 | ✅ 响应式样式监听设置完成
2025-09-16 14:27:44.936 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:27:44.952 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:27:44.952 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:27:44.968 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-09-16 14:27:44.968 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-09-16 14:27:44.968 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:2357 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-09-16 14:27:44.983 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:2069 | 🏠 使用示例数据结构加载导航（首次启动或暂无数据时显示）
2025-09-16 14:27:44.983 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:2441 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-09-16 14:27:44.983 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:893 | 导航面板已重构：移除功能性导航，专注数据导航
2025-09-16 14:27:44.983 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:927 | 恢复导航状态: 0个展开项
2025-09-16 14:27:44.983 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月', '工资表 > 2025年 > 05月 > 全部在职人员']
2025-09-16 14:27:44.998 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:646 | 增强导航面板初始化完成
2025-09-16 14:27:44.999 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-09-16 14:27:45.002 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-09-16 14:27:45.013 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:2357 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-09-16 14:27:45.017 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:2441 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-09-16 14:27:45.031 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:893 | 导航面板已重构：移除功能性导航，专注数据导航
2025-09-16 14:27:45.037 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:646 | 增强导航面板初始化完成
2025-09-16 14:27:45.038 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1399 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-09-16 14:27:45.039 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1408 | 🔧 [P2修复] 未找到最新工资数据路径（可能是首次启动）
2025-09-16 14:27:45.311 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-09-16 14:27:45.313 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2126 | 🚨 [架构修复] 启用模型数据同步机制
2025-09-16 14:27:45.319 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1350 | 快捷键注册完成: 18/18 个
2025-09-16 14:27:45.320 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1793 | 拖拽排序管理器初始化完成
2025-09-16 14:27:45.347 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-09-16 14:27:45.347 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-09-16 14:27:45.356 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-09-16 14:27:45.358 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2179 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-09-16 14:27:45.359 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-09-16 14:27:45.361 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-09-16 14:27:45.362 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-16 14:27:45.370 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2231 | 🎯 [统一格式管理] 使用统一格式管理器
2025-09-16 14:27:45.382 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:370 | 🔧 [新架构] 成功加载 0 个字段映射
2025-09-16 14:27:45.385 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-09-16 14:27:45.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2278 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-09-16 14:27:45.387 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1537 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-09-16 14:27:45.388 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1538 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-09-16 14:27:45.389 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1539 | 🔧 [列宽保存修复] 配置文件存在: False
2025-09-16 14:27:45.390 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1540 | 🔧 [列宽保存修复] 父目录存在: True
2025-09-16 14:27:45.391 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1541 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-09-16 14:27:45.392 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2285 | 列宽管理器初始化完成
2025-09-16 14:27:45.393 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2412 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-09-16 14:27:45.405 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2299 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-09-16 14:27:45.406 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2358 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-09-16 14:27:45.407 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-09-16 14:27:45.408 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4653 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-09-16 14:27:45.420 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-09-16 14:27:45.427 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7393 | 方案A：安全设置列数: 0
2025-09-16 14:27:45.428 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2822 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-09-16 14:27:45.429 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5557 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-09-16 14:27:45.436 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-09-16 14:27:45.455 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2874 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-09-16 14:27:45.456 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2959 | 表格格式化完成: default_table, 类型: active_employees
2025-09-16 14:27:45.474 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-09-16 14:27:45.475 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-09-16 14:27:45.486 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-09-16 14:27:45.487 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-09-16 14:27:45.489 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:447 | 表格数据已设置: 0 行, 22 列
2025-09-16 14:27:45.491 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-09-16 14:27:45.493 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 0 行, 耗时: 79.9ms
2025-09-16 14:27:45.493 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 14:27:45.494 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 14:27:45.495 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1694 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-09-16 14:27:45.510 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 14:27:45.510 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2383 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-09-16 14:27:45.523 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-09-16 14:27:45.536 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-09-16 14:27:45.537 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-09-16 14:27:45.590 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:595 | 控制面板按钮信号连接完成
2025-09-16 14:27:45.669 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5472 | 快捷键设置完成
2025-09-16 14:27:45.670 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5429 | 主窗口UI设置完成。
2025-09-16 14:27:45.674 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5666 | 🔧 [全局排序] 全局排序开关连接成功
2025-09-16 14:27:45.675 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5698 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-09-16 14:27:45.678 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5722 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-09-16 14:27:45.678 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5725 | 信号连接设置完成
2025-09-16 14:27:45.680 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:27:45.696 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:27:45.702 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:27:45.703 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:7137 | 🔧 [P1-2修复] 发现 20 个表的配置
2025-09-16 14:27:45.769 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:7147 | ✅ [P1-2修复] 已加载字段映射信息，共20个表的映射
2025-09-16 14:27:45.782 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2358 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-09-16 14:27:45.793 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-09-16 14:27:45.793 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4653 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-09-16 14:27:45.795 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5557 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-09-16 14:27:45.797 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2959 | 表格格式化完成: default_table, 类型: active_employees
2025-09-16 14:27:45.799 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7393 | 方案A：安全设置列数: 22
2025-09-16 14:27:45.799 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-09-16 14:27:45.800 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-09-16 14:27:45.801 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-09-16 14:27:45.816 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-09-16 14:27:45.817 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 0 行, 耗时: 21.5ms
2025-09-16 14:27:45.818 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 14:27:45.820 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 14:27:45.821 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1694 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-09-16 14:27:45.823 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 14:27:45.829 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2383 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-09-16 14:27:45.830 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-09-16 14:27:45.831 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8941 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-09-16 14:27:45.839 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2358 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-09-16 14:27:45.847 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-09-16 14:27:45.848 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4653 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-09-16 14:27:45.849 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5557 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-09-16 14:27:45.851 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2959 | 表格格式化完成: default_table, 类型: active_employees
2025-09-16 14:27:45.852 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-09-16 14:27:45.852 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-09-16 14:27:45.859 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-09-16 14:27:45.860 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-09-16 14:27:45.861 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 0 行, 耗时: 11.7ms
2025-09-16 14:27:45.863 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 14:27:45.868 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 14:27:45.883 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1694 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-09-16 14:27:45.885 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 14:27:45.886 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2383 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-09-16 14:27:45.888 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8959 | 已显示标准空表格，表头数量: 22
2025-09-16 14:27:45.889 | INFO     | src.gui.prototype.prototype_main_window:__init__:3756 | 原型主窗口初始化完成
2025-09-16 14:27:45.922 | INFO     | src.gui.style_manager:update_responsive_style:227 | 响应式样式更新: medium -> small
2025-09-16 14:27:45.923 | INFO     | src.gui.style_manager:update_responsive_style:238 | 响应式样式更新成功: small
2025-09-16 14:27:45.926 | INFO     | src.gui.style_manager:update_responsive_style:227 | 响应式样式更新: small -> medium
2025-09-16 14:27:45.927 | INFO     | src.gui.style_manager:update_responsive_style:238 | 响应式样式更新成功: medium
2025-09-16 14:27:45.981 | INFO     | __main__:main:514 | 应用程序启动成功
2025-09-16 14:27:45.985 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-09-16 14:27:45.986 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1708 | 执行延迟的自动选择最新数据...
2025-09-16 14:27:46.002 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1731 | 延迟自动选择最新数据失败，可能没有可用数据
2025-09-16 14:27:46.003 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-09-16 14:27:46.087 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-09-16 14:27:46.165 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-09-16 14:27:46.165 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2131 | MainWorkspaceArea 响应式适配: sm
2025-09-16 14:27:46.666 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9843 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-09-16 14:27:46.666 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9753 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-09-16 14:27:46.666 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9767 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-09-16 14:27:46.666 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:10301 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-09-16 14:27:46.682 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9773 | 🔧 [P0-1] 智能显示亮度修复完成
2025-09-16 14:28:12.603 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:655 | 数据导入功能被触发，发出 import_requested 信号。
2025-09-16 14:28:12.603 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5958 | 首次创建UnifiedDataImportWindow
2025-09-16 14:28:12.619 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-09-16 14:28:12.619 | INFO     | src.modules.data_import.change_data_config_manager:__init__:55 | 异动表配置管理器重构版初始化完成，使用统一配置系统
2025-09-16 14:28:12.619 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:96 | 多Sheet导入器初始化完成
2025-09-16 14:28:12.619 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-09-16 14:28:12.619 | INFO     | src.modules.data_import.change_data_config_manager:__init__:55 | 异动表配置管理器重构版初始化完成，使用统一配置系统
2025-09-16 14:28:12.635 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:96 | 多Sheet导入器初始化完成
2025-09-16 14:28:12.744 | INFO     | src.modules.data_import.sheet_config_manager:__init__:128 | Sheet配置管理器初始化完成，配置目录: state\sheet_configs
2025-09-16 14:28:12.744 | INFO     | src.modules.data_import.sheet_config_manager:__init__:128 | Sheet配置管理器初始化完成，配置目录: state\sheet_configs
2025-09-16 14:28:12.791 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-09-16 14:28:12.791 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-09-16 14:28:12.791 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-09-16 14:28:12.791 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-09-16 14:28:12.791 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-16 14:28:12.791 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-09-16 14:28:12.791 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-09-16 14:28:12.806 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-09-16 14:28:12.806 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-16 14:28:12.806 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-09-16 14:28:12.806 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 15.7ms
2025-09-16 14:28:12.901 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-09-16 14:28:12.901 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:557 | 注册规则类型: number
2025-09-16 14:28:12.901 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:557 | 注册规则类型: string
2025-09-16 14:28:12.901 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:557 | 注册规则类型: date
2025-09-16 14:28:12.916 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:557 | 注册规则类型: code
2025-09-16 14:28:12.916 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:557 | 注册规则类型: custom
2025-09-16 14:28:12.916 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:568 | 🔧 [方案一实施] 注册内置字段类型: salary_float - 工资金额
2025-09-16 14:28:12.916 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:568 | 🔧 [方案一实施] 注册内置字段类型: employee_id_string - 工号
2025-09-16 14:28:12.916 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:568 | 🔧 [方案一实施] 注册内置字段类型: name_string - 姓名
2025-09-16 14:28:12.916 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:568 | 🔧 [方案一实施] 注册内置字段类型: date_string - 日期
2025-09-16 14:28:12.916 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:568 | 🔧 [方案一实施] 注册内置字段类型: id_number_string - 身份证号
2025-09-16 14:28:12.916 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:568 | 🔧 [方案一实施] 注册内置字段类型: code_string - 代码
2025-09-16 14:28:12.916 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:568 | 🔧 [方案一实施] 注册内置字段类型: float - 浮点数
2025-09-16 14:28:12.916 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:568 | 🔧 [方案一实施] 注册内置字段类型: integer - 整数
2025-09-16 14:28:12.916 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:568 | 🔧 [方案一实施] 注册内置字段类型: text_string - 文本字符串
2025-09-16 14:28:12.916 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:568 | 🔧 [方案一实施] 注册内置字段类型: personnel_category_code - 人员类别代码
2025-09-16 14:28:12.916 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-09-16 14:28:12.916 | INFO     | src.modules.data_import.formatting_engine:reload_custom_field_types:937 | 🔧 [方案一实施] 重新加载了 0 个自定义字段类型
2025-09-16 14:28:13.018 | INFO     | src.gui.prototype.prototype_main_window:_get_suggested_target_path:8764 | 检测到当前在工资表TAB，生成工资表默认路径
2025-09-16 14:28:13.019 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5969 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 09月 > 全部在职人员。打开导入对话框。
2025-09-16 14:29:08.135 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-09-16 14:29:08.137 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-16 14:29:08.142 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-16 14:29:08.142 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:29:08.278 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-09-16 14:29:08.283 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:29:08.287 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-16 14:29:08.287 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-09-16 14:29:08.319 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-16 14:29:08.321 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-09-16 14:29:08.326 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-09-16 14:29:08.328 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:29:08.433 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:29:08.435 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-09-16 14:29:08.440 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:29:08.552 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:29:08.554 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-09-16 14:29:08.558 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:29:08.658 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:29:08.661 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-16 14:29:11.944 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet '离休人员工资表' 创建默认配置
2025-09-16 14:29:11.944 | INFO     | src.modules.data_import.formatting_engine:clear_temporary_field_types:978 | 🔧 [方案一实施] 已清理 0 个临时字段类型
2025-09-16 14:29:11.944 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet '离休人员工资表' 创建默认配置
2025-09-16 14:29:11.960 | INFO     | src.modules.data_import.config_template_manager:_init_builtin_templates:184 | 初始化了 4 个内置模板
2025-09-16 14:29:11.960 | INFO     | src.modules.data_import.config_template_manager:__init__:100 | 配置模板管理器初始化完成，模板目录: state\config_templates
2025-09-16 14:29:11.960 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 4 个模板
2025-09-16 14:29:11.975 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: 离休人员工资表
2025-09-16 14:29:11.975 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-16 14:29:11.975 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-16 14:29:11.975 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:29:12.069 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-09-16 14:29:12.069 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:29:12.069 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-16 14:29:12.069 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 16列
2025-09-16 14:29:12.084 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 16列
2025-09-16 14:29:12.178 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:29:12.288 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:29:12.288 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-16 14:29:12.288 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-16 14:29:12.288 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-09-16 14:29:17.688 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-16 14:29:17.704 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-16 14:29:17.704 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:29:17.798 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-09-16 14:29:17.798 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:29:17.798 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-16 14:29:17.798 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-09-16 14:29:17.798 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-16 14:29:17.815 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-09-16 14:29:17.815 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-09-16 14:30:22.412 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-09-16 14:30:22.412 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-09-16 14:30:22.412 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-09-16 14:30:22.412 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-16 14:30:22.412 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-09-16 14:30:22.412 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-09-16 14:30:22.412 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-09-16 14:30:22.412 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-16 14:30:22.412 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-09-16 14:30:22.430 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 18.1ms
2025-09-16 14:30:22.430 | INFO     | src.modules.data_import.multi_sheet_importer:set_config_sync_manager:110 | 🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter
2025-09-16 14:30:22.430 | INFO     | src.gui.workers.data_import_worker:setup_import:64 | 导入工作线程配置完成: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-16 14:30:22.442 | INFO     | src.gui.workers.data_import_worker:run:112 | 开始异步数据导入
2025-09-16 14:30:22.442 | INFO     | src.modules.data_import.multi_sheet_importer:set_config_sync_manager:110 | 🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter
2025-09-16 14:30:22.442 | INFO     | src.gui.workers.data_import_worker:run:142 | 🔧 [P3修复] ConfigSyncManager注入成功
2025-09-16 14:30:22.442 | INFO     | src.modules.data_import.multi_sheet_importer:set_stop_checker:115 | 🔧 [可取消] StopChecker 已设置
2025-09-16 14:30:22.442 | INFO     | src.modules.data_import.multi_sheet_importer:set_progress_callback:127 | 🔧 [P1修复] 进度回调已设置到MultiSheetImporter
2025-09-16 14:30:22.458 | INFO     | src.gui.workers.data_import_worker:run:164 | 🔧 [P1修复] 进度回调已注入到MultiSheetImporter
2025-09-16 14:30:22.473 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:286 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-16 14:30:22.473 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-16 14:30:22.692 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-09-16 14:30:22.692 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:327 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']; 实际将导入 4 个: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-09-16 14:30:22.692 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:335 | effective_strategy=separate_tables, target_path=工资表
2025-09-16 14:30:22.692 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-16 14:30:22.692 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:30:22.786 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-09-16 14:30:22.786 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:30:22.802 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-16 14:30:22.802 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-09-16 14:30:22.802 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-16 14:30:22.802 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-09-16 14:30:22.802 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-09-16 14:30:22.802 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:812 | 工作表 离休人员工资表 使用智能默认配置
2025-09-16 14:30:22.802 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:1146 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-09-16 14:30:22.802 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 retired_employees 生成了 21 个字段映射
2025-09-16 14:30:22.802 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:875 | 使用专用模板 retired_employees 生成字段映射: 21 个字段
2025-09-16 14:30:22.817 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:922 | 为表 salary_data_2025_05_retired_employees 生成标准化字段映射: 27 个字段
2025-09-16 14:30:22.817 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:943 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-09-16 14:30:22.833 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:604 | 🔧 [P2修复] 检测到工资表导入: 工资表
2025-09-16 14:30:22.833 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:608 | Sheet '离休人员工资表' 检测到模板类型: retired_employees
2025-09-16 14:30:22.833 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:665 | [P0] 使用英文名生成表名: salary_data_2025_05_retired | source=ui_selection
2025-09-16 14:30:22.833 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:1150 | 成功创建表: salary_data_2025_05_retired
2025-09-16 14:30:22.848 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_index:1188 | 成功创建索引: salary_data_2025_05_retired_idx_salary_employee_salary_data_2025_05_retired
2025-09-16 14:30:22.848 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_index:1188 | 成功创建索引: salary_data_2025_05_retired_idx_salary_month_salary_data_2025_05_retired
2025-09-16 14:30:22.848 | INFO     | src.modules.data_storage.dynamic_table_manager:create_salary_data_table:573 | ✅ [表名一致性修复] 工资数据表创建成功: salary_data_2025_05_retired (动态模式)
2025-09-16 14:30:22.848 | INFO     | src.modules.data_storage.dynamic_table_manager:_generate_display_name_for_table:1320 | 🔧 [P0修复] 使用默认映射: salary_data_2025_05_retired -> 离休人员
2025-09-16 14:30:22.864 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:724 | 从列 '人员代码' 生成 employee_id
2025-09-16 14:30:22.880 | INFO     | src.modules.data_storage.dynamic_table_manager:_find_best_matching_table_config_corrected:3267 | 开始为新表 'salary_data_2025_05_retired' 寻找最佳匹配的字段映射配置...
2025-09-16 14:30:22.880 | INFO     | src.modules.system_config.config_manager:get_all_table_configs:680 | 已加载字段映射配置: 0 项（表级+模板级）
2025-09-16 14:30:22.880 | WARNING  | src.modules.data_storage.dynamic_table_manager:_find_best_matching_table_config_corrected:3271 | 系统中没有任何已保存的表配置，无法进行匹配。
2025-09-16 14:30:22.880 | WARNING  | src.modules.data_storage.dynamic_table_manager:_get_dynamic_field_mappings:3234 | 未找到表 salary_data_2025_05_retired 的专用映射，使用基础映射
2025-09-16 14:30:22.895 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2730 | [FIX] [修复标识] 导入字段映射加载完成: 30 个映射规则
2025-09-16 14:30:22.911 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2737 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time', 'employee_id']
2025-09-16 14:30:22.911 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2746 | [FIX] [修复标识] 导入列名映射成功: 19 个字段已映射
2025-09-16 14:30:22.911 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 增发一次
性生活补贴 设置默认值
2025-09-16 14:30:22.927 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 结余
津贴 设置默认值
2025-09-16 14:30:22.927 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 姓名 设置默认值
2025-09-16 14:30:22.927 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 护理费 设置默认值
2025-09-16 14:30:22.927 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 人员代码 设置默认值
2025-09-16 14:30:22.927 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 部门名称 设置默认值
2025-09-16 14:30:22.927 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 序号 设置默认值
2025-09-16 14:30:22.942 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 合计 设置默认值
2025-09-16 14:30:22.942 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 物业
补贴 设置默认值
2025-09-16 14:30:22.942 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 住房
补贴 设置默认值
2025-09-16 14:30:22.942 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 补发 设置默认值
2025-09-16 14:30:22.942 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 离休
补贴 设置默认值
2025-09-16 14:30:22.958 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 借支 设置默认值
2025-09-16 14:30:22.958 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 基本
离休费 设置默认值
2025-09-16 14:30:22.958 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 备注 设置默认值
2025-09-16 14:30:22.958 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 生活
补贴 设置默认值
2025-09-16 14:30:22.973 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:3015 | [TX] 验证成功：表 salary_data_2025_05_retired 中有 2 条记录
2025-09-16 14:30:22.973 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:3025 | 成功向表 salary_data_2025_05_retired 保存 2 条数据。
2025-09-16 14:30:22.973 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:30:23.083 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:30:23.083 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-09-16 14:30:23.099 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-16 14:30:23.099 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-09-16 14:30:23.099 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:812 | 工作表 退休人员工资表 使用智能默认配置
2025-09-16 14:30:23.099 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:1146 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-09-16 14:30:23.099 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 pension_employees 生成了 32 个字段映射
2025-09-16 14:30:23.099 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:875 | 使用专用模板 pension_employees 生成字段映射: 32 个字段
2025-09-16 14:30:23.099 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:922 | 为表 salary_data_2025_05_pension_employees 生成标准化字段映射: 38 个字段
2025-09-16 14:30:23.099 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:943 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-09-16 14:30:23.114 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:604 | 🔧 [P2修复] 检测到工资表导入: 工资表
2025-09-16 14:30:23.114 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:608 | Sheet '退休人员工资表' 检测到模板类型: pension_employees
2025-09-16 14:30:23.114 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:665 | [P0] 使用英文名生成表名: salary_data_2025_05_pension | source=ui_selection
2025-09-16 14:30:23.129 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:1150 | 成功创建表: salary_data_2025_05_pension
2025-09-16 14:30:23.129 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_index:1188 | 成功创建索引: salary_data_2025_05_pension_idx_salary_employee_salary_data_2025_05_pension
2025-09-16 14:30:23.129 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_index:1188 | 成功创建索引: salary_data_2025_05_pension_idx_salary_month_salary_data_2025_05_pension
2025-09-16 14:30:23.129 | INFO     | src.modules.data_storage.dynamic_table_manager:create_salary_data_table:573 | ✅ [表名一致性修复] 工资数据表创建成功: salary_data_2025_05_pension (动态模式)
2025-09-16 14:30:23.129 | INFO     | src.modules.data_storage.dynamic_table_manager:_generate_display_name_for_table:1320 | 🔧 [P0修复] 使用默认映射: salary_data_2025_05_pension -> 退休人员
2025-09-16 14:30:23.145 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:724 | 从列 '人员代码' 生成 employee_id
2025-09-16 14:30:23.145 | INFO     | src.modules.data_storage.dynamic_table_manager:_find_best_matching_table_config_corrected:3267 | 开始为新表 'salary_data_2025_05_pension' 寻找最佳匹配的字段映射配置...
2025-09-16 14:30:23.145 | INFO     | src.modules.system_config.config_manager:get_all_table_configs:680 | 已加载字段映射配置: 0 项（表级+模板级）
2025-09-16 14:30:23.145 | WARNING  | src.modules.data_storage.dynamic_table_manager:_find_best_matching_table_config_corrected:3271 | 系统中没有任何已保存的表配置，无法进行匹配。
2025-09-16 14:30:23.145 | WARNING  | src.modules.data_storage.dynamic_table_manager:_get_dynamic_field_mappings:3234 | 未找到表 salary_data_2025_05_pension 的专用映射，使用基础映射
2025-09-16 14:30:23.161 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2730 | [FIX] [修复标识] 导入字段映射加载完成: 23 个映射规则
2025-09-16 14:30:23.161 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2737 | [FIX] [修复标识] 保留未映射列: ['人员类别代码', '基本退休费', '离退休生活补贴', '增资预付', '2016待遇调整', '2017待遇调整', '2018待遇调整', '2019待遇调整', '2020待遇调整', '2021待遇调整', '2022待遇调整', '2023待遇调整', '公积', '保险扣款', 'data_source', 'import_time', 'employee_id']
2025-09-16 14:30:23.161 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2746 | [FIX] [修复标识] 导入列名映射成功: 30 个字段已映射
2025-09-16 14:30:23.176 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 应发工资 设置默认值
2025-09-16 14:30:23.176 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 结余津贴 设置默认值
2025-09-16 14:30:23.176 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 住房补贴 设置默认值
2025-09-16 14:30:23.176 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 物业补贴 设置默认值
2025-09-16 14:30:23.176 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 姓名 设置默认值
2025-09-16 14:30:23.176 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 护理费 设置默认值
2025-09-16 14:30:23.176 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 人员代码 设置默认值
2025-09-16 14:30:23.192 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 部门名称 设置默认值
2025-09-16 14:30:23.192 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 序号 设置默认值
2025-09-16 14:30:23.192 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 补发 设置默认值
2025-09-16 14:30:23.192 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 借支 设置默认值
2025-09-16 14:30:23.192 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 备注 设置默认值
2025-09-16 14:30:23.192 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 津贴 设置默认值
2025-09-16 14:30:23.208 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:3015 | [TX] 验证成功：表 salary_data_2025_05_pension 中有 13 条记录
2025-09-16 14:30:23.208 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:3025 | 成功向表 salary_data_2025_05_pension 保存 13 条数据。
2025-09-16 14:30:23.208 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:30:23.333 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:30:23.333 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-09-16 14:30:23.333 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-16 14:30:23.333 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-09-16 14:30:23.348 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:812 | 工作表 全部在职人员工资表 使用智能默认配置
2025-09-16 14:30:23.348 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:1146 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-09-16 14:30:23.348 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 active_employees 生成了 28 个字段映射
2025-09-16 14:30:23.348 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:875 | 使用专用模板 active_employees 生成字段映射: 28 个字段
2025-09-16 14:30:23.348 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:922 | 为表 salary_data_2025_05_active_employees 生成标准化字段映射: 34 个字段
2025-09-16 14:30:23.364 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:943 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-09-16 14:30:23.364 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:604 | 🔧 [P2修复] 检测到工资表导入: 工资表
2025-09-16 14:30:23.364 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:608 | Sheet '全部在职人员工资表' 检测到模板类型: active_employees
2025-09-16 14:30:23.364 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:665 | [P0] 使用英文名生成表名: salary_data_2025_05_all_active | source=ui_selection
2025-09-16 14:30:23.380 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:1150 | 成功创建表: salary_data_2025_05_all_active
2025-09-16 14:30:23.380 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_index:1188 | 成功创建索引: salary_data_2025_05_all_active_idx_salary_employee_salary_data_2025_05_all_active
2025-09-16 14:30:23.395 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_index:1188 | 成功创建索引: salary_data_2025_05_all_active_idx_salary_month_salary_data_2025_05_all_active
2025-09-16 14:30:23.395 | INFO     | src.modules.data_storage.dynamic_table_manager:create_salary_data_table:573 | ✅ [表名一致性修复] 工资数据表创建成功: salary_data_2025_05_all_active (动态模式)
2025-09-16 14:30:23.395 | INFO     | src.modules.data_storage.dynamic_table_manager:_generate_display_name_for_table:1320 | 🔧 [P0修复] 使用默认映射: salary_data_2025_05_all_active -> 全部在职人员
2025-09-16 14:30:23.395 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:724 | 从列 '工号' 生成 employee_id
2025-09-16 14:30:23.411 | INFO     | src.modules.data_storage.dynamic_table_manager:_find_best_matching_table_config_corrected:3267 | 开始为新表 'salary_data_2025_05_all_active' 寻找最佳匹配的字段映射配置...
2025-09-16 14:30:23.411 | INFO     | src.modules.system_config.config_manager:get_all_table_configs:680 | 已加载字段映射配置: 0 项（表级+模板级）
2025-09-16 14:30:23.411 | WARNING  | src.modules.data_storage.dynamic_table_manager:_find_best_matching_table_config_corrected:3271 | 系统中没有任何已保存的表配置，无法进行匹配。
2025-09-16 14:30:23.411 | WARNING  | src.modules.data_storage.dynamic_table_manager:_get_dynamic_field_mappings:3234 | 未找到表 salary_data_2025_05_all_active 的专用映射，使用基础映射
2025-09-16 14:30:23.411 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2730 | [FIX] [修复标识] 导入字段映射加载完成: 21 个映射规则
2025-09-16 14:30:23.411 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2737 | [FIX] [修复标识] 保留未映射列: ['人员类别代码', '人员类别', '2025年岗位工资', '2025年薪级工资', '2025年基础性绩效', '卫生费', '交通补贴', '车补', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '代扣代存养老保险', 'data_source', 'import_time', 'employee_id']
2025-09-16 14:30:23.429 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2746 | [FIX] [修复标识] 导入列名映射成功: 26 个字段已映射
2025-09-16 14:30:23.429 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 应发工资 设置默认值
2025-09-16 14:30:23.442 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 结余津贴 设置默认值
2025-09-16 14:30:23.442 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 住房补贴 设置默认值
2025-09-16 14:30:23.442 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 物业补贴 设置默认值
2025-09-16 14:30:23.442 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 姓名 设置默认值
2025-09-16 14:30:23.442 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 工号 设置默认值
2025-09-16 14:30:23.442 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 部门名称 设置默认值
2025-09-16 14:30:23.442 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 序号 设置默认值
2025-09-16 14:30:23.442 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 补发 设置默认值
2025-09-16 14:30:23.442 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 借支 设置默认值
2025-09-16 14:30:23.442 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 津贴 设置默认值
2025-09-16 14:30:23.504 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:3015 | [TX] 验证成功：表 salary_data_2025_05_all_active 中有 1396 条记录
2025-09-16 14:30:23.504 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:3025 | 成功向表 salary_data_2025_05_all_active 保存 1396 条数据。
2025-09-16 14:30:23.504 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-16 14:30:23.504 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:30:23.599 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:30:23.615 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-16 14:30:23.615 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-16 14:30:23.615 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-09-16 14:30:23.615 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:812 | 工作表 A岗职工 使用智能默认配置
2025-09-16 14:30:23.615 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:1146 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-09-16 14:30:23.615 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 a_grade_employees 生成了 26 个字段映射
2025-09-16 14:30:23.615 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:875 | 使用专用模板 a_grade_employees 生成字段映射: 26 个字段
2025-09-16 14:30:23.630 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:922 | 为表 salary_data_2025_05_a_grade_employees 生成标准化字段映射: 32 个字段
2025-09-16 14:30:23.630 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:943 | Sheet A岗职工 数据处理完成: 62 行
2025-09-16 14:30:23.630 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:604 | 🔧 [P2修复] 检测到工资表导入: 工资表
2025-09-16 14:30:23.630 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:608 | Sheet 'A岗职工' 检测到模板类型: a_grade_employees
2025-09-16 14:30:23.630 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:665 | [P0] 使用英文名生成表名: salary_data_2025_05_a_grade | source=ui_selection
2025-09-16 14:30:23.645 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:1150 | 成功创建表: salary_data_2025_05_a_grade
2025-09-16 14:30:23.645 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_index:1188 | 成功创建索引: salary_data_2025_05_a_grade_idx_salary_employee_salary_data_2025_05_a_grade
2025-09-16 14:30:23.645 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_index:1188 | 成功创建索引: salary_data_2025_05_a_grade_idx_salary_month_salary_data_2025_05_a_grade
2025-09-16 14:30:23.645 | INFO     | src.modules.data_storage.dynamic_table_manager:create_salary_data_table:573 | ✅ [表名一致性修复] 工资数据表创建成功: salary_data_2025_05_a_grade (动态模式)
2025-09-16 14:30:23.645 | INFO     | src.modules.data_storage.dynamic_table_manager:_generate_display_name_for_table:1320 | 🔧 [P0修复] 使用默认映射: salary_data_2025_05_a_grade -> A岗职工
2025-09-16 14:30:23.661 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:724 | 从列 '工号' 生成 employee_id
2025-09-16 14:30:23.661 | INFO     | src.modules.data_storage.dynamic_table_manager:_find_best_matching_table_config_corrected:3267 | 开始为新表 'salary_data_2025_05_a_grade' 寻找最佳匹配的字段映射配置...
2025-09-16 14:30:23.661 | INFO     | src.modules.system_config.config_manager:get_all_table_configs:680 | 已加载字段映射配置: 0 项（表级+模板级）
2025-09-16 14:30:23.661 | WARNING  | src.modules.data_storage.dynamic_table_manager:_find_best_matching_table_config_corrected:3271 | 系统中没有任何已保存的表配置，无法进行匹配。
2025-09-16 14:30:23.661 | WARNING  | src.modules.data_storage.dynamic_table_manager:_get_dynamic_field_mappings:3234 | 未找到表 salary_data_2025_05_a_grade 的专用映射，使用基础映射
2025-09-16 14:30:23.679 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2730 | [FIX] [修复标识] 导入字段映射加载完成: 21 个映射规则
2025-09-16 14:30:23.679 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2737 | [FIX] [修复标识] 保留未映射列: ['人员类别', '人员类别代码', '2025年岗位工资', '2025年校龄工资', '2025年基础性绩效', '卫生费', '2025年生活补贴', '车补', '2025年奖励性绩效预发', '2025公积金', '保险扣款', '代扣代存养老保险', 'data_source', 'import_time', 'employee_id']
2025-09-16 14:30:23.679 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2746 | [FIX] [修复标识] 导入列名映射成功: 24 个字段已映射
2025-09-16 14:30:23.692 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 应发工资 设置默认值
2025-09-16 14:30:23.692 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 结余津贴 设置默认值
2025-09-16 14:30:23.692 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 姓名 设置默认值
2025-09-16 14:30:23.692 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 工号 设置默认值
2025-09-16 14:30:23.692 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 部门名称 设置默认值
2025-09-16 14:30:23.692 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 序号 设置默认值
2025-09-16 14:30:23.692 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 补发 设置默认值
2025-09-16 14:30:23.692 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 借支 设置默认值
2025-09-16 14:30:23.692 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2956 | [FIX] [修复标识] 字段 津贴 设置默认值
2025-09-16 14:30:23.708 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:3015 | [TX] 验证成功：表 salary_data_2025_05_a_grade 中有 62 条记录
2025-09-16 14:30:23.708 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:3025 | 成功向表 salary_data_2025_05_a_grade 保存 62 条数据。
2025-09-16 14:30:23.708 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:359 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_05_retired 保存 2 条数据。', 'table_name': 'salary_data_2025_05_retired', 'records': 2, 'filtered_empty_employee_id': 0}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_05_pension 保存 13 条数据。', 'table_name': 'salary_data_2025_05_pension', 'records': 13, 'filtered_empty_employee_id': 0}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_05_all_active 保存 1396 条数据。', 'table_name': 'salary_data_2025_05_all_active', 'records': 1396, 'filtered_empty_employee_id': 0}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_05_a_grade 保存 62 条数据。', 'table_name': 'salary_data_2025_05_a_grade', 'records': 62, 'filtered_empty_employee_id': 0}}, 'total_records': 1473, 'year': 2025, 'month': 5, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-09-16 14:30:23.708 | INFO     | src.gui.workers.data_import_worker:run:199 | 🔧 [P2修复] 异步数据导入成功完成，已发送完成信号
2025-09-16 14:30:25.155 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3874 | 📥[data_event] received | table= | request_id=None
2025-09-16 14:30:25.155 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3906 | 数据内容: 0行 x 0列
2025-09-16 14:30:25.155 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3937 | 🔧 [数据更新] 接收到操作的数据更新事件: , 0行
2025-09-16 14:30:25.155 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4077 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-09-16 14:30:25.163 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:6094 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_05_retired 保存 2 条数据。', 'table_name': 'salary_data_2025_05_retired', 'records': 2, 'filtered_empty_employee_id': 0}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_05_pension 保存 13 条数据。', 'table_name': 'salary_data_2025_05_pension', 'records': 13, 'filtered_empty_employee_id': 0}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_05_all_active 保存 1396 条数据。', 'table_name': 'salary_data_2025_05_all_active', 'records': 1396, 'filtered_empty_employee_id': 0}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_05_a_grade 保存 62 条数据。', 'table_name': 'salary_data_2025_05_a_grade', 'records': 62, 'filtered_empty_employee_id': 0}}, 'total_records': 1473, 'year': 2025, 'month': 5, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'message': '导入完成！共导入 1473 条记录到 4 个表，涉及 4 个Sheet', 'import_mode': 'separate_tables', 'target_path': '工资表', 'source_file': '2025年5月份正式工工资（报财务)  最终版.xls'}
2025-09-16 14:30:25.164 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:6108 | 导入模式: separate_tables, 目标路径: '工资表'
2025-09-16 14:30:25.165 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:6126 | 接收到导入数据, 来源: 2025年5月份正式工工资（报财务)  最终版.xls, 目标路径: 工资表
2025-09-16 14:30:25.166 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:2084 | 🔧 [P2-2] 开始增强版导航数据刷新...
2025-09-16 14:30:25.167 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月']
2025-09-16 14:30:25.179 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7713 | 导航变化: 工资表 > 2025年 > 05月 > 全部在职人员
2025-09-16 14:30:25.180 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10932 | 🚫 [用户要求] 表格切换不显示加载条:  -> None
2025-09-16 14:30:25.181 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10948 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-09-16 14:30:25.182 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8724 | 🔧 [表名生成] 命中最近导入映射: ('工资表', '2025', '05', '全部在职人员') -> salary_data_2025_05_all_active
2025-09-16 14:30:25.183 | INFO     | src.gui.prototype.prototype_main_window:_process_navigation_change:7775 | 🔧 [P0-紧急修复] 导航变化时更新状态栏: 工资表 > 2025年 > 05月 > 全部在职人员
2025-09-16 14:30:25.184 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:384 | 已清理表 salary_data_2025_05_all_active 的缓存
2025-09-16 14:30:25.193 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11620 | 已注册 4 个表格到表头管理器
2025-09-16 14:30:25.194 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1197 | 🔧 [P1-3] 开始智能表头重影检测，共 4 个表格
2025-09-16 14:30:25.196 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1270 | 🔧 [P1-3] 智能表头重影检测完成，耗时 2.03ms
2025-09-16 14:30:25.197 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1271 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-09-16 14:30:25.198 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7934 | 🆕 使用新架构加载数据: salary_data_2025_05_all_active（通过事件系统）
2025-09-16 14:30:25.201 | INFO     | src.services.table_data_service:load_table_data:377 | 加载表格数据: 页码: 1
2025-09-16 14:30:25.207 | INFO     | src.core.unified_data_request_manager:request_table_data:237 | 开始处理数据请求
2025-09-16 14:30:25.209 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:899 | 正在从表 salary_data_2025_05_all_active 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-09-16 14:30:25.219 | INFO     | src.core.sort_cache_manager:__init__:93 | SortCacheManager 初始化完成
2025-09-16 14:30:25.223 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:1019 | 成功从表 salary_data_2025_05_all_active 获取第1页数据（含排序）: 50 行，总计1396行
2025-09-16 14:30:25.225 | INFO     | src.core.unified_data_request_manager:request_table_data:287 | 数据请求处理完成: 字段=31, 行数=50, 耗时=18.6ms
2025-09-16 14:30:25.227 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-09-16 14:30:25.227 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-09-16 14:30:25.228 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-16 14:30:25.233 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-09-16 14:30:25.234 | INFO     | src.modules.format_management.change_data_analyzer:__init__:78 | 异动表智能分析器初始化完成
2025-09-16 14:30:25.235 | INFO     | src.modules.format_management.format_renderer:__init__:94 | 🎨 [格式渲染] 格式渲染器初始化完成（已集成智能分析器）
2025-09-16 14:30:25.236 | INFO     | src.modules.format_management.unified_format_manager:_setup_event_listeners:885 | 🎯 [事件驱动] 事件监听器设置完成
2025-09-16 14:30:25.238 | INFO     | src.core.unified_state_manager:update_global_state:350 | 全局状态已更新: StateChangeType.DATA_RELOADED
2025-09-16 14:30:25.241 | INFO     | src.modules.format_management.unified_format_manager:_sync_to_state_manager:900 | 🎯 [统一状态管理] 状态同步完成
2025-09-16 14:30:25.247 | INFO     | src.modules.format_management.format_config:load_config:347 | 🔧 [格式配置] 配置文件不存在，创建默认配置
2025-09-16 14:30:25.249 | INFO     | src.modules.format_management.format_config:save_config:397 | 🔧 [格式配置] 配置文件保存成功
2025-09-16 14:30:25.251 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:30:25.251 | INFO     | src.modules.format_management.unified_format_manager:_initialize_system:150 | 🎯 [统一格式管理] 系统初始化完成
2025-09-16 14:30:25.253 | INFO     | src.modules.format_management.unified_format_manager:__init__:134 | 🎯 [统一格式管理] 统一格式管理器初始化完成（新架构版本）
2025-09-16 14:30:25.254 | INFO     | src.modules.format_management.unified_format_manager:__init__:1083 | 🔧 [单例优化] 单例统一格式管理器初始化完成
2025-09-16 14:30:25.254 | INFO     | src.services.table_data_service:load_table_data:476 | [新架构] 使用统一格式管理器（单例优化）
2025-09-16 14:30:25.265 | INFO     | src.core.error_recovery_manager:__init__:74 | 🔧 [P2-ErrorRecovery] 错误恢复管理器初始化完成
2025-09-16 14:30:25.265 | INFO     | src.modules.format_management.format_renderer:render_dataframe:127 | 🎯 [格式渲染] 隐藏字段配置: ['created_at', 'updated_at', 'id', 'sequence_number']
2025-09-16 14:30:25.267 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | 🎯 [格式渲染] 已隐藏字段: ['created_at', 'updated_at', 'id']
2025-09-16 14:30:25.269 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-09-16 14:30:25.282 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-09-16 14:30:25.283 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-09-16 14:30:25.288 | INFO     | src.modules.format_management.field_registry:get_display_fields:1582 | 🔧 [P0-2修复] display_order转换完成: 22个字段，原始字段数: 22
2025-09-16 14:30:25.288 | INFO     | src.modules.format_management.format_renderer:render_dataframe:186 | 🔧 [DEBUG] table_type=a_grade_employees, display_fields=22个字段
2025-09-16 14:30:25.290 | INFO     | src.modules.format_management.format_renderer:render_dataframe:244 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 19/22 个字段
2025-09-16 14:30:25.291 | INFO     | src.modules.format_management.format_renderer:render_dataframe:252 | 🔧 [P0-关键修复] 保留未配置的业务字段: 9个
2025-09-16 14:30:25.298 | INFO     | src.modules.format_management.format_renderer:render_dataframe:257 | 🔧 [P0修复] 字段重排完成: 原始28个 -> 最终28个字段
2025-09-16 14:30:25.299 | INFO     | src.modules.format_management.format_renderer:render_dataframe:273 | 🎨 [格式渲染] DataFrame格式化完成: a_grade_employees, 行数: 50, 列数: 28
2025-09-16 14:30:25.300 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: a_grade_employees, 行数: 50, 列数: 28
2025-09-16 14:30:25.302 | INFO     | src.core.unified_state_manager:update_table_state:233 | 表状态已更新: salary_data_2025_05_all_active, 变更类型: None
2025-09-16 14:30:25.303 | INFO     | src.services.table_data_service:load_table_data:528 | [修复数据发布] 数据加载成功，发布更新事件: 50行
2025-09-16 14:30:25.304 | INFO     | src.services.table_data_service:load_table_data:532 | 📨[data_event] publish | table=salary_data_2025_05_all_active | rows=50 | page=1 | size=50 | total=1396 | req_type=RequestType.INITIAL_LOAD | request_id=SV-1758004225303-6eb904e4
2025-09-16 14:30:25.309 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3874 | 📥[data_event] received | table=salary_data_2025_05_all_active | request_id=SV-1758004225303-6eb904e4
2025-09-16 14:30:25.310 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3906 | 数据内容: 50行 x 28列
2025-09-16 14:30:25.311 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3937 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_05_all_active, 50行
2025-09-16 14:30:25.312 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3952 | 🔧 [P0-修复] 排序数据处理前: 28列 - ['工号', '姓名', '部门名称', '人员类别', '人员类别代码']...
2025-09-16 14:30:25.314 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:30:25.315 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:30:25.318 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔧 [P0-修复] 字段映射后: 28列
2025-09-16 14:30:25.324 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:7617 | 🔧 [P0-修复] 系统字段过滤: 隐藏 5 个字段 - ['年份', '月份', '序号', 'data_source', 'import_time']
2025-09-16 14:30:25.325 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3956 | 🔧 [P0-修复] 系统字段过滤后: 25列
2025-09-16 14:30:25.326 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:30:25.332 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:30:25.335 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:30:25.336 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7670 | 表 salary_data_2025_05_all_active 没有用户偏好设置，显示所有可见字段
2025-09-16 14:30:25.348 | INFO     | src.gui.prototype.prototype_main_window:set_data:922 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-09-16 14:30:25.349 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:30:25.351 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:30:25.356 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:30:25.366 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:30:25.367 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:30:25.372 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7670 | 表 salary_data_2025_05_all_active 没有用户偏好设置，显示所有可见字段
2025-09-16 14:30:25.377 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_05_all_active
2025-09-16 14:30:25.378 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引1, 名称'工号'
2025-09-16 14:30:25.379 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引2, 名称'工号'
2025-09-16 14:30:25.383 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引3, 名称'工号'
2025-09-16 14:30:25.384 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引4, 名称'工号'
2025-09-16 14:30:25.385 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引5, 名称'工号'
2025-09-16 14:30:25.386 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引6, 名称'工号'
2025-09-16 14:30:25.387 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引7, 名称'工号'
2025-09-16 14:30:25.388 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引24, 名称'工号'
2025-09-16 14:30:25.389 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引25, 名称'工号'
2025-09-16 14:30:25.389 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引26, 名称'工号'
2025-09-16 14:30:25.390 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引27, 名称'工号'
2025-09-16 14:30:25.391 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引28, 名称'工号'
2025-09-16 14:30:25.392 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引29, 名称'工号'
2025-09-16 14:30:25.399 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引30, 名称'工号'
2025-09-16 14:30:25.400 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引31, 名称'工号'
2025-09-16 14:30:25.401 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4899 | 🔧 [方案A2] 表头去重完成: 37 -> 22
2025-09-16 14:30:25.401 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4902 | 🔧 [方案A2] 移除的重复项: 工号(索引1), 工号(索引2), 工号(索引3), 工号(索引4), 工号(索引5), 工号(索引6), 工号(索引7), 工号(索引24), 工号(索引25), 工号(索引26), 工号(索引27), 工号(索引28), 工号(索引29), 工号(索引30), 工号(索引31)
2025-09-16 14:30:25.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7393 | 方案A：安全设置列数: 0
2025-09-16 14:30:25.403 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2822 | 🔧 [P1优化] 表切换操作: default_table -> salary_data_2025_05_all_active
2025-09-16 14:30:25.405 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089, 薪资=2375
2025-09-16 14:30:25.406 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565, 薪资=1696
2025-09-16 14:30:25.408 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5528 | 最大可见行数已更新: 1000 -> 50
2025-09-16 14:30:25.413 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5581 | 根据数据量(50)自动调整最大可见行数为: 50
2025-09-16 14:30:25.417 | INFO     | src.modules.format_management.format_renderer:render_dataframe:127 | 🎯 [格式渲染] 隐藏字段配置: ['created_at', 'updated_at', 'id', 'sequence_number', 'row_number']
2025-09-16 14:30:25.418 | INFO     | src.modules.format_management.format_renderer:render_dataframe:135 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-09-16 14:30:25.425 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-09-16 14:30:25.427 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-09-16 14:30:25.432 | INFO     | src.modules.format_management.field_registry:get_display_fields:1582 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-09-16 14:30:25.433 | INFO     | src.modules.format_management.format_renderer:render_dataframe:186 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-09-16 14:30:25.435 | INFO     | src.modules.format_management.format_renderer:render_dataframe:244 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 22/25 个字段
2025-09-16 14:30:25.441 | INFO     | src.modules.format_management.format_renderer:render_dataframe:257 | 🔧 [P0修复] 字段重排完成: 原始22个 -> 最终22个字段
2025-09-16 14:30:25.442 | INFO     | src.modules.format_management.format_renderer:render_dataframe:273 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 22
2025-09-16 14:30:25.443 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 22
2025-09-16 14:30:25.532 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-09-16 14:30:25.533 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 22列
2025-09-16 14:30:25.545 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时11.7ms, 平均每行0.23ms
2025-09-16 14:30:25.556 | INFO     | src.core.performance_metrics_collector:__init__:74 | 性能度量收集器初始化完成，存储路径: logs\performance_metrics.json
2025-09-16 14:30:25.558 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=11.7ms, 策略=small_dataset
2025-09-16 14:30:25.559 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-09-16 14:30:25.561 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第0行数据工号: 19990089
2025-09-16 14:30:25.562 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第1行数据工号: 20161565
2025-09-16 14:30:25.563 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第2行数据工号: 20191782
2025-09-16 14:30:25.563 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第3行数据工号: 20151515
2025-09-16 14:30:25.564 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第4行数据工号: 20181640
2025-09-16 14:30:25.565 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-09-16 14:30:25.566 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=2375.00
2025-09-16 14:30:25.566 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=1696.00
2025-09-16 14:30:25.567 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=1427.00
2025-09-16 14:30:25.575 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=2175.00
2025-09-16 14:30:25.575 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=2582.00
2025-09-16 14:30:25.576 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:488 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-09-16 14:30:25.577 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:447 | 表格数据已设置: 50 行, 22 列
2025-09-16 14:30:25.578 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_05_all_active
2025-09-16 14:30:25.580 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:474 | 🔧 [新架构] 为表格 salary_data_2025_05_all_active 重新加载 29 个字段映射
2025-09-16 14:30:25.580 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 50 行, 耗时: 175.8ms
2025-09-16 14:30:25.581 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 14:30:25.582 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 14:30:25.591 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1708 | 🆕 [列宽保存] 未找到表格 salary_data_2025_05_all_active 的列宽配置
2025-09-16 14:30:25.592 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 14:30:25.602 | INFO     | src.core.pagination_state_manager:__init__:63 | 🔧 [立即修复] 分页状态管理器初始化完成
2025-09-16 14:30:25.603 | INFO     | src.gui.prototype.prototype_main_window:set_data:1047 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=0，等待数据事件纠正
2025-09-16 14:30:25.604 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3984 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 25列
2025-09-16 14:30:25.605 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 25 个, 行号起始 1, 共 50 行
2025-09-16 14:30:25.609 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4000 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_05_all_active, 传递参数: 25个表头
2025-09-16 14:30:25.610 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4004 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 25列
2025-09-16 14:30:25.691 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:401 | 已清理所有缓存
2025-09-16 14:30:25.691 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:11427 | ✅ [新架构] 组件状态一致性验证通过
2025-09-16 14:30:25.694 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:11390 | 🆕 [新架构] 导航迁移完成
2025-09-16 14:30:25.695 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4077 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-09-16 14:30:25.696 | INFO     | src.services.table_data_service:load_table_data:555 | 📨[data_event] published
2025-09-16 14:30:25.696 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7939 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-09-16 14:30:25.718 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1619 | 🔧 [P1修复] 找到 4 个匹配类型 'salary_data' 的表 (尝试 1/3)
2025-09-16 14:30:25.725 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_execute_salary_data_load:2055 | 动态加载了 1 个月份的工资数据导航
2025-09-16 14:30:25.734 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:2441 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-09-16 14:30:25.739 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月', '工资表 > 2025年', '工资表']
2025-09-16 14:30:25.740 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7713 | 导航变化: 工资表 > 2025年 > 05月 > 全部在职人员
2025-09-16 14:30:25.745 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10932 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_all_active -> None
2025-09-16 14:30:25.745 | WARNING  | src.gui.prototype.prototype_main_window:_clear_header_shadows_on_navigation:11256 | 🔧 [P1-2] 表间切换时检测到表头重影: ['工号']
2025-09-16 14:30:25.746 | INFO     | src.gui.table_header_manager:clear_table_header_cache_enhanced:355 | 表头缓存清理完成: 1/1 个表格成功清理
2025-09-16 14:30:25.747 | INFO     | src.gui.prototype.prototype_main_window:_clear_header_shadows_on_navigation:11263 | 🔧 [P1-2] 已执行表间切换表头重影清理
2025-09-16 14:30:25.748 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10948 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-09-16 14:30:25.749 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8724 | 🔧 [表名生成] 命中最近导入映射: ('工资表', '2025', '05', '全部在职人员') -> salary_data_2025_05_all_active
2025-09-16 14:30:25.750 | INFO     | src.gui.prototype.prototype_main_window:_process_navigation_change:7775 | 🔧 [P0-紧急修复] 导航变化时更新状态栏: 工资表 > 2025年 > 05月 > 全部在职人员
2025-09-16 14:30:25.750 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:384 | 已清理表 salary_data_2025_05_all_active 的缓存
2025-09-16 14:30:25.757 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11620 | 已注册 4 个表格到表头管理器
2025-09-16 14:30:25.758 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7934 | 🆕 使用新架构加载数据: salary_data_2025_05_all_active（通过事件系统）
2025-09-16 14:30:25.759 | INFO     | src.services.table_data_service:load_table_data:401 | [缓存命中] 使用缓存数据: 第1页
2025-09-16 14:30:25.760 | INFO     | src.services.table_data_service:load_table_data:412 | 📨[data_event] publish(cache) | table=salary_data_2025_05_all_active | rows=50 | page=1 | size=50 | total=1396 | request_id=SV-1758004225760-2831935c-C
2025-09-16 14:30:25.760 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3874 | 📥[data_event] received | table=salary_data_2025_05_all_active | request_id=SV-1758004225760-2831935c-C
2025-09-16 14:30:25.761 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3906 | 数据内容: 50行 x 28列
2025-09-16 14:30:25.762 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3937 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_05_all_active, 50行
2025-09-16 14:30:25.763 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3952 | 🔧 [P0-修复] 排序数据处理前: 28列 - ['工号', '姓名', '部门名称', '人员类别', '人员类别代码']...
2025-09-16 14:30:25.765 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:30:25.766 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:30:25.768 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔧 [P0-修复] 字段映射后: 28列
2025-09-16 14:30:25.775 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:7617 | 🔧 [P0-修复] 系统字段过滤: 隐藏 5 个字段 - ['年份', '月份', '序号', 'data_source', 'import_time']
2025-09-16 14:30:25.775 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3956 | 🔧 [P0-修复] 系统字段过滤后: 25列
2025-09-16 14:30:25.776 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:30:25.783 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:30:25.786 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:30:25.788 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7670 | 表 salary_data_2025_05_all_active 没有用户偏好设置，显示所有可见字段
2025-09-16 14:30:25.789 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3984 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 25列
2025-09-16 14:30:25.789 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 25 个, 行号起始 1, 共 50 行
2025-09-16 14:30:25.790 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4000 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_05_all_active, 传递参数: 25个表头
2025-09-16 14:30:25.790 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4004 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 25列
2025-09-16 14:30:25.845 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:401 | 已清理所有缓存
2025-09-16 14:30:25.846 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:11427 | ✅ [新架构] 组件状态一致性验证通过
2025-09-16 14:30:25.849 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:11390 | 🆕 [新架构] 导航迁移完成
2025-09-16 14:30:25.850 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4077 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-09-16 14:30:25.851 | INFO     | src.services.table_data_service:load_table_data:437 | 📨[data_event] published(cache)
2025-09-16 14:30:25.851 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7939 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-09-16 14:30:25.852 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:893 | 导航面板已重构：移除功能性导航，专注数据导航
2025-09-16 14:30:25.853 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:2179 | 🔧 [P2修复] 开始恢复导航状态...
2025-09-16 14:30:25.854 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:2207 | 🔧 [P2修复] 导航状态恢复完成: 0个展开项
2025-09-16 14:30:25.855 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:2132 | 🔧 [P2-2] 增强版导航数据刷新完成
2025-09-16 14:30:25.856 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2131 | MainWorkspaceArea 响应式适配: sm
2025-09-16 14:30:25.861 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1399 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-09-16 14:30:25.863 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2658 | 找到最新工资数据路径: 工资表 > 2025年 > 05月 > 全部在职人员
2025-09-16 14:30:25.866 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:2084 | 🔧 [P2-2] 开始增强版导航数据刷新...
2025-09-16 14:30:25.868 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:2441 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-09-16 14:30:25.869 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:893 | 导航面板已重构：移除功能性导航，专注数据导航
2025-09-16 14:30:25.870 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:2179 | 🔧 [P2修复] 开始恢复导航状态...
2025-09-16 14:30:25.870 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:2207 | 🔧 [P2修复] 导航状态恢复完成: 0个展开项
2025-09-16 14:30:25.871 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1966 | 导航树刷新完成，重新执行自动选择...
2025-09-16 14:30:25.876 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2658 | 找到最新工资数据路径: 工资表 > 2025年 > 05月 > 全部在职人员
2025-09-16 14:30:25.877 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1975 | 重新选择最新数据路径: 工资表 > 2025年 > 05月 > 全部在职人员
2025-09-16 14:30:25.878 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1999 | 重新选择完成: 工资表 > 2025年 > 05月 > 全部在职人员
2025-09-16 14:30:25.880 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:2132 | 🔧 [P2-2] 增强版导航数据刷新完成
2025-09-16 14:30:25.881 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2131 | MainWorkspaceArea 响应式适配: sm
2025-09-16 14:30:25.885 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:6217 | [P2] 导入引导：自动定位至 工资表 > 2025年 > 05月 > retired
2025-09-16 14:30:25.890 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:6667 | 尝试导航到新导入的路径: 工资表 > 2025年 > 05月 > retired
2025-09-16 14:30:25.890 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:6672 | 已成功导航到新导入的路径: 工资表 > 2025年 > 05月 > retired
2025-09-16 14:30:25.892 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8724 | 🔧 [表名生成] 命中最近导入映射: ('工资表', '2025', '05', 'retired') -> salary_data_2025_05_retired
2025-09-16 14:30:25.892 | INFO     | src.gui.prototype.prototype_main_window:_schedule_safe_navigation:6507 | 🔧 [P1修复] 安全导航后更新表名: salary_data_2025_05_retired
2025-09-16 14:30:25.893 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:6760 | [数据流追踪] 开始智能数据显示刷新: salary_data_2025_05_retired
2025-09-16 14:30:25.894 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:6765 | 🔧 [P1修复] 刷新数据时更新表名: salary_data_2025_05_retired
2025-09-16 14:30:25.903 | INFO     | src.core.smart_pagination_strategy:__init__:47 | 智能分页策略管理器初始化完成
2025-09-16 14:30:25.903 | INFO     | src.core.smart_pagination_strategy:should_use_pagination:68 | [数据流追踪] 分页决策输入: 总记录数=2, 页面大小=50, 用户偏好=None
2025-09-16 14:30:25.904 | INFO     | src.core.smart_pagination_strategy:should_use_pagination:93 | [数据流追踪] 分页决策输出: 策略=full_display, 原因=数据量(2条) <= 页面大小(50条), 预期性能=10ms, 决策耗时=1ms
2025-09-16 14:30:25.906 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:6787 | [数据流追踪] 智能分页策略决策: full_display, 原因=数据量(2条) <= 页面大小(50条), 预期性能=10ms
2025-09-16 14:30:25.907 | INFO     | src.gui.prototype.prototype_main_window:_execute_full_display_mode:6820 | [数据流追踪] 执行全量显示模式: salary_data_2025_05_retired, 2条记录
2025-09-16 14:30:25.908 | WARNING  | src.gui.prototype.prototype_main_window:_set_pagination_visibility:6893 | 🔧 [P0-修复] 分页组件不存在，跳过可见性设置
2025-09-16 14:30:25.909 | INFO     | src.core.unified_data_request_manager:request_table_data:237 | 开始处理数据请求
2025-09-16 14:30:25.911 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:899 | 正在从表 salary_data_2025_05_retired 分页获取数据（支持排序）: 第1页, 每页100条, 排序=0列
2025-09-16 14:30:25.918 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:1019 | 成功从表 salary_data_2025_05_retired 获取第1页数据（含排序）: 2 行，总计2行
2025-09-16 14:30:25.920 | INFO     | src.core.unified_data_request_manager:request_table_data:287 | 数据请求处理完成: 字段=24, 行数=2, 耗时=11.4ms
2025-09-16 14:30:25.921 | INFO     | src.modules.format_management.format_renderer:render_dataframe:127 | 🎯 [格式渲染] 隐藏字段配置: ['created_at', 'updated_at', 'id', 'sequence_number']
2025-09-16 14:30:25.922 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | 🎯 [格式渲染] 已隐藏字段: ['created_at', 'updated_at', 'id']
2025-09-16 14:30:25.923 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string_extract_last_two
2025-09-16 14:30:25.930 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-09-16 14:30:25.932 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-09-16 14:30:25.933 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 备注 -> 英文名: remarks -> 类型: string
2025-09-16 14:30:25.934 | INFO     | src.modules.format_management.field_registry:get_display_fields:1582 | 🔧 [P0-2修复] display_order转换完成: 17个字段，原始字段数: 17
2025-09-16 14:30:25.935 | INFO     | src.modules.format_management.format_renderer:render_dataframe:186 | 🔧 [DEBUG] table_type=retired_employees, display_fields=17个字段
2025-09-16 14:30:25.936 | INFO     | src.modules.format_management.format_renderer:render_dataframe:244 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 11/17 个字段
2025-09-16 14:30:25.937 | INFO     | src.modules.format_management.format_renderer:render_dataframe:252 | 🔧 [P0-关键修复] 保留未配置的业务字段: 10个
2025-09-16 14:30:25.939 | INFO     | src.modules.format_management.format_renderer:render_dataframe:257 | 🔧 [P0修复] 字段重排完成: 原始21个 -> 最终21个字段
2025-09-16 14:30:25.944 | INFO     | src.modules.format_management.format_renderer:render_dataframe:273 | 🎨 [格式渲染] DataFrame格式化完成: retired_employees, 行数: 2, 列数: 21
2025-09-16 14:30:25.945 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: retired_employees, 行数: 2, 列数: 21
2025-09-16 14:30:25.947 | INFO     | src.services.table_data_service:load_table_data:532 | 📨[data_event] publish | table=salary_data_2025_05_retired | rows=2 | page=1 | size=100 | total=2 | req_type=RequestType.INITIAL_LOAD | request_id=SV-1758004225947-08882ea9
2025-09-16 14:30:25.948 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3874 | 📥[data_event] received | table=salary_data_2025_05_retired | request_id=SV-1758004225947-08882ea9
2025-09-16 14:30:25.949 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3906 | 数据内容: 2行 x 21列
2025-09-16 14:30:25.949 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3937 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_05_retired, 2行
2025-09-16 14:30:25.950 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3952 | 🔧 [P0-修复] 排序数据处理前: 21列 - ['人员代码', '姓名', '部门名称', '增发一次\n性生活补贴', '护理费']...
2025-09-16 14:30:25.952 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:30:25.958 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:30:25.960 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔧 [P0-修复] 字段映射后: 21列
2025-09-16 14:30:25.961 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:7617 | 🔧 [P0-修复] 系统字段过滤: 隐藏 5 个字段 - ['年份', '月份', '序号', 'data_source', 'import_time']
2025-09-16 14:30:25.962 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3956 | 🔧 [P0-修复] 系统字段过滤后: 16列
2025-09-16 14:30:25.963 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:30:25.970 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:30:25.971 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:30:25.972 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7670 | 表 salary_data_2025_05_retired 没有用户偏好设置，显示所有可见字段
2025-09-16 14:30:25.973 | INFO     | src.gui.prototype.prototype_main_window:set_data:922 | 通过公共接口设置新的表格数据(DataFrame): 2 行
2025-09-16 14:30:25.975 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:30:25.976 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:30:25.980 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:30:25.989 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:30:25.990 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:30:25.991 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7670 | 表 salary_data_2025_05_retired 没有用户偏好设置，显示所有可见字段
2025-09-16 14:30:25.998 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 2行，表名: salary_data_2025_05_retired
2025-09-16 14:30:26.000 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2822 | 🔧 [P1优化] 表切换操作: salary_data_2025_05_all_active -> salary_data_2025_05_retired
2025-09-16 14:30:26.000 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19289006, 薪资=N/A
2025-09-16 14:30:26.001 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=19339009, 薪资=N/A
2025-09-16 14:30:26.012 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5528 | 最大可见行数已更新: 50 -> 10
2025-09-16 14:30:26.012 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5581 | 根据数据量(2)自动调整最大可见行数为: 10
2025-09-16 14:30:26.016 | INFO     | src.modules.format_management.format_renderer:render_dataframe:127 | 🎯 [格式渲染] 隐藏字段配置: ['created_at', 'updated_at', 'id', 'sequence_number', 'row_number']
2025-09-16 14:30:26.020 | INFO     | src.modules.format_management.format_renderer:render_dataframe:135 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-09-16 14:30:26.023 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-09-16 14:30:26.023 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-09-16 14:30:26.025 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 备注 -> 英文名: 备注 -> 类型: string
2025-09-16 14:30:26.028 | INFO     | src.modules.format_management.field_registry:get_display_fields:1582 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-09-16 14:30:26.029 | INFO     | src.modules.format_management.format_renderer:render_dataframe:186 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-09-16 14:30:26.034 | INFO     | src.modules.format_management.format_renderer:render_dataframe:244 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 6/25 个字段
2025-09-16 14:30:26.034 | INFO     | src.modules.format_management.format_renderer:render_dataframe:252 | 🔧 [P0-关键修复] 保留未配置的业务字段: 10个
2025-09-16 14:30:26.036 | INFO     | src.modules.format_management.format_renderer:render_dataframe:257 | 🔧 [P0修复] 字段重排完成: 原始16个 -> 最终16个字段
2025-09-16 14:30:26.037 | INFO     | src.modules.format_management.format_renderer:render_dataframe:273 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 2, 列数: 16
2025-09-16 14:30:26.038 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 2, 列数: 16
2025-09-16 14:30:26.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2983 | [列对齐] 已对齐记录与表头: rows=2->2, cols=16
2025-09-16 14:30:26.042 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7393 | 方案A：安全设置列数: 16
2025-09-16 14:30:26.049 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 2行数据
2025-09-16 14:30:26.050 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 2行 x 16列
2025-09-16 14:30:26.051 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时1.0ms, 平均每行0.52ms
2025-09-16 14:30:26.052 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=2, 渲染时间=1.0ms, 策略=small_dataset
2025-09-16 14:30:26.052 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-09-16 14:30:26.054 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第0行数据工号: 19289006
2025-09-16 14:30:26.055 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第1行数据工号: 19339009
2025-09-16 14:30:26.055 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 2
2025-09-16 14:30:26.056 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[0]: 工号=19289006, 薪资=N/A
2025-09-16 14:30:26.062 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[1]: 工号=19339009, 薪资=N/A
2025-09-16 14:30:26.063 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:488 | 可见行数据顺序: ['19289006', '19339009']
2025-09-16 14:30:26.065 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_05_retired
2025-09-16 14:30:26.067 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:474 | 🔧 [新架构] 为表格 salary_data_2025_05_retired 重新加载 29 个字段映射
2025-09-16 14:30:26.067 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 2 行, 耗时: 66.1ms
2025-09-16 14:30:26.068 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 14:30:26.069 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 14:30:26.070 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1708 | 🆕 [列宽保存] 未找到表格 salary_data_2025_05_retired 的列宽配置
2025-09-16 14:30:26.076 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 14:30:26.077 | INFO     | src.gui.prototype.prototype_main_window:set_data:1047 | 📊[pagination-write] 跳过写入当前页数据量(total=2)，保持 total=0，等待数据事件纠正
2025-09-16 14:30:26.078 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3984 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 2行, 16列
2025-09-16 14:30:26.079 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 16 个, 行号起始 1, 共 2 行
2025-09-16 14:30:26.079 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4000 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_05_retired, 传递参数: 16个表头
2025-09-16 14:30:26.080 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4004 | 🔧 [统一数据设置] 数据已成功设置到UI: 2行, 16列
2025-09-16 14:30:26.081 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4077 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-09-16 14:30:26.081 | INFO     | src.services.table_data_service:load_table_data:555 | 📨[data_event] published
2025-09-16 14:30:26.082 | INFO     | src.gui.prototype.prototype_main_window:_execute_full_display_mode:6844 | [数据流追踪] 全量显示模式加载成功: 2行数据
2025-09-16 14:30:26.084 | INFO     | src.gui.prototype.prototype_main_window:_schedule_safe_navigation:6510 | 同步导航完成: 工资表 > 2025年 > 05月 > retired
2025-09-16 14:30:26.091 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2658 | 找到最新工资数据路径: 工资表 > 2025年 > 05月 > 全部在职人员
2025-09-16 14:30:26.094 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:6281 | separate_tables 模式下目标路径为概览路径，跳过路径完整性检查
2025-09-16 14:30:26.095 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:30:26.103 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:30:26.104 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:30:26.104 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:30:26.107 | INFO     | src.modules.system_config.config_manager:_create_backup:510 | 配置文件备份已创建: C:\test\salary_changes\salary_changes\config_backups\config_20250916_143026.json
2025-09-16 14:30:26.108 | INFO     | src.modules.system_config.config_manager:save_config:413 | 正在保存配置文件: C:\test\salary_changes\salary_changes\config.json
2025-09-16 14:30:26.142 | INFO     | src.modules.system_config.config_manager:save_config:418 | 配置文件保存成功
2025-09-16 14:30:26.143 | INFO     | src.modules.system_config.config_manager:save_configuration:568 | 配置保存成功: import_config_工资表_16fields
2025-09-16 14:30:26.151 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_05_retired
2025-09-16 14:30:26.192 | INFO     | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2492 | 🔧 [方案A1] 表头重影清理完成
2025-09-16 14:30:26.268 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_05_retired
2025-09-16 14:30:26.296 | INFO     | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2492 | 🔧 [方案A1] 表头重影清理完成
2025-09-16 14:30:26.901 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:6373 | 检查是否需要更新导航面板: ['工资表']
2025-09-16 14:30:26.901 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:6484 | 其他类型数据导入，使用标准刷新
2025-09-16 14:30:26.901 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:2084 | 🔧 [P2-2] 开始增强版导航数据刷新...
2025-09-16 14:30:26.901 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月', '工资表 > 2025年', '工资表']
2025-09-16 14:30:26.901 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7713 | 导航变化: 工资表 > 2025年 > 05月 > 全部在职人员
2025-09-16 14:30:26.901 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10932 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_retired -> None
2025-09-16 14:30:26.901 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10948 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-09-16 14:30:26.901 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8724 | 🔧 [表名生成] 命中最近导入映射: ('工资表', '2025', '05', '全部在职人员') -> salary_data_2025_05_all_active
2025-09-16 14:30:26.901 | INFO     | src.gui.prototype.prototype_main_window:_process_navigation_change:7775 | 🔧 [P0-紧急修复] 导航变化时更新状态栏: 工资表 > 2025年 > 05月 > 全部在职人员
2025-09-16 14:30:26.901 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:384 | 已清理表 salary_data_2025_05_all_active 的缓存
2025-09-16 14:30:26.917 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11620 | 已注册 4 个表格到表头管理器
2025-09-16 14:30:26.917 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1197 | 🔧 [P1-3] 开始智能表头重影检测，共 5 个表格
2025-09-16 14:30:26.917 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1270 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-09-16 14:30:26.917 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1271 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-09-16 14:30:26.917 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7934 | 🆕 使用新架构加载数据: salary_data_2025_05_all_active（通过事件系统）
2025-09-16 14:30:26.917 | INFO     | src.services.table_data_service:load_table_data:377 | 加载表格数据: 页码: 1
2025-09-16 14:30:26.917 | INFO     | src.services.table_data_service:load_table_data:401 | [缓存命中] 使用缓存数据: 第1页
2025-09-16 14:30:26.917 | INFO     | src.services.table_data_service:load_table_data:412 | 📨[data_event] publish(cache) | table=salary_data_2025_05_all_active | rows=50 | page=1 | size=50 | total=1396 | request_id=SV-1758004226917-9358290c-C
2025-09-16 14:30:26.917 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3874 | 📥[data_event] received | table=salary_data_2025_05_all_active | request_id=SV-1758004226917-9358290c-C
2025-09-16 14:30:26.917 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3906 | 数据内容: 50行 x 28列
2025-09-16 14:30:26.917 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3937 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_05_all_active, 50行
2025-09-16 14:30:26.917 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3952 | 🔧 [P0-修复] 排序数据处理前: 28列 - ['工号', '姓名', '部门名称', '人员类别', '人员类别代码']...
2025-09-16 14:30:26.932 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:30:26.932 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:30:26.932 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔧 [P0-修复] 字段映射后: 28列
2025-09-16 14:30:26.932 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:7617 | 🔧 [P0-修复] 系统字段过滤: 隐藏 5 个字段 - ['年份', '月份', '序号', 'data_source', 'import_time']
2025-09-16 14:30:26.932 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3956 | 🔧 [P0-修复] 系统字段过滤后: 25列
2025-09-16 14:30:26.932 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:30:26.948 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:30:26.948 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:30:26.948 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7670 | 表 salary_data_2025_05_all_active 没有用户偏好设置，显示所有可见字段
2025-09-16 14:30:26.948 | INFO     | src.gui.prototype.prototype_main_window:set_data:922 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-09-16 14:30:26.948 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:30:26.948 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:30:26.964 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:30:26.964 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:30:26.964 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:30:26.964 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7670 | 表 salary_data_2025_05_all_active 没有用户偏好设置，显示所有可见字段
2025-09-16 14:30:26.980 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_05_all_active
2025-09-16 14:30:26.980 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引1, 名称'工号'
2025-09-16 14:30:26.980 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引2, 名称'工号'
2025-09-16 14:30:26.980 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引3, 名称'工号'
2025-09-16 14:30:26.980 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引4, 名称'工号'
2025-09-16 14:30:26.980 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引5, 名称'工号'
2025-09-16 14:30:26.980 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引6, 名称'工号'
2025-09-16 14:30:26.980 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引7, 名称'工号'
2025-09-16 14:30:26.980 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引24, 名称'工号'
2025-09-16 14:30:26.980 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引25, 名称'工号'
2025-09-16 14:30:26.980 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引26, 名称'工号'
2025-09-16 14:30:26.980 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引27, 名称'工号'
2025-09-16 14:30:26.980 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引28, 名称'工号'
2025-09-16 14:30:26.980 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引29, 名称'工号'
2025-09-16 14:30:26.980 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引30, 名称'工号'
2025-09-16 14:30:26.994 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引31, 名称'工号'
2025-09-16 14:30:26.994 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4899 | 🔧 [方案A2] 表头去重完成: 37 -> 22
2025-09-16 14:30:26.994 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4902 | 🔧 [方案A2] 移除的重复项: 工号(索引1), 工号(索引2), 工号(索引3), 工号(索引4), 工号(索引5), 工号(索引6), 工号(索引7), 工号(索引24), 工号(索引25), 工号(索引26), 工号(索引27), 工号(索引28), 工号(索引29), 工号(索引30), 工号(索引31)
2025-09-16 14:30:26.994 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7393 | 方案A：安全设置列数: 0
2025-09-16 14:30:26.994 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2822 | 🔧 [P1优化] 表切换操作: salary_data_2025_05_retired -> salary_data_2025_05_all_active
2025-09-16 14:30:26.994 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089, 薪资=2375
2025-09-16 14:30:26.994 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565, 薪资=1696
2025-09-16 14:30:26.994 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5528 | 最大可见行数已更新: 10 -> 50
2025-09-16 14:30:26.994 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5581 | 根据数据量(50)自动调整最大可见行数为: 50
2025-09-16 14:30:26.994 | INFO     | src.modules.format_management.format_renderer:render_dataframe:127 | 🎯 [格式渲染] 隐藏字段配置: ['created_at', 'updated_at', 'id', 'sequence_number', 'row_number']
2025-09-16 14:30:26.994 | INFO     | src.modules.format_management.format_renderer:render_dataframe:135 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-09-16 14:30:27.010 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-09-16 14:30:27.010 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-09-16 14:30:27.026 | INFO     | src.modules.format_management.field_registry:get_display_fields:1582 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-09-16 14:30:27.026 | INFO     | src.modules.format_management.format_renderer:render_dataframe:186 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-09-16 14:30:27.026 | INFO     | src.modules.format_management.format_renderer:render_dataframe:244 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 22/25 个字段
2025-09-16 14:30:27.026 | INFO     | src.modules.format_management.format_renderer:render_dataframe:257 | 🔧 [P0修复] 字段重排完成: 原始22个 -> 最终22个字段
2025-09-16 14:30:27.026 | INFO     | src.modules.format_management.format_renderer:render_dataframe:273 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 22
2025-09-16 14:30:27.026 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 22
2025-09-16 14:30:27.041 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-09-16 14:30:27.041 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 22列
2025-09-16 14:30:27.057 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时15.7ms, 平均每行0.31ms
2025-09-16 14:30:27.057 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=15.7ms, 策略=small_dataset
2025-09-16 14:30:27.057 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-09-16 14:30:27.057 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第0行数据工号: 19990089
2025-09-16 14:30:27.057 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第1行数据工号: 20161565
2025-09-16 14:30:27.057 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第2行数据工号: 20191782
2025-09-16 14:30:27.057 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第3行数据工号: 20151515
2025-09-16 14:30:27.057 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第4行数据工号: 20181640
2025-09-16 14:30:27.057 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-09-16 14:30:27.057 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=2375.00
2025-09-16 14:30:27.057 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=1696.00
2025-09-16 14:30:27.057 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=1427.00
2025-09-16 14:30:27.057 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=2175.00
2025-09-16 14:30:27.057 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=2582.00
2025-09-16 14:30:27.057 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:488 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-09-16 14:30:27.057 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:447 | 表格数据已设置: 50 行, 22 列
2025-09-16 14:30:27.078 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_05_all_active
2025-09-16 14:30:27.083 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:474 | 🔧 [新架构] 为表格 salary_data_2025_05_all_active 重新加载 29 个字段映射
2025-09-16 14:30:27.084 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 50 行, 耗时: 83.5ms
2025-09-16 14:30:27.084 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 14:30:27.085 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 14:30:27.086 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1708 | 🆕 [列宽保存] 未找到表格 salary_data_2025_05_all_active 的列宽配置
2025-09-16 14:30:27.087 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 14:30:27.088 | INFO     | src.gui.prototype.prototype_main_window:set_data:1047 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=0，等待数据事件纠正
2025-09-16 14:30:27.089 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3984 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 25列
2025-09-16 14:30:27.089 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 25 个, 行号起始 1, 共 50 行
2025-09-16 14:30:27.094 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4000 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_05_all_active, 传递参数: 25个表头
2025-09-16 14:30:27.098 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4004 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 25列
2025-09-16 14:30:27.153 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:401 | 已清理所有缓存
2025-09-16 14:30:27.154 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:11427 | ✅ [新架构] 组件状态一致性验证通过
2025-09-16 14:30:27.157 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:11390 | 🆕 [新架构] 导航迁移完成
2025-09-16 14:30:27.158 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4077 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-09-16 14:30:27.158 | INFO     | src.services.table_data_service:load_table_data:437 | 📨[data_event] published(cache)
2025-09-16 14:30:27.159 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7939 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-09-16 14:30:27.166 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_execute_salary_data_load:2055 | 动态加载了 1 个月份的工资数据导航
2025-09-16 14:30:27.170 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:2441 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-09-16 14:30:27.176 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月', '工资表 > 2025年', '工资表']
2025-09-16 14:30:27.176 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:893 | 导航面板已重构：移除功能性导航，专注数据导航
2025-09-16 14:30:27.177 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:2179 | 🔧 [P2修复] 开始恢复导航状态...
2025-09-16 14:30:27.223 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:2207 | 🔧 [P2修复] 导航状态恢复完成: 4个展开项
2025-09-16 14:30:27.223 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:2132 | 🔧 [P2-2] 增强版导航数据刷新完成
2025-09-16 14:30:27.227 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:6488 | 导航面板已刷新
2025-09-16 14:30:27.229 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed:4939 | 🔧 [P1-强化] 延迟调整时发现列数不匹配: 期望22列, 实际25列
2025-09-16 14:30:27.230 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7393 | 方案A：安全设置列数: 22
2025-09-16 14:30:27.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed:4956 | 🔧 [P1-强化] 最终列数修复: 22列
2025-09-16 14:30:27.232 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_05_all_active
2025-09-16 14:30:27.293 | WARNING  | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2429 | 🔧 [方案A1] 发现重复表头: 列1(工号)，原始列在0
2025-09-16 14:30:27.293 | WARNING  | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2429 | 🔧 [方案A1] 发现重复表头: 列18(工号)，原始列在0
2025-09-16 14:30:27.297 | WARNING  | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2429 | 🔧 [方案A1] 发现重复表头: 列19(工号)，原始列在0
2025-09-16 14:30:27.298 | INFO     | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2440 | 🔧 [方案A1] 检测到3个重复表头，开始移除
2025-09-16 14:30:27.301 | INFO     | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2448 | 🔧 [方案A1] 已移除重复列: 列19(工号)
2025-09-16 14:30:27.302 | INFO     | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2448 | 🔧 [方案A1] 已移除重复列: 列18(工号)
2025-09-16 14:30:27.303 | INFO     | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2448 | 🔧 [方案A1] 已移除重复列: 列1(工号)
2025-09-16 14:30:27.318 | INFO     | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2464 | 🔧 [方案A1] 表格显示已刷新，列宽已调整
2025-09-16 14:30:27.321 | INFO     | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2492 | 🔧 [方案A1] 表头重影清理完成
2025-09-16 14:30:28.864 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-09-16 14:30:28.864 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-16 14:30:28.880 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-16 14:30:28.880 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:30:28.974 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-09-16 14:30:28.974 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:30:28.974 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-16 14:30:28.974 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-09-16 14:30:28.974 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-16 14:30:28.974 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-09-16 14:30:28.990 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-09-16 14:30:28.990 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:30:29.099 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:30:29.099 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-09-16 14:30:29.099 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:30:29.192 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:30:29.192 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-09-16 14:30:29.208 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:30:29.302 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:30:29.302 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-16 14:30:36.568 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet 'A岗职工' 创建默认配置
2025-09-16 14:30:36.583 | INFO     | src.modules.data_import.formatting_engine:clear_temporary_field_types:978 | 🔧 [方案一实施] 已清理 0 个临时字段类型
2025-09-16 14:30:36.583 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet 'A岗职工' 创建默认配置
2025-09-16 14:30:36.583 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 4 个模板
2025-09-16 14:30:36.583 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: A岗职工
2025-09-16 14:30:36.583 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-16 14:30:36.583 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-16 14:30:36.583 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:30:36.677 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-09-16 14:30:36.693 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:30:36.693 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-16 14:30:36.693 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 21列
2025-09-16 14:30:36.693 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 21列
2025-09-16 14:30:36.802 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:30:36.911 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:30:36.911 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-16 14:30:36.911 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-16 14:30:36.911 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-09-16 14:30:38.650 | INFO     | src.modules.data_import.formatting_engine:clear_temporary_field_types:978 | 🔧 [方案一实施] 已清理 0 个临时字段类型
2025-09-16 14:30:38.650 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 4 个模板
2025-09-16 14:30:38.650 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: 离休人员工资表
2025-09-16 14:30:38.650 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-16 14:30:38.650 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-16 14:30:38.650 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:30:38.759 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-09-16 14:30:38.759 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:30:38.759 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-16 14:30:38.759 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 16列
2025-09-16 14:30:38.759 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 16列
2025-09-16 14:30:38.852 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:30:38.946 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:30:38.962 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-16 14:30:38.962 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-16 14:30:38.962 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-09-16 14:30:40.607 | INFO     | src.modules.data_import.formatting_engine:clear_temporary_field_types:978 | 🔧 [方案一实施] 已清理 0 个临时字段类型
2025-09-16 14:30:40.607 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 4 个模板
2025-09-16 14:30:40.607 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: A岗职工
2025-09-16 14:30:40.607 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-16 14:30:40.607 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-16 14:30:40.607 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:30:40.716 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:30:40.716 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-16 14:30:40.716 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 21列
2025-09-16 14:30:40.716 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 21列
2025-09-16 14:30:40.841 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:30:40.935 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-09-16 14:30:40.950 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:30:40.950 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-16 14:30:40.950 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-16 14:30:40.950 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-09-16 14:30:42.985 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-16 14:30:42.985 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-16 14:30:42.985 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:30:43.106 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-09-16 14:30:43.109 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:30:43.111 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-16 14:30:43.112 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 10行 x 21列
2025-09-16 14:30:43.116 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 10行 × 21列
2025-09-16 14:30:51.609 | INFO     | src.modules.data_import.multi_sheet_importer:set_config_sync_manager:110 | 🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter
2025-09-16 14:30:51.609 | INFO     | src.gui.workers.data_import_worker:setup_import:64 | 导入工作线程配置完成: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-16 14:30:51.609 | INFO     | src.gui.workers.data_import_worker:run:112 | 开始异步数据导入
2025-09-16 14:30:51.625 | INFO     | src.modules.data_import.multi_sheet_importer:set_config_sync_manager:110 | 🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter
2025-09-16 14:30:51.625 | INFO     | src.gui.workers.data_import_worker:run:142 | 🔧 [P3修复] ConfigSyncManager注入成功
2025-09-16 14:30:51.642 | INFO     | src.modules.data_import.multi_sheet_importer:set_stop_checker:115 | 🔧 [可取消] StopChecker 已设置
2025-09-16 14:30:51.642 | INFO     | src.modules.data_import.multi_sheet_importer:set_progress_callback:127 | 🔧 [P1修复] 进度回调已设置到MultiSheetImporter
2025-09-16 14:30:51.642 | INFO     | src.gui.workers.data_import_worker:run:164 | 🔧 [P1修复] 进度回调已注入到MultiSheetImporter
2025-09-16 14:30:51.642 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:286 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-16 14:30:51.642 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-16 14:30:51.844 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-09-16 14:30:51.844 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:327 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']; 实际将导入 4 个: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-09-16 14:30:51.844 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:335 | effective_strategy=separate_tables, target_path=异动人员表
2025-09-16 14:30:51.844 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-16 14:30:51.844 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:30:51.937 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-09-16 14:30:51.953 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:30:51.953 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-16 14:30:51.953 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-09-16 14:30:51.953 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-16 14:30:51.953 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-09-16 14:30:51.953 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-09-16 14:30:51.953 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:812 | 工作表 离休人员工资表 使用智能默认配置
2025-09-16 14:30:51.953 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:1146 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-09-16 14:30:51.953 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:860 | 未找到相似配置，使用默认映射: 16 个字段
2025-09-16 14:30:51.969 | INFO     | src.modules.data_import.multi_sheet_importer:_save_unified_change_data_mapping:1406 | 🔧 [P1-1修复] 异动表统一映射保存成功: change_data_2025_12_retired_employees, 16个字段
2025-09-16 14:30:51.969 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:922 | 为表 change_data_2025_12_retired_employees 生成标准化字段映射: 22 个字段
2025-09-16 14:30:51.984 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:943 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-09-16 14:30:51.984 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:585 | 🔧 [P2-1修复] 检测到异动表导入: 异动人员表
2025-09-16 14:30:51.984 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:600 | 🔧 [P2-1修复] 异动表使用动态模式
2025-09-16 14:30:51.984 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:665 | [P0] 使用英文名生成表名: change_data_2025_12_retired | source=ui_selection
2025-09-16 14:30:51.984 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 序号
2025-09-16 14:30:51.984 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 人员代码
2025-09-16 14:30:51.984 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 姓名
2025-09-16 14:30:51.984 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 部门名称
2025-09-16 14:30:51.984 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 基本
离休费
2025-09-16 14:30:51.984 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 结余
津贴
2025-09-16 14:30:51.984 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 生活
补贴
2025-09-16 14:30:51.984 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 住房
补贴
2025-09-16 14:30:51.984 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 物业
补贴
2025-09-16 14:30:51.984 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 离休
补贴
2025-09-16 14:30:51.984 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 护理费
2025-09-16 14:30:51.984 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 增发一次
性生活补贴
2025-09-16 14:30:51.984 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 补发
2025-09-16 14:30:51.984 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 合计
2025-09-16 14:30:51.984 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 借支
2025-09-16 14:30:51.984 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 备注
2025-09-16 14:30:51.984 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: data_source
2025-09-16 14:30:51.984 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: import_time
2025-09-16 14:30:52.000 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:494 | 成功创建异动数据表: change_data_2025_12_retired
2025-09-16 14:30:52.000 | INFO     | src.modules.data_storage.dynamic_table_manager:_add_table_metadata:1420 | 🔧 [P0修复] 异动表元数据保存成功: change_data_2025_12_retired -> retired
2025-09-16 14:30:52.000 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:500 | 🔧 [元数据修复] 异动表元数据保存成功: change_data_2025_12_retired
2025-09-16 14:30:52.016 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2723 | 🔧 [P0-CRITICAL修复] 异动表 change_data_2025_12_retired 保持原始中文字段名
2025-09-16 14:30:52.016 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2737 | [FIX] [修复标识] 保留未映射列: ['序号', '人员代码', '姓名', '部门名称', '基本\n离休费', '结余\n津贴', '生活\n补贴', '住房\n补贴', '物业\n补贴', '离休\n补贴', '护理费', '增发一次\n性生活补贴', '补发', '合计', '借支', '备注', 'data_source', 'import_time']
2025-09-16 14:30:52.016 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2746 | [FIX] [修复标识] 导入列名映射成功: 18 个字段已映射
2025-09-16 14:30:52.016 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2780 | 🔧 [P0-CRITICAL修复] 异动表 change_data_2025_12_retired 清理列名（移除换行符但保持中文）
2025-09-16 14:30:52.016 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2785 | 🔧 清理后的列名: ['序号', '人员代码', '姓名', '部门名称', '基本离休费', '结余津贴', '生活补贴', '住房补贴', '物业补贴', '离休补贴']...
2025-09-16 14:30:52.016 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2902 | 🔧 [方案A3] 创建系统字段：复制'人员代码'到'employee_id'
2025-09-16 14:30:52.016 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2925 | 🔧 [P0-CRITICAL修复] 异动表 change_data_2025_12_retired 只添加系统字段
2025-09-16 14:30:52.016 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:3015 | [TX] 验证成功：表 change_data_2025_12_retired 中有 2 条记录
2025-09-16 14:30:52.016 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:3022 | [TX] 异动表数据采样验证通过
2025-09-16 14:30:52.016 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:3025 | 成功向表 change_data_2025_12_retired 保存 2 条数据。
2025-09-16 14:30:52.016 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:30:52.125 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:30:52.125 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-09-16 14:30:52.125 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-16 14:30:52.125 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-09-16 14:30:52.125 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:812 | 工作表 退休人员工资表 使用智能默认配置
2025-09-16 14:30:52.125 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:1146 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-09-16 14:30:52.125 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:860 | 未找到相似配置，使用默认映射: 27 个字段
2025-09-16 14:30:52.141 | INFO     | src.modules.data_import.multi_sheet_importer:_save_unified_change_data_mapping:1406 | 🔧 [P1-1修复] 异动表统一映射保存成功: change_data_2025_12_pension_employees, 27个字段
2025-09-16 14:30:52.141 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:922 | 为表 change_data_2025_12_pension_employees 生成标准化字段映射: 33 个字段
2025-09-16 14:30:52.141 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:943 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-09-16 14:30:52.156 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:585 | 🔧 [P2-1修复] 检测到异动表导入: 异动人员表
2025-09-16 14:30:52.156 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:600 | 🔧 [P2-1修复] 异动表使用动态模式
2025-09-16 14:30:52.156 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:665 | [P0] 使用英文名生成表名: change_data_2025_12_pension | source=ui_selection
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 序号
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 人员代码
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 姓名
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 部门名称
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 人员类别代码
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 基本退休费
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 津贴
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 结余津贴
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 离退休生活补贴
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 护理费
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 物业补贴
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 住房补贴
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 增资预付
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 2016待遇调整
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 2017待遇调整
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 2018待遇调整
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 2019待遇调整
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 2020待遇调整
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 2021待遇调整
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 2022待遇调整
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 2023待遇调整
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 补发
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 借支
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 应发工资
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 公积
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 保险扣款
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 备注
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: data_source
2025-09-16 14:30:52.156 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: import_time
2025-09-16 14:30:52.173 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:494 | 成功创建异动数据表: change_data_2025_12_pension
2025-09-16 14:30:52.173 | INFO     | src.modules.data_storage.dynamic_table_manager:_add_table_metadata:1420 | 🔧 [P0修复] 异动表元数据保存成功: change_data_2025_12_pension -> pension
2025-09-16 14:30:52.173 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:500 | 🔧 [元数据修复] 异动表元数据保存成功: change_data_2025_12_pension
2025-09-16 14:30:52.188 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2723 | 🔧 [P0-CRITICAL修复] 异动表 change_data_2025_12_pension 保持原始中文字段名
2025-09-16 14:30:52.188 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2737 | [FIX] [修复标识] 保留未映射列: ['序号', '人员代码', '姓名', '部门名称', '人员类别代码', '基本退休费', '津贴', '结余津贴', '离退休生活补贴', '护理费', '物业补贴', '住房补贴', '增资预付', '2016待遇调整', '2017待遇调整', '2018待遇调整', '2019待遇调整', '2020待遇调整', '2021待遇调整', '2022待遇调整', '2023待遇调整', '补发', '借支', '应发工资', '公积', '保险扣款', '备注', 'data_source', 'import_time']
2025-09-16 14:30:52.188 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2746 | [FIX] [修复标识] 导入列名映射成功: 29 个字段已映射
2025-09-16 14:30:52.188 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2780 | 🔧 [P0-CRITICAL修复] 异动表 change_data_2025_12_pension 清理列名（移除换行符但保持中文）
2025-09-16 14:30:52.188 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2785 | 🔧 清理后的列名: ['序号', '人员代码', '姓名', '部门名称', '人员类别代码', '基本退休费', '津贴', '结余津贴', '离退休生活补贴', '护理费']...
2025-09-16 14:30:52.188 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2902 | 🔧 [方案A3] 创建系统字段：复制'人员代码'到'employee_id'
2025-09-16 14:30:52.188 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2925 | 🔧 [P0-CRITICAL修复] 异动表 change_data_2025_12_pension 只添加系统字段
2025-09-16 14:30:52.219 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:3015 | [TX] 验证成功：表 change_data_2025_12_pension 中有 13 条记录
2025-09-16 14:30:52.219 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:3022 | [TX] 异动表数据采样验证通过
2025-09-16 14:30:52.219 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:3025 | 成功向表 change_data_2025_12_pension 保存 13 条数据。
2025-09-16 14:30:52.219 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:30:52.328 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:30:52.328 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-09-16 14:30:52.344 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-16 14:30:52.344 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-09-16 14:30:52.344 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:812 | 工作表 全部在职人员工资表 使用智能默认配置
2025-09-16 14:30:52.344 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:1146 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-09-16 14:30:52.344 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:860 | 未找到相似配置，使用默认映射: 23 个字段
2025-09-16 14:30:52.344 | INFO     | src.modules.data_import.multi_sheet_importer:_save_unified_change_data_mapping:1406 | 🔧 [P1-1修复] 异动表统一映射保存成功: change_data_2025_12_active_employees, 23个字段
2025-09-16 14:30:52.359 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:922 | 为表 change_data_2025_12_active_employees 生成标准化字段映射: 29 个字段
2025-09-16 14:30:52.359 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:943 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-09-16 14:30:52.359 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:585 | 🔧 [P2-1修复] 检测到异动表导入: 异动人员表
2025-09-16 14:30:52.359 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:600 | 🔧 [P2-1修复] 异动表使用动态模式
2025-09-16 14:30:52.359 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:665 | [P0] 使用英文名生成表名: change_data_2025_12_all_active | source=ui_selection
2025-09-16 14:30:52.359 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 序号
2025-09-16 14:30:52.359 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 工号
2025-09-16 14:30:52.359 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 姓名
2025-09-16 14:30:52.359 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 部门名称
2025-09-16 14:30:52.375 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 人员类别代码
2025-09-16 14:30:52.375 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 人员类别
2025-09-16 14:30:52.375 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 2025年岗位工资
2025-09-16 14:30:52.375 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 2025年薪级工资
2025-09-16 14:30:52.375 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 津贴
2025-09-16 14:30:52.375 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 结余津贴
2025-09-16 14:30:52.375 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 2025年基础性绩效
2025-09-16 14:30:52.375 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 卫生费
2025-09-16 14:30:52.375 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 交通补贴
2025-09-16 14:30:52.375 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 物业补贴
2025-09-16 14:30:52.375 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 住房补贴
2025-09-16 14:30:52.375 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 车补
2025-09-16 14:30:52.375 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 通讯补贴
2025-09-16 14:30:52.375 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 2025年奖励性绩效预发
2025-09-16 14:30:52.375 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 补发
2025-09-16 14:30:52.375 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 借支
2025-09-16 14:30:52.375 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 应发工资
2025-09-16 14:30:52.375 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 2025公积金
2025-09-16 14:30:52.375 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 代扣代存养老保险
2025-09-16 14:30:52.375 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: data_source
2025-09-16 14:30:52.375 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: import_time
2025-09-16 14:30:52.391 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:494 | 成功创建异动数据表: change_data_2025_12_all_active
2025-09-16 14:30:52.391 | INFO     | src.modules.data_storage.dynamic_table_manager:_add_table_metadata:1420 | 🔧 [P0修复] 异动表元数据保存成功: change_data_2025_12_all_active -> all_active
2025-09-16 14:30:52.391 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:500 | 🔧 [元数据修复] 异动表元数据保存成功: change_data_2025_12_all_active
2025-09-16 14:30:52.391 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2723 | 🔧 [P0-CRITICAL修复] 异动表 change_data_2025_12_all_active 保持原始中文字段名
2025-09-16 14:30:52.391 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2737 | [FIX] [修复标识] 保留未映射列: ['序号', '工号', '姓名', '部门名称', '人员类别代码', '人员类别', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '住房补贴', '车补', '通讯补贴', '2025年奖励性绩效预发', '补发', '借支', '应发工资', '2025公积金', '代扣代存养老保险', 'data_source', 'import_time']
2025-09-16 14:30:52.391 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2746 | [FIX] [修复标识] 导入列名映射成功: 25 个字段已映射
2025-09-16 14:30:52.406 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2780 | 🔧 [P0-CRITICAL修复] 异动表 change_data_2025_12_all_active 清理列名（移除换行符但保持中文）
2025-09-16 14:30:52.406 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2785 | 🔧 清理后的列名: ['序号', '工号', '姓名', '部门名称', '人员类别代码', '人员类别', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴']...
2025-09-16 14:30:52.406 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2902 | 🔧 [方案A3] 创建系统字段：复制'工号'到'employee_id'
2025-09-16 14:30:52.406 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2925 | 🔧 [P0-CRITICAL修复] 异动表 change_data_2025_12_all_active 只添加系统字段
2025-09-16 14:30:52.453 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:3015 | [TX] 验证成功：表 change_data_2025_12_all_active 中有 1396 条记录
2025-09-16 14:30:52.453 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:3022 | [TX] 异动表数据采样验证通过
2025-09-16 14:30:52.453 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:3025 | 成功向表 change_data_2025_12_all_active 保存 1396 条数据。
2025-09-16 14:30:52.453 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-16 14:30:52.641 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-16 14:30:52.641 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-16 14:30:52.641 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-16 14:30:52.641 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-09-16 14:30:52.641 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:812 | 工作表 A岗职工 使用智能默认配置
2025-09-16 14:30:52.641 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:1146 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-09-16 14:30:52.641 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:860 | 未找到相似配置，使用默认映射: 21 个字段
2025-09-16 14:30:52.641 | INFO     | src.modules.data_import.multi_sheet_importer:_save_unified_change_data_mapping:1406 | 🔧 [P1-1修复] 异动表统一映射保存成功: change_data_2025_12_a_grade_employees, 21个字段
2025-09-16 14:30:52.657 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:922 | 为表 change_data_2025_12_a_grade_employees 生成标准化字段映射: 27 个字段
2025-09-16 14:30:52.657 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:943 | Sheet A岗职工 数据处理完成: 62 行
2025-09-16 14:30:52.657 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:585 | 🔧 [P2-1修复] 检测到异动表导入: 异动人员表
2025-09-16 14:30:52.657 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:600 | 🔧 [P2-1修复] 异动表使用动态模式
2025-09-16 14:30:52.657 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:665 | [P0] 使用英文名生成表名: change_data_2025_12_a_grade | source=ui_selection
2025-09-16 14:30:52.657 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 序号
2025-09-16 14:30:52.657 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 工号
2025-09-16 14:30:52.657 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 姓名
2025-09-16 14:30:52.657 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 部门名称
2025-09-16 14:30:52.672 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 人员类别
2025-09-16 14:30:52.672 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 人员类别代码
2025-09-16 14:30:52.672 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 2025年岗位工资
2025-09-16 14:30:52.672 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 2025年校龄工资
2025-09-16 14:30:52.672 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 津贴
2025-09-16 14:30:52.672 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 结余津贴
2025-09-16 14:30:52.672 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 2025年基础性绩效
2025-09-16 14:30:52.672 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 卫生费
2025-09-16 14:30:52.672 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 2025年生活补贴
2025-09-16 14:30:52.672 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 车补
2025-09-16 14:30:52.672 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 2025年奖励性绩效预发
2025-09-16 14:30:52.672 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 补发
2025-09-16 14:30:52.672 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 借支
2025-09-16 14:30:52.672 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 应发工资
2025-09-16 14:30:52.672 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 2025公积金
2025-09-16 14:30:52.672 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 保险扣款
2025-09-16 14:30:52.672 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: 代扣代存养老保险
2025-09-16 14:30:52.672 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: data_source
2025-09-16 14:30:52.672 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:452 | 🔧 [P0修复] 添加用户自定义字段: import_time
2025-09-16 14:30:52.672 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:494 | 成功创建异动数据表: change_data_2025_12_a_grade
2025-09-16 14:30:52.672 | INFO     | src.modules.data_storage.dynamic_table_manager:_add_table_metadata:1420 | 🔧 [P0修复] 异动表元数据保存成功: change_data_2025_12_a_grade -> a_grade
2025-09-16 14:30:52.672 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:500 | 🔧 [元数据修复] 异动表元数据保存成功: change_data_2025_12_a_grade
2025-09-16 14:30:52.672 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2723 | 🔧 [P0-CRITICAL修复] 异动表 change_data_2025_12_a_grade 保持原始中文字段名
2025-09-16 14:30:52.672 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2737 | [FIX] [修复标识] 保留未映射列: ['序号', '工号', '姓名', '部门名称', '人员类别', '人员类别代码', '2025年岗位工资', '2025年校龄工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '2025年生活补贴', '车补', '2025年奖励性绩效预发', '补发', '借支', '应发工资', '2025公积金', '保险扣款', '代扣代存养老保险', 'data_source', 'import_time']
2025-09-16 14:30:52.688 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2746 | [FIX] [修复标识] 导入列名映射成功: 23 个字段已映射
2025-09-16 14:30:52.688 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2780 | 🔧 [P0-CRITICAL修复] 异动表 change_data_2025_12_a_grade 清理列名（移除换行符但保持中文）
2025-09-16 14:30:52.688 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2785 | 🔧 清理后的列名: ['序号', '工号', '姓名', '部门名称', '人员类别', '人员类别代码', '2025年岗位工资', '2025年校龄工资', '津贴', '结余津贴']...
2025-09-16 14:30:52.688 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2902 | 🔧 [方案A3] 创建系统字段：复制'工号'到'employee_id'
2025-09-16 14:30:52.703 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2925 | 🔧 [P0-CRITICAL修复] 异动表 change_data_2025_12_a_grade 只添加系统字段
2025-09-16 14:30:52.703 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:3015 | [TX] 验证成功：表 change_data_2025_12_a_grade 中有 62 条记录
2025-09-16 14:30:52.703 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:3022 | [TX] 异动表数据采样验证通过
2025-09-16 14:30:52.703 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:3025 | 成功向表 change_data_2025_12_a_grade 保存 62 条数据。
2025-09-16 14:30:52.703 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:359 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 change_data_2025_12_retired 保存 2 条数据。', 'table_name': 'change_data_2025_12_retired', 'records': 2, 'filtered_empty_employee_id': 0}, '退休人员工资表': {'success': True, 'message': '成功向表 change_data_2025_12_pension 保存 13 条数据。', 'table_name': 'change_data_2025_12_pension', 'records': 13, 'filtered_empty_employee_id': 0}, '全部在职人员工资表': {'success': True, 'message': '成功向表 change_data_2025_12_all_active 保存 1396 条数据。', 'table_name': 'change_data_2025_12_all_active', 'records': 1396, 'filtered_empty_employee_id': 0}, 'A岗职工': {'success': True, 'message': '成功向表 change_data_2025_12_a_grade 保存 62 条数据。', 'table_name': 'change_data_2025_12_a_grade', 'records': 62, 'filtered_empty_employee_id': 0}}, 'total_records': 1473, 'year': 2025, 'month': 12, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-09-16 14:30:52.703 | INFO     | src.gui.workers.data_import_worker:run:199 | 🔧 [P2修复] 异步数据导入成功完成，已发送完成信号
2025-09-16 14:30:54.307 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3874 | 📥[data_event] received | table= | request_id=None
2025-09-16 14:30:54.309 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3906 | 数据内容: 0行 x 0列
2025-09-16 14:30:54.310 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3937 | 🔧 [数据更新] 接收到操作的数据更新事件: , 0行
2025-09-16 14:30:54.311 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4077 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-09-16 14:30:54.312 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:6094 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 change_data_2025_12_retired 保存 2 条数据。', 'table_name': 'change_data_2025_12_retired', 'records': 2, 'filtered_empty_employee_id': 0}, '退休人员工资表': {'success': True, 'message': '成功向表 change_data_2025_12_pension 保存 13 条数据。', 'table_name': 'change_data_2025_12_pension', 'records': 13, 'filtered_empty_employee_id': 0}, '全部在职人员工资表': {'success': True, 'message': '成功向表 change_data_2025_12_all_active 保存 1396 条数据。', 'table_name': 'change_data_2025_12_all_active', 'records': 1396, 'filtered_empty_employee_id': 0}, 'A岗职工': {'success': True, 'message': '成功向表 change_data_2025_12_a_grade 保存 62 条数据。', 'table_name': 'change_data_2025_12_a_grade', 'records': 62, 'filtered_empty_employee_id': 0}}, 'total_records': 1473, 'year': 2025, 'month': 12, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'message': '导入完成！共导入 1473 条记录到 4 个表，涉及 4 个Sheet', 'import_mode': 'separate_tables', 'target_path': '异动人员表', 'source_file': '2025年5月份正式工工资（报财务)  最终版.xls'}
2025-09-16 14:30:54.313 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:6108 | 导入模式: separate_tables, 目标路径: '异动人员表'
2025-09-16 14:30:54.314 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:6126 | 接收到导入数据, 来源: 2025年5月份正式工工资（报财务)  最终版.xls, 目标路径: 异动人员表
2025-09-16 14:30:54.314 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:2084 | 🔧 [P2-2] 开始增强版导航数据刷新...
2025-09-16 14:30:54.317 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月', '工资表 > 2025年', '工资表']
2025-09-16 14:30:54.318 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7713 | 导航变化: 工资表 > 2025年 > 05月 > 全部在职人员
2025-09-16 14:30:54.325 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10932 | 🚫 [用户要求] 表格切换不显示加载条:  -> None
2025-09-16 14:30:54.330 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10948 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-09-16 14:30:54.331 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8724 | 🔧 [表名生成] 命中最近导入映射: ('工资表', '2025', '05', '全部在职人员') -> salary_data_2025_05_all_active
2025-09-16 14:30:54.332 | INFO     | src.gui.prototype.prototype_main_window:_process_navigation_change:7775 | 🔧 [P0-紧急修复] 导航变化时更新状态栏: 工资表 > 2025年 > 05月 > 全部在职人员
2025-09-16 14:30:54.333 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:384 | 已清理表 salary_data_2025_05_all_active 的缓存
2025-09-16 14:30:54.343 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11620 | 已注册 4 个表格到表头管理器
2025-09-16 14:30:54.344 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1197 | 🔧 [P1-3] 开始智能表头重影检测，共 5 个表格
2025-09-16 14:30:54.344 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1270 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-09-16 14:30:54.345 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1271 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-09-16 14:30:54.345 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7934 | 🆕 使用新架构加载数据: salary_data_2025_05_all_active（通过事件系统）
2025-09-16 14:30:54.347 | INFO     | src.services.table_data_service:load_table_data:377 | 加载表格数据: 页码: 1
2025-09-16 14:30:54.347 | INFO     | src.services.table_data_service:load_table_data:401 | [缓存命中] 使用缓存数据: 第1页
2025-09-16 14:30:54.348 | INFO     | src.services.table_data_service:load_table_data:412 | 📨[data_event] publish(cache) | table=salary_data_2025_05_all_active | rows=50 | page=1 | size=50 | total=1396 | request_id=SV-1758004254348-f469ab65-C
2025-09-16 14:30:54.348 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3874 | 📥[data_event] received | table=salary_data_2025_05_all_active | request_id=SV-1758004254348-f469ab65-C
2025-09-16 14:30:54.349 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3906 | 数据内容: 50行 x 28列
2025-09-16 14:30:54.355 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3937 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_05_all_active, 50行
2025-09-16 14:30:54.356 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3952 | 🔧 [P0-修复] 排序数据处理前: 28列 - ['工号', '姓名', '部门名称', '人员类别', '人员类别代码']...
2025-09-16 14:30:54.357 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:30:54.358 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:30:54.360 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔧 [P0-修复] 字段映射后: 28列
2025-09-16 14:30:54.362 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:7617 | 🔧 [P0-修复] 系统字段过滤: 隐藏 5 个字段 - ['年份', '月份', '序号', 'data_source', 'import_time']
2025-09-16 14:30:54.362 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3956 | 🔧 [P0-修复] 系统字段过滤后: 25列
2025-09-16 14:30:54.363 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:30:54.370 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:30:54.371 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:30:54.371 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7670 | 表 salary_data_2025_05_all_active 没有用户偏好设置，显示所有可见字段
2025-09-16 14:30:54.372 | INFO     | src.gui.prototype.prototype_main_window:set_data:922 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-09-16 14:30:54.373 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:30:54.374 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:30:54.385 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_05_all_active
2025-09-16 14:30:54.386 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引1, 名称'工号'
2025-09-16 14:30:54.387 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引2, 名称'工号'
2025-09-16 14:30:54.387 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引3, 名称'工号'
2025-09-16 14:30:54.387 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引4, 名称'工号'
2025-09-16 14:30:54.388 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引5, 名称'工号'
2025-09-16 14:30:54.388 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引6, 名称'工号'
2025-09-16 14:30:54.389 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引7, 名称'工号'
2025-09-16 14:30:54.389 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引24, 名称'工号'
2025-09-16 14:30:54.390 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引25, 名称'工号'
2025-09-16 14:30:54.396 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引26, 名称'工号'
2025-09-16 14:30:54.397 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引27, 名称'工号'
2025-09-16 14:30:54.397 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引28, 名称'工号'
2025-09-16 14:30:54.398 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引29, 名称'工号'
2025-09-16 14:30:54.398 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引30, 名称'工号'
2025-09-16 14:30:54.399 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引31, 名称'工号'
2025-09-16 14:30:54.399 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4899 | 🔧 [方案A2] 表头去重完成: 37 -> 22
2025-09-16 14:30:54.399 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4902 | 🔧 [方案A2] 移除的重复项: 工号(索引1), 工号(索引2), 工号(索引3), 工号(索引4), 工号(索引5), 工号(索引6), 工号(索引7), 工号(索引24), 工号(索引25), 工号(索引26), 工号(索引27), 工号(索引28), 工号(索引29), 工号(索引30), 工号(索引31)
2025-09-16 14:30:54.400 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089, 薪资=2375
2025-09-16 14:30:54.401 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565, 薪资=1696
2025-09-16 14:30:54.401 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5528 | 最大可见行数已更新: 50 -> 50
2025-09-16 14:30:54.401 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5581 | 根据数据量(50)自动调整最大可见行数为: 50
2025-09-16 14:30:54.404 | INFO     | src.modules.format_management.format_renderer:render_dataframe:127 | 🎯 [格式渲染] 隐藏字段配置: ['created_at', 'updated_at', 'id', 'sequence_number', 'row_number']
2025-09-16 14:30:54.404 | INFO     | src.modules.format_management.format_renderer:render_dataframe:135 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-09-16 14:30:54.418 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-09-16 14:30:54.419 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-09-16 14:30:54.426 | INFO     | src.modules.format_management.field_registry:get_display_fields:1582 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-09-16 14:30:54.426 | INFO     | src.modules.format_management.format_renderer:render_dataframe:186 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-09-16 14:30:54.427 | INFO     | src.modules.format_management.format_renderer:render_dataframe:244 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 22/25 个字段
2025-09-16 14:30:54.428 | INFO     | src.modules.format_management.format_renderer:render_dataframe:257 | 🔧 [P0修复] 字段重排完成: 原始22个 -> 最终22个字段
2025-09-16 14:30:54.429 | INFO     | src.modules.format_management.format_renderer:render_dataframe:273 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 22
2025-09-16 14:30:54.429 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 22
2025-09-16 14:30:54.448 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-09-16 14:30:54.449 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 22列
2025-09-16 14:30:54.459 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时9.8ms, 平均每行0.20ms
2025-09-16 14:30:54.459 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=9.8ms, 策略=small_dataset
2025-09-16 14:30:54.462 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-09-16 14:30:54.462 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第0行数据工号: 19990089
2025-09-16 14:30:54.463 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第1行数据工号: 20161565
2025-09-16 14:30:54.463 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第2行数据工号: 20191782
2025-09-16 14:30:54.464 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第3行数据工号: 20151515
2025-09-16 14:30:54.464 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第4行数据工号: 20181640
2025-09-16 14:30:54.465 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-09-16 14:30:54.465 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=2375.00
2025-09-16 14:30:54.466 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=1696.00
2025-09-16 14:30:54.466 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=1427.00
2025-09-16 14:30:54.466 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=2175.00
2025-09-16 14:30:54.468 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=2582.00
2025-09-16 14:30:54.468 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:488 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-09-16 14:30:54.468 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:447 | 表格数据已设置: 50 行, 22 列
2025-09-16 14:30:54.469 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 50 行, 耗时: 69.0ms
2025-09-16 14:30:54.470 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 14:30:54.470 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 14:30:54.472 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1736 | 🔧 [P2-3修复] 列宽已恢复: salary_data_2025_05_all_active (19/22 列, 表头数量: 19)
2025-09-16 14:30:54.480 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 14:30:54.481 | INFO     | src.gui.prototype.prototype_main_window:set_data:1047 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=0，等待数据事件纠正
2025-09-16 14:30:54.482 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3984 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 25列
2025-09-16 14:30:54.483 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 25 个, 行号起始 1, 共 50 行
2025-09-16 14:30:54.485 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4000 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_05_all_active, 传递参数: 25个表头
2025-09-16 14:30:54.486 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4004 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 25列
2025-09-16 14:30:54.546 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:401 | 已清理所有缓存
2025-09-16 14:30:54.547 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:11427 | ✅ [新架构] 组件状态一致性验证通过
2025-09-16 14:30:54.549 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:11390 | 🆕 [新架构] 导航迁移完成
2025-09-16 14:30:54.549 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4077 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-09-16 14:30:54.550 | INFO     | src.services.table_data_service:load_table_data:437 | 📨[data_event] published(cache)
2025-09-16 14:30:54.550 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7939 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-09-16 14:30:54.551 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:1028 | 导航选择: 工资表 > 2025年 > 05月 > 全部在职人员
2025-09-16 14:30:54.558 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_execute_salary_data_load:2055 | 动态加载了 1 个月份的工资数据导航
2025-09-16 14:30:54.588 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1619 | 🔧 [P1修复] 找到 4 个匹配类型 'change_data' 的表 (尝试 1/3)
2025-09-16 14:30:54.595 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月', '工资表 > 2025年', '工资表']
2025-09-16 14:30:54.596 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:893 | 导航面板已重构：移除功能性导航，专注数据导航
2025-09-16 14:30:54.596 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:2179 | 🔧 [P2修复] 开始恢复导航状态...
2025-09-16 14:30:54.639 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:2207 | 🔧 [P2修复] 导航状态恢复完成: 4个展开项
2025-09-16 14:30:54.640 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:2132 | 🔧 [P2-2] 增强版导航数据刷新完成
2025-09-16 14:30:54.643 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2131 | MainWorkspaceArea 响应式适配: sm
2025-09-16 14:30:54.647 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:2084 | 🔧 [P2-2] 开始增强版导航数据刷新...
2025-09-16 14:30:54.651 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:893 | 导航面板已重构：移除功能性导航，专注数据导航
2025-09-16 14:30:54.655 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:2179 | 🔧 [P2修复] 开始恢复导航状态...
2025-09-16 14:30:54.656 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:2207 | 🔧 [P2修复] 导航状态恢复完成: 0个展开项
2025-09-16 14:30:54.656 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1963 | 无需执行自动选择，跳过
2025-09-16 14:30:54.657 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:2132 | 🔧 [P2-2] 增强版导航数据刷新完成
2025-09-16 14:30:54.657 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2131 | MainWorkspaceArea 响应式适配: sm
2025-09-16 14:30:54.662 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2131 | MainWorkspaceArea 响应式适配: sm
2025-09-16 14:30:54.666 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1399 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-09-16 14:30:54.667 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2658 | 找到最新工资数据路径: 工资表 > 2025年 > 05月 > 全部在职人员
2025-09-16 14:30:54.670 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:6281 | separate_tables 模式下目标路径为概览路径，跳过路径完整性检查
2025-09-16 14:30:54.671 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:30:54.677 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:30:54.680 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:30:54.680 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:30:54.684 | INFO     | src.modules.system_config.config_manager:_create_backup:510 | 配置文件备份已创建: C:\test\salary_changes\salary_changes\config_backups\config_20250916_143054.json
2025-09-16 14:30:54.685 | INFO     | src.modules.system_config.config_manager:save_config:413 | 正在保存配置文件: C:\test\salary_changes\salary_changes\config.json
2025-09-16 14:30:54.717 | INFO     | src.modules.system_config.config_manager:save_config:418 | 配置文件保存成功
2025-09-16 14:30:54.717 | INFO     | src.modules.system_config.config_manager:save_configuration:568 | 配置保存成功: import_config_异动表_21fields
2025-09-16 14:30:54.725 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed:4939 | 🔧 [P1-强化] 延迟调整时发现列数不匹配: 期望22列, 实际25列
2025-09-16 14:30:54.726 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed:4956 | 🔧 [P1-强化] 最终列数修复: 22列
2025-09-16 14:30:54.726 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths:4763 | 🔧 [P0-1修复] 列数不匹配: 期望22列, 实际25列
2025-09-16 14:30:54.727 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths:4768 | 🔧 [P0-1修复] 删除多余空白列: 25 -> 22
2025-09-16 14:30:54.727 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths:4771 | 🔧 [P0-1修复] 列数修复完成: 22列
2025-09-16 14:30:54.728 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_05_all_active
2025-09-16 14:30:54.781 | WARNING  | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2429 | 🔧 [方案A1] 发现重复表头: 列1(工号)，原始列在0
2025-09-16 14:30:54.782 | WARNING  | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2429 | 🔧 [方案A1] 发现重复表头: 列18(工号)，原始列在0
2025-09-16 14:30:54.784 | WARNING  | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2429 | 🔧 [方案A1] 发现重复表头: 列19(工号)，原始列在0
2025-09-16 14:30:54.784 | INFO     | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2440 | 🔧 [方案A1] 检测到3个重复表头，开始移除
2025-09-16 14:30:54.786 | INFO     | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2448 | 🔧 [方案A1] 已移除重复列: 列19(工号)
2025-09-16 14:30:54.786 | INFO     | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2448 | 🔧 [方案A1] 已移除重复列: 列18(工号)
2025-09-16 14:30:54.788 | INFO     | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2448 | 🔧 [方案A1] 已移除重复列: 列1(工号)
2025-09-16 14:30:54.799 | INFO     | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2464 | 🔧 [方案A1] 表格显示已刷新，列宽已调整
2025-09-16 14:30:55.470 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:6373 | 检查是否需要更新导航面板: ['异动人员表']
2025-09-16 14:30:55.471 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:6411 | 检测到异动表数据导入，路径层次: 1, 开始刷新导航面板
2025-09-16 14:30:55.474 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:2084 | 🔧 [P2-2] 开始增强版导航数据刷新...
2025-09-16 14:30:55.478 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:893 | 导航面板已重构：移除功能性导航，专注数据导航
2025-09-16 14:30:55.478 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:2179 | 🔧 [P2修复] 开始恢复导航状态...
2025-09-16 14:30:55.479 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:2207 | 🔧 [P2修复] 导航状态恢复完成: 0个展开项
2025-09-16 14:30:55.480 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1963 | 无需执行自动选择，跳过
2025-09-16 14:30:55.480 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:2132 | 🔧 [P2-2] 增强版导航数据刷新完成
2025-09-16 14:30:55.481 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:6416 | 导航面板已刷新
2025-09-16 14:30:55.481 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:6427 | 🔧 [P2优化] 使用最新异动数据路径: 异动人员表 > 2025年 > 12月 > A岗职工
2025-09-16 14:30:55.482 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:6451 | 🔧 [P2优化] 异动表路径补全: ['异动人员表'] -> ['异动人员表', '2025年', '12月', 'A岗职工']
2025-09-16 14:30:55.488 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:6455 | 导航到异动表路径: 异动人员表 > 2025年 > 12月 > A岗职工
2025-09-16 14:30:55.488 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:6460 | 已切换到异动表TAB
2025-09-16 14:30:55.489 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8556 | 🔧 [表名生成] 处理异动表路径: ['异动人员表']
2025-09-16 14:30:55.489 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8637 | 🔧 [P0-紧急修复] 异动表路径层次较少，使用最新表: change_data_2025_12_retired
2025-09-16 14:30:55.490 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:6469 | 🔧 [修复] 异动表导入后更新表名: change_data_2025_12_retired
2025-09-16 14:30:55.789 | INFO     | src.gui.prototype.prototype_main_window:_force_navigate_after_import:6538 | 🔧 [修复] 强制导航到导入路径: 异动人员表 > 2025年 > 12月 > A岗职工
2025-09-16 14:30:55.791 | INFO     | src.gui.prototype.prototype_main_window:_force_navigate_after_import:6543 | 🔧 [P0-紧急修复] 导入后立即更新状态栏: 异动人员表 > 2025年 > 12月 > A岗职工
2025-09-16 14:30:55.792 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:2084 | 🔧 [P2-2] 开始增强版导航数据刷新...
2025-09-16 14:30:55.796 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:893 | 导航面板已重构：移除功能性导航，专注数据导航
2025-09-16 14:30:55.798 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:2179 | 🔧 [P2修复] 开始恢复导航状态...
2025-09-16 14:30:55.798 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:2207 | 🔧 [P2修复] 导航状态恢复完成: 0个展开项
2025-09-16 14:30:55.798 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1963 | 无需执行自动选择，跳过
2025-09-16 14:30:55.799 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:2132 | 🔧 [P2-2] 增强版导航数据刷新完成
2025-09-16 14:30:55.799 | INFO     | src.gui.prototype.prototype_main_window:_force_navigate_after_import:6548 | 导航面板数据已刷新
2025-09-16 14:30:55.800 | INFO     | src.gui.prototype.prototype_main_window:_force_navigate_after_import:6624 | 已切换到异动表TAB
2025-09-16 14:30:55.800 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8556 | 🔧 [表名生成] 处理异动表路径: ['异动人员表', '2025年', '12月', 'A岗职工']
2025-09-16 14:30:55.800 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8577 | 🔧 [表名生成] 命中最近导入映射: ('异动人员表', '2025', '12', 'A岗职工') -> change_data_2025_12_a_grade
2025-09-16 14:30:55.807 | INFO     | src.gui.prototype.prototype_main_window:_force_navigate_after_import:6630 | 🔧 [修复] 更新当前表名: change_data_2025_12_a_grade
2025-09-16 14:30:56.291 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:6760 | [数据流追踪] 开始智能数据显示刷新: change_data_2025_12_retired
2025-09-16 14:30:56.291 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:6765 | 🔧 [P1修复] 刷新数据时更新表名: change_data_2025_12_retired
2025-09-16 14:30:56.294 | INFO     | src.core.smart_pagination_strategy:should_use_pagination:68 | [数据流追踪] 分页决策输入: 总记录数=2, 页面大小=50, 用户偏好=None
2025-09-16 14:30:56.294 | INFO     | src.core.smart_pagination_strategy:should_use_pagination:93 | [数据流追踪] 分页决策输出: 策略=full_display, 原因=数据量(2条) <= 页面大小(50条), 预期性能=10ms, 决策耗时=0ms
2025-09-16 14:30:56.295 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:6787 | [数据流追踪] 智能分页策略决策: full_display, 原因=数据量(2条) <= 页面大小(50条), 预期性能=10ms
2025-09-16 14:30:56.295 | INFO     | src.gui.prototype.prototype_main_window:_execute_full_display_mode:6820 | [数据流追踪] 执行全量显示模式: change_data_2025_12_retired, 2条记录
2025-09-16 14:30:56.295 | WARNING  | src.gui.prototype.prototype_main_window:_set_pagination_visibility:6893 | 🔧 [P0-修复] 分页组件不存在，跳过可见性设置
2025-09-16 14:30:56.296 | INFO     | src.services.table_data_service:load_table_data:377 | 加载表格数据: 页码: 1
2025-09-16 14:30:56.297 | INFO     | src.core.unified_data_request_manager:request_table_data:237 | 开始处理数据请求
2025-09-16 14:30:56.300 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:899 | 正在从表 change_data_2025_12_retired 分页获取数据（支持排序）: 第1页, 每页100条, 排序=0列
2025-09-16 14:30:56.303 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:1019 | 成功从表 change_data_2025_12_retired 获取第1页数据（含排序）: 2 行，总计2行
2025-09-16 14:30:56.308 | INFO     | src.core.unified_data_request_manager:request_table_data:287 | 数据请求处理完成: 字段=24, 行数=2, 耗时=10.9ms
2025-09-16 14:30:56.308 | INFO     | src.services.table_data_service:load_table_data:476 | [新架构] 使用统一格式管理器（单例优化）
2025-09-16 14:30:56.309 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1438 | 🔧 [智能分析] 开始异动表智能渲染: change_data
2025-09-16 14:30:56.309 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1447 | 🔧 [智能分析] 执行新的结构分析
2025-09-16 14:30:56.309 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:92 | 开始分析Excel结构: change_data
2025-09-16 14:30:56.310 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:93 | 数据维度: 2 行 x 24 列
2025-09-16 14:30:56.414 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:149 | Excel结构分析完成
2025-09-16 14:30:56.414 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:150 | 识别字段类型: 24 个
2025-09-16 14:30:56.415 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:151 | 整体数据质量得分: 0.80
2025-09-16 14:30:56.415 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1456 | 🔧 [智能分析] 分析建议: ['以下字段类型识别置信度较低，建议手动确认: 基本\n离休费, 补发, 借支, 备注', '以下字段存在大量空值: 基本\n离休费, 结余\n津贴, 生活\n补贴, 住房\n补贴, 物业\n补贴']
2025-09-16 14:30:56.416 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1474 | 🔧 [智能分析] 已隐藏系统字段: ['import_time', 'id', 'created_at', 'data_source', 'updated_at']
2025-09-16 14:30:56.426 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1507 | 🔧 [智能分析] 数据质量评分: 0.80
2025-09-16 14:30:56.428 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1512 | 🔧 [智能分析] 异动表渲染完成: (2, 19)
2025-09-16 14:30:56.428 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: change_data, 行数: 2, 列数: 19
2025-09-16 14:30:56.430 | INFO     | src.core.unified_state_manager:update_table_state:233 | 表状态已更新: change_data_2025_12_retired, 变更类型: None
2025-09-16 14:30:56.431 | INFO     | src.services.table_data_service:load_table_data:528 | [修复数据发布] 数据加载成功，发布更新事件: 2行
2025-09-16 14:30:56.431 | INFO     | src.services.table_data_service:load_table_data:532 | 📨[data_event] publish | table=change_data_2025_12_retired | rows=2 | page=1 | size=100 | total=2 | req_type=RequestType.INITIAL_LOAD | request_id=SV-1758004256431-8ddb0638
2025-09-16 14:30:56.432 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3874 | 📥[data_event] received | table=change_data_2025_12_retired | request_id=SV-1758004256431-8ddb0638
2025-09-16 14:30:56.432 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3906 | 数据内容: 2行 x 19列
2025-09-16 14:30:56.433 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3937 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: change_data_2025_12_retired, 2行
2025-09-16 14:30:56.433 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3952 | 🔧 [P0-修复] 排序数据处理前: 19列 - ['工号', '月份', '年份', '序号', '人员代码']...
2025-09-16 14:30:56.434 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:30:56.435 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:30:56.436 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔧 [P0-修复] 字段映射后: 19列
2025-09-16 14:30:56.445 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:7617 | 🔧 [P0-修复] 系统字段过滤: 隐藏 3 个字段 - ['月份', '年份', '序号']
2025-09-16 14:30:56.445 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3956 | 🔧 [P0-修复] 系统字段过滤后: 16列
2025-09-16 14:30:56.446 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:30:56.453 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:30:56.456 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:30:56.456 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7670 | 表 change_data_2025_12_retired 没有用户偏好设置，显示所有可见字段
2025-09-16 14:30:56.457 | INFO     | src.gui.prototype.prototype_main_window:set_data:922 | 通过公共接口设置新的表格数据(DataFrame): 2 行
2025-09-16 14:30:56.458 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:30:56.459 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:30:56.466 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 2行，表名: change_data_2025_12_retired
2025-09-16 14:30:56.469 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7393 | 方案A：安全设置列数: 0
2025-09-16 14:30:56.470 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2822 | 🔧 [P1优化] 表切换操作: salary_data_2025_05_all_active -> change_data_2025_12_retired
2025-09-16 14:30:56.471 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19289006, 薪资=N/A
2025-09-16 14:30:56.472 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=19339009, 薪资=N/A
2025-09-16 14:30:56.479 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5528 | 最大可见行数已更新: 50 -> 10
2025-09-16 14:30:56.480 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5581 | 根据数据量(2)自动调整最大可见行数为: 10
2025-09-16 14:30:56.483 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1438 | 🔧 [智能分析] 开始异动表智能渲染: change_data
2025-09-16 14:30:56.483 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1447 | 🔧 [智能分析] 执行新的结构分析
2025-09-16 14:30:56.483 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:92 | 开始分析Excel结构: change_data
2025-09-16 14:30:56.484 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:93 | 数据维度: 2 行 x 16 列
2025-09-16 14:30:56.522 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:149 | Excel结构分析完成
2025-09-16 14:30:56.523 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:150 | 识别字段类型: 16 个
2025-09-16 14:30:56.523 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:151 | 整体数据质量得分: 0.94
2025-09-16 14:30:56.523 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1456 | 🔧 [智能分析] 分析建议: ['数据质量良好，可以正常处理']
2025-09-16 14:30:56.538 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1507 | 🔧 [智能分析] 数据质量评分: 0.94
2025-09-16 14:30:56.538 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1512 | 🔧 [智能分析] 异动表渲染完成: (2, 16)
2025-09-16 14:30:56.539 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: change_data, 行数: 2, 列数: 16
2025-09-16 14:30:56.541 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2983 | [列对齐] 已对齐记录与表头: rows=2->2, cols=16
2025-09-16 14:30:56.547 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 2行数据
2025-09-16 14:30:56.547 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 2行 x 16列
2025-09-16 14:30:56.548 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时1.0ms, 平均每行0.51ms
2025-09-16 14:30:56.548 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=2, 渲染时间=1.0ms, 策略=small_dataset
2025-09-16 14:30:56.549 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-09-16 14:30:56.549 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第0行数据工号: 19289006
2025-09-16 14:30:56.550 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第1行数据工号: 19339009
2025-09-16 14:30:56.550 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 2
2025-09-16 14:30:56.550 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[0]: 工号=19289006, 薪资=N/A
2025-09-16 14:30:56.551 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[1]: 工号=19339009, 薪资=N/A
2025-09-16 14:30:56.551 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:488 | 可见行数据顺序: ['19289006', '19339009']
2025-09-16 14:30:56.551 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:447 | 表格数据已设置: 2 行, 16 列
2025-09-16 14:30:56.555 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: change_data_2025_12_retired
2025-09-16 14:30:56.561 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:474 | 🔧 [新架构] 为表格 change_data_2025_12_retired 重新加载 29 个字段映射
2025-09-16 14:30:56.562 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 2 行, 耗时: 85.6ms
2025-09-16 14:30:56.562 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 14:30:56.563 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 14:30:56.564 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1708 | 🆕 [列宽保存] 未找到表格 change_data_2025_12_retired 的列宽配置
2025-09-16 14:30:56.564 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 14:30:56.565 | INFO     | src.gui.prototype.prototype_main_window:set_data:1047 | 📊[pagination-write] 跳过写入当前页数据量(total=2)，保持 total=0，等待数据事件纠正
2025-09-16 14:30:56.566 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3984 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 2行, 16列
2025-09-16 14:30:56.566 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 16 个, 行号起始 1, 共 2 行
2025-09-16 14:30:56.567 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4000 | 🔧 [P0-新5修复] 表头强制刷新完成: change_data_2025_12_retired, 传递参数: 16个表头
2025-09-16 14:30:56.567 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4004 | 🔧 [统一数据设置] 数据已成功设置到UI: 2行, 16列
2025-09-16 14:30:56.568 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4077 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-09-16 14:30:56.568 | INFO     | src.services.table_data_service:load_table_data:555 | 📨[data_event] published
2025-09-16 14:30:56.568 | INFO     | src.gui.prototype.prototype_main_window:_execute_full_display_mode:6844 | [数据流追踪] 全量显示模式加载成功: 2行数据
2025-09-16 14:30:56.576 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:6760 | [数据流追踪] 开始智能数据显示刷新: change_data_2025_12_a_grade
2025-09-16 14:30:56.577 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:6765 | 🔧 [P1修复] 刷新数据时更新表名: change_data_2025_12_a_grade
2025-09-16 14:30:56.577 | INFO     | src.core.smart_pagination_strategy:should_use_pagination:68 | [数据流追踪] 分页决策输入: 总记录数=62, 页面大小=50, 用户偏好=None
2025-09-16 14:30:56.578 | INFO     | src.core.smart_pagination_strategy:should_use_pagination:93 | [数据流追踪] 分页决策输出: 策略=full_display, 原因=小数据集(62条) <= 阈值(75条), 预期性能=300ms, 决策耗时=1ms
2025-09-16 14:30:56.578 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:6787 | [数据流追踪] 智能分页策略决策: full_display, 原因=小数据集(62条) <= 阈值(75条), 预期性能=300ms
2025-09-16 14:30:56.579 | INFO     | src.gui.prototype.prototype_main_window:_execute_full_display_mode:6820 | [数据流追踪] 执行全量显示模式: change_data_2025_12_a_grade, 62条记录
2025-09-16 14:30:56.579 | WARNING  | src.gui.prototype.prototype_main_window:_set_pagination_visibility:6893 | 🔧 [P0-修复] 分页组件不存在，跳过可见性设置
2025-09-16 14:30:56.581 | INFO     | src.core.unified_data_request_manager:request_table_data:237 | 开始处理数据请求
2025-09-16 14:30:56.584 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:899 | 正在从表 change_data_2025_12_a_grade 分页获取数据（支持排序）: 第1页, 每页100条, 排序=0列
2025-09-16 14:30:56.594 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:1019 | 成功从表 change_data_2025_12_a_grade 获取第1页数据（含排序）: 62 行，总计62行
2025-09-16 14:30:56.595 | INFO     | src.core.unified_data_request_manager:request_table_data:287 | 数据请求处理完成: 字段=29, 行数=62, 耗时=14.4ms
2025-09-16 14:30:56.596 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1438 | 🔧 [智能分析] 开始异动表智能渲染: change_data
2025-09-16 14:30:56.596 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1447 | 🔧 [智能分析] 执行新的结构分析
2025-09-16 14:30:56.597 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:92 | 开始分析Excel结构: change_data
2025-09-16 14:30:56.597 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:93 | 数据维度: 62 行 x 29 列
2025-09-16 14:30:56.664 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:149 | Excel结构分析完成
2025-09-16 14:30:56.665 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:150 | 识别字段类型: 29 个
2025-09-16 14:30:56.667 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:151 | 整体数据质量得分: 0.92
2025-09-16 14:30:56.667 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1456 | 🔧 [智能分析] 分析建议: ['以下字段类型识别置信度较低，建议手动确认: 车补, 补发, 保险扣款, 代扣代存养老保险', '以下字段存在大量空值: 车补, 补发, 借支, 保险扣款, 代扣代存养老保险', "字段 '人员类别代码' 存在重复值，可能影响数据准确性"]
2025-09-16 14:30:56.668 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1474 | 🔧 [智能分析] 已隐藏系统字段: ['import_time', 'id', 'employee_id', 'created_at', 'data_source', 'updated_at']
2025-09-16 14:30:56.682 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1507 | 🔧 [智能分析] 数据质量评分: 0.92
2025-09-16 14:30:56.682 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1512 | 🔧 [智能分析] 异动表渲染完成: (62, 23)
2025-09-16 14:30:56.683 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: change_data, 行数: 62, 列数: 23
2025-09-16 14:30:56.685 | INFO     | src.services.table_data_service:load_table_data:532 | 📨[data_event] publish | table=change_data_2025_12_a_grade | rows=62 | page=1 | size=100 | total=62 | req_type=RequestType.INITIAL_LOAD | request_id=SV-1758004256685-16be17d7
2025-09-16 14:30:56.685 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3874 | 📥[data_event] received | table=change_data_2025_12_a_grade | request_id=SV-1758004256685-16be17d7
2025-09-16 14:30:56.686 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3906 | 数据内容: 62行 x 23列
2025-09-16 14:30:56.687 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3937 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: change_data_2025_12_a_grade, 62行
2025-09-16 14:30:56.687 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3952 | 🔧 [P0-修复] 排序数据处理前: 23列 - ['月份', '年份', '序号', '工号', '姓名']...
2025-09-16 14:30:56.693 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:30:56.694 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:30:56.695 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔧 [P0-修复] 字段映射后: 23列
2025-09-16 14:30:56.697 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:7617 | 🔧 [P0-修复] 系统字段过滤: 隐藏 3 个字段 - ['月份', '年份', '序号']
2025-09-16 14:30:56.697 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3956 | 🔧 [P0-修复] 系统字段过滤后: 20列
2025-09-16 14:30:56.697 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:30:56.704 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:30:56.707 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:30:56.707 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7670 | 表 change_data_2025_12_a_grade 没有用户偏好设置，显示所有可见字段
2025-09-16 14:30:56.708 | INFO     | src.gui.prototype.prototype_main_window:set_data:922 | 通过公共接口设置新的表格数据(DataFrame): 62 行
2025-09-16 14:30:56.709 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:30:56.710 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:30:56.718 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 62行，表名: change_data_2025_12_a_grade
2025-09-16 14:30:56.719 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2822 | 🔧 [P1优化] 表切换操作: change_data_2025_12_retired -> change_data_2025_12_a_grade
2025-09-16 14:30:56.720 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=34660024, 薪资=N/A
2025-09-16 14:30:56.720 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20222002, 薪资=N/A
2025-09-16 14:30:56.720 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5528 | 最大可见行数已更新: 10 -> 62
2025-09-16 14:30:56.721 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5581 | 根据数据量(62)自动调整最大可见行数为: 62
2025-09-16 14:30:56.724 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1438 | 🔧 [智能分析] 开始异动表智能渲染: change_data
2025-09-16 14:30:56.724 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1447 | 🔧 [智能分析] 执行新的结构分析
2025-09-16 14:30:56.725 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:92 | 开始分析Excel结构: change_data
2025-09-16 14:30:56.725 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:93 | 数据维度: 62 行 x 20 列
2025-09-16 14:30:56.777 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:149 | Excel结构分析完成
2025-09-16 14:30:56.778 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:150 | 识别字段类型: 20 个
2025-09-16 14:30:56.778 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:151 | 整体数据质量得分: 0.91
2025-09-16 14:30:56.779 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1456 | 🔧 [智能分析] 分析建议: ["字段 '人员类别代码' 存在重复值，可能影响数据准确性"]
2025-09-16 14:30:56.796 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1507 | 🔧 [智能分析] 数据质量评分: 0.91
2025-09-16 14:30:56.798 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1512 | 🔧 [智能分析] 异动表渲染完成: (62, 20)
2025-09-16 14:30:56.799 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: change_data, 行数: 62, 列数: 20
2025-09-16 14:30:56.805 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7393 | 方案A：安全设置列数: 20
2025-09-16 14:30:56.823 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 62行数据
2025-09-16 14:30:56.824 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 62行 x 20列
2025-09-16 14:30:56.836 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时11.7ms, 平均每行0.19ms
2025-09-16 14:30:56.837 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=62, 渲染时间=11.7ms, 策略=small_dataset
2025-09-16 14:30:56.837 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-09-16 14:30:56.838 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第0行数据工号: 34660024
2025-09-16 14:30:56.838 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第1行数据工号: 20222002
2025-09-16 14:30:56.838 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第2行数据工号: 14660141
2025-09-16 14:30:56.839 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第3行数据工号: 34660002
2025-09-16 14:30:56.839 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第4行数据工号: 34660010
2025-09-16 14:30:56.840 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 62
2025-09-16 14:30:56.840 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[0]: 工号=34660024, 薪资=N/A
2025-09-16 14:30:56.840 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[1]: 工号=20222002, 薪资=N/A
2025-09-16 14:30:56.841 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[2]: 工号=14660141, 薪资=N/A
2025-09-16 14:30:56.841 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[3]: 工号=34660002, 薪资=N/A
2025-09-16 14:30:56.841 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[4]: 工号=34660010, 薪资=N/A
2025-09-16 14:30:56.842 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:488 | 可见行数据顺序: ['34660024', '20222002', '14660141', '34660002', '34660010']
2025-09-16 14:30:56.848 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: change_data_2025_12_a_grade
2025-09-16 14:30:56.854 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:474 | 🔧 [新架构] 为表格 change_data_2025_12_a_grade 重新加载 29 个字段映射
2025-09-16 14:30:56.854 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 62 行, 耗时: 129.1ms
2025-09-16 14:30:56.855 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 14:30:56.855 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 14:30:56.856 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1708 | 🆕 [列宽保存] 未找到表格 change_data_2025_12_a_grade 的列宽配置
2025-09-16 14:30:56.857 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 14:30:56.857 | INFO     | src.gui.prototype.prototype_main_window:set_data:1047 | 📊[pagination-write] 跳过写入当前页数据量(total=62)，保持 total=0，等待数据事件纠正
2025-09-16 14:30:56.858 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3984 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 62行, 20列
2025-09-16 14:30:56.858 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 20 个, 行号起始 1, 共 62 行
2025-09-16 14:30:56.859 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4000 | 🔧 [P0-新5修复] 表头强制刷新完成: change_data_2025_12_a_grade, 传递参数: 20个表头
2025-09-16 14:30:56.859 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4004 | 🔧 [统一数据设置] 数据已成功设置到UI: 62行, 20列
2025-09-16 14:30:56.860 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4077 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-09-16 14:30:56.860 | INFO     | src.services.table_data_service:load_table_data:555 | 📨[data_event] published
2025-09-16 14:30:56.860 | INFO     | src.gui.prototype.prototype_main_window:_execute_full_display_mode:6844 | [数据流追踪] 全量显示模式加载成功: 62行数据
2025-09-16 14:30:56.890 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: change_data_2025_12_a_grade
2025-09-16 14:30:56.957 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: change_data_2025_12_a_grade
2025-09-16 14:30:58.136 | INFO     | src.gui.prototype.prototype_main_window:_show_unified_import_dialog:6081 | 用户取消了数据导入
2025-09-16 14:31:01.074 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '异动人员表']
2025-09-16 14:31:01.074 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7713 | 导航变化: 异动人员表
2025-09-16 14:31:01.074 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10932 | 🚫 [用户要求] 表格切换不显示加载条: change_data_2025_12_a_grade -> None
2025-09-16 14:31:01.074 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10948 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-09-16 14:31:01.074 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8556 | 🔧 [表名生成] 处理异动表路径: ['异动人员表']
2025-09-16 14:31:01.074 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8637 | 🔧 [P0-紧急修复] 异动表路径层次较少，使用最新表: change_data_2025_12_retired
2025-09-16 14:31:01.074 | INFO     | src.gui.prototype.prototype_main_window:_process_navigation_change:7775 | 🔧 [P0-紧急修复] 导航变化时更新状态栏: 异动人员表
2025-09-16 14:31:01.074 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:384 | 已清理表 change_data_2025_12_retired 的缓存
2025-09-16 14:31:01.090 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11620 | 已注册 4 个表格到表头管理器
2025-09-16 14:31:01.090 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1197 | 🔧 [P1-3] 开始智能表头重影检测，共 5 个表格
2025-09-16 14:31:01.090 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1270 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-09-16 14:31:01.090 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1271 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-09-16 14:31:01.090 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7934 | 🆕 使用新架构加载数据: change_data_2025_12_retired（通过事件系统）
2025-09-16 14:31:01.090 | INFO     | src.services.table_data_service:load_table_data:377 | 加载表格数据: 页码: 1
2025-09-16 14:31:01.090 | INFO     | src.core.unified_data_request_manager:request_table_data:237 | 开始处理数据请求
2025-09-16 14:31:01.090 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:899 | 正在从表 change_data_2025_12_retired 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-09-16 14:31:01.090 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:1019 | 成功从表 change_data_2025_12_retired 获取第1页数据（含排序）: 2 行，总计2行
2025-09-16 14:31:01.090 | INFO     | src.core.unified_data_request_manager:request_table_data:287 | 数据请求处理完成: 字段=24, 行数=2, 耗时=0.0ms
2025-09-16 14:31:01.090 | INFO     | src.services.table_data_service:load_table_data:476 | [新架构] 使用统一格式管理器（单例优化）
2025-09-16 14:31:01.090 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1438 | 🔧 [智能分析] 开始异动表智能渲染: change_data
2025-09-16 14:31:01.090 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1443 | 🔧 [智能分析] 使用缓存的分析结果
2025-09-16 14:31:01.090 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1474 | 🔧 [智能分析] 已隐藏系统字段: ['import_time', 'id', 'created_at', 'data_source', 'updated_at']
2025-09-16 14:31:01.105 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1507 | 🔧 [智能分析] 数据质量评分: 0.80
2025-09-16 14:31:01.105 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1512 | 🔧 [智能分析] 异动表渲染完成: (2, 19)
2025-09-16 14:31:01.105 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: change_data, 行数: 2, 列数: 19
2025-09-16 14:31:01.105 | INFO     | src.core.unified_state_manager:update_table_state:233 | 表状态已更新: change_data_2025_12_retired, 变更类型: None
2025-09-16 14:31:01.105 | INFO     | src.services.table_data_service:load_table_data:528 | [修复数据发布] 数据加载成功，发布更新事件: 2行
2025-09-16 14:31:01.105 | INFO     | src.services.table_data_service:load_table_data:532 | 📨[data_event] publish | table=change_data_2025_12_retired | rows=2 | page=1 | size=50 | total=2 | req_type=RequestType.INITIAL_LOAD | request_id=SV-1758004261105-acbe5145
2025-09-16 14:31:01.105 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3874 | 📥[data_event] received | table=change_data_2025_12_retired | request_id=SV-1758004261105-acbe5145
2025-09-16 14:31:01.105 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3906 | 数据内容: 2行 x 19列
2025-09-16 14:31:01.105 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3937 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: change_data_2025_12_retired, 2行
2025-09-16 14:31:01.105 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3952 | 🔧 [P0-修复] 排序数据处理前: 19列 - ['工号', '月份', '年份', '序号', '人员代码']...
2025-09-16 14:31:01.121 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:01.121 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:01.121 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔧 [P0-修复] 字段映射后: 19列
2025-09-16 14:31:01.121 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:7617 | 🔧 [P0-修复] 系统字段过滤: 隐藏 3 个字段 - ['月份', '年份', '序号']
2025-09-16 14:31:01.121 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3956 | 🔧 [P0-修复] 系统字段过滤后: 16列
2025-09-16 14:31:01.121 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:31:01.121 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:31:01.121 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:31:01.121 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7670 | 表 change_data_2025_12_retired 没有用户偏好设置，显示所有可见字段
2025-09-16 14:31:01.121 | INFO     | src.gui.prototype.prototype_main_window:set_data:922 | 通过公共接口设置新的表格数据(DataFrame): 2 行
2025-09-16 14:31:01.136 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:01.136 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:01.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 2行，表名: change_data_2025_12_retired
2025-09-16 14:31:01.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7393 | 方案A：安全设置列数: 0
2025-09-16 14:31:01.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2822 | 🔧 [P1优化] 表切换操作: change_data_2025_12_a_grade -> change_data_2025_12_retired
2025-09-16 14:31:01.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19289006, 薪资=N/A
2025-09-16 14:31:01.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=19339009, 薪资=N/A
2025-09-16 14:31:01.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5528 | 最大可见行数已更新: 62 -> 10
2025-09-16 14:31:01.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5581 | 根据数据量(2)自动调整最大可见行数为: 10
2025-09-16 14:31:01.152 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1438 | 🔧 [智能分析] 开始异动表智能渲染: change_data
2025-09-16 14:31:01.152 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1443 | 🔧 [智能分析] 使用缓存的分析结果
2025-09-16 14:31:01.167 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1507 | 🔧 [智能分析] 数据质量评分: 0.94
2025-09-16 14:31:01.167 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1512 | 🔧 [智能分析] 异动表渲染完成: (2, 16)
2025-09-16 14:31:01.167 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: change_data, 行数: 2, 列数: 16
2025-09-16 14:31:01.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2983 | [列对齐] 已对齐记录与表头: rows=2->2, cols=16
2025-09-16 14:31:01.183 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 2行数据
2025-09-16 14:31:01.183 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 2行 x 16列
2025-09-16 14:31:01.183 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时0.0ms, 平均每行0.00ms
2025-09-16 14:31:01.183 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=2, 渲染时间=0.0ms, 策略=small_dataset
2025-09-16 14:31:01.183 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-09-16 14:31:01.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第0行数据工号: 19289006
2025-09-16 14:31:01.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第1行数据工号: 19339009
2025-09-16 14:31:01.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 2
2025-09-16 14:31:01.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[0]: 工号=19289006, 薪资=N/A
2025-09-16 14:31:01.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[1]: 工号=19339009, 薪资=N/A
2025-09-16 14:31:01.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:488 | 可见行数据顺序: ['19289006', '19339009']
2025-09-16 14:31:01.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:447 | 表格数据已设置: 2 行, 16 列
2025-09-16 14:31:01.196 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: change_data_2025_12_retired
2025-09-16 14:31:01.203 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:474 | 🔧 [新架构] 为表格 change_data_2025_12_retired 重新加载 29 个字段映射
2025-09-16 14:31:01.203 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 2 行, 耗时: 44.7ms
2025-09-16 14:31:01.204 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 14:31:01.205 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 14:31:01.205 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1708 | 🆕 [列宽保存] 未找到表格 change_data_2025_12_retired 的列宽配置
2025-09-16 14:31:01.206 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 14:31:01.206 | INFO     | src.gui.prototype.prototype_main_window:set_data:1047 | 📊[pagination-write] 跳过写入当前页数据量(total=2)，保持 total=0，等待数据事件纠正
2025-09-16 14:31:01.207 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3984 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 2行, 16列
2025-09-16 14:31:01.207 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 16 个, 行号起始 1, 共 2 行
2025-09-16 14:31:01.208 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4000 | 🔧 [P0-新5修复] 表头强制刷新完成: change_data_2025_12_retired, 传递参数: 16个表头
2025-09-16 14:31:01.208 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4004 | 🔧 [统一数据设置] 数据已成功设置到UI: 2行, 16列
2025-09-16 14:31:01.269 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:401 | 已清理所有缓存
2025-09-16 14:31:01.270 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:11427 | ✅ [新架构] 组件状态一致性验证通过
2025-09-16 14:31:01.272 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:11390 | 🆕 [新架构] 导航迁移完成
2025-09-16 14:31:01.273 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4077 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-09-16 14:31:01.273 | INFO     | src.services.table_data_service:load_table_data:555 | 📨[data_event] published
2025-09-16 14:31:01.273 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7939 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-09-16 14:31:01.307 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: change_data_2025_12_retired
2025-09-16 14:31:02.505 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '异动人员表', '异动人员表 > 2025年']
2025-09-16 14:31:02.505 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7713 | 导航变化: 异动人员表 > 2025年
2025-09-16 14:31:02.505 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10932 | 🚫 [用户要求] 表格切换不显示加载条: change_data_2025_12_retired -> None
2025-09-16 14:31:02.505 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10948 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-09-16 14:31:02.505 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8556 | 🔧 [表名生成] 处理异动表路径: ['异动人员表', '2025年']
2025-09-16 14:31:02.505 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8648 | 🔧 [表名生成] 生成异动表名: change_data_2025_09_all_active
2025-09-16 14:31:02.505 | INFO     | src.gui.prototype.prototype_main_window:_process_navigation_change:7775 | 🔧 [P0-紧急修复] 导航变化时更新状态栏: 异动人员表 > 2025年
2025-09-16 14:31:02.505 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:384 | 已清理表 change_data_2025_09_all_active 的缓存
2025-09-16 14:31:02.521 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11620 | 已注册 4 个表格到表头管理器
2025-09-16 14:31:02.521 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7934 | 🆕 使用新架构加载数据: change_data_2025_09_all_active（通过事件系统）
2025-09-16 14:31:02.521 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1619 | 🔧 [P1修复] 找到 8 个匹配类型 'None' 的表 (尝试 1/3)
2025-09-16 14:31:02.521 | WARNING  | src.core.unified_data_request_manager:resolve_table_name:797 | 🔧 [P2修复] 表 change_data_2025_09_all_active 不存在，使用相似表: change_data_2025_12_all_active
2025-09-16 14:31:02.537 | INFO     | src.services.table_data_service:load_table_data:377 | 加载表格数据: 页码: 1
2025-09-16 14:31:02.537 | WARNING  | src.core.unified_data_request_manager:resolve_table_name:797 | 🔧 [P2修复] 表 change_data_2025_09_all_active 不存在，使用相似表: change_data_2025_12_all_active
2025-09-16 14:31:02.537 | INFO     | src.core.unified_data_request_manager:request_table_data:237 | 开始处理数据请求
2025-09-16 14:31:02.537 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:899 | 正在从表 change_data_2025_12_all_active 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-09-16 14:31:02.537 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:1019 | 成功从表 change_data_2025_12_all_active 获取第1页数据（含排序）: 50 行，总计1396行
2025-09-16 14:31:02.552 | INFO     | src.core.unified_data_request_manager:request_table_data:287 | 数据请求处理完成: 字段=31, 行数=50, 耗时=15.7ms
2025-09-16 14:31:02.552 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1438 | 🔧 [智能分析] 开始异动表智能渲染: change_data
2025-09-16 14:31:02.552 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1447 | 🔧 [智能分析] 执行新的结构分析
2025-09-16 14:31:02.552 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:92 | 开始分析Excel结构: change_data
2025-09-16 14:31:02.552 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:93 | 数据维度: 50 行 x 31 列
2025-09-16 14:31:02.614 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:149 | Excel结构分析完成
2025-09-16 14:31:02.614 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:150 | 识别字段类型: 31 个
2025-09-16 14:31:02.614 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:151 | 整体数据质量得分: 0.93
2025-09-16 14:31:02.614 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1456 | 🔧 [智能分析] 分析建议: ['以下字段类型识别置信度较低，建议手动确认: 车补, 补发', '以下字段存在大量空值: 车补, 通讯补贴, 补发, 借支', "字段 '人员类别代码' 存在重复值，可能影响数据准确性"]
2025-09-16 14:31:02.630 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1474 | 🔧 [智能分析] 已隐藏系统字段: ['import_time', 'id', 'employee_id', 'created_at', 'data_source', 'updated_at']
2025-09-16 14:31:02.630 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1507 | 🔧 [智能分析] 数据质量评分: 0.93
2025-09-16 14:31:02.630 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1512 | 🔧 [智能分析] 异动表渲染完成: (50, 25)
2025-09-16 14:31:02.630 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: change_data, 行数: 50, 列数: 25
2025-09-16 14:31:02.630 | INFO     | src.core.unified_state_manager:update_table_state:233 | 表状态已更新: change_data_2025_12_all_active, 变更类型: None
2025-09-16 14:31:02.630 | INFO     | src.services.table_data_service:load_table_data:528 | [修复数据发布] 数据加载成功，发布更新事件: 50行
2025-09-16 14:31:02.646 | INFO     | src.services.table_data_service:load_table_data:532 | 📨[data_event] publish | table=change_data_2025_12_all_active | rows=50 | page=1 | size=50 | total=1396 | req_type=RequestType.INITIAL_LOAD | request_id=SV-1758004262646-e4d89cc5
2025-09-16 14:31:02.646 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3874 | 📥[data_event] received | table=change_data_2025_12_all_active | request_id=SV-1758004262646-e4d89cc5
2025-09-16 14:31:02.646 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3906 | 数据内容: 50行 x 25列
2025-09-16 14:31:02.646 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3937 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: change_data_2025_12_all_active, 50行
2025-09-16 14:31:02.646 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3952 | 🔧 [P0-修复] 排序数据处理前: 25列 - ['月份', '年份', '序号', '工号', '姓名']...
2025-09-16 14:31:02.646 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:02.646 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:02.646 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔧 [P0-修复] 字段映射后: 25列
2025-09-16 14:31:02.646 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:7617 | 🔧 [P0-修复] 系统字段过滤: 隐藏 3 个字段 - ['月份', '年份', '序号']
2025-09-16 14:31:02.646 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3956 | 🔧 [P0-修复] 系统字段过滤后: 22列
2025-09-16 14:31:02.646 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:31:02.661 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:31:02.661 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:31:02.661 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7670 | 表 change_data_2025_12_all_active 没有用户偏好设置，显示所有可见字段
2025-09-16 14:31:02.661 | INFO     | src.gui.prototype.prototype_main_window:set_data:922 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-09-16 14:31:02.661 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:02.661 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:02.677 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 50行，表名: change_data_2025_12_all_active
2025-09-16 14:31:02.677 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2822 | 🔧 [P1优化] 表切换操作: change_data_2025_12_retired -> change_data_2025_12_all_active
2025-09-16 14:31:02.677 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089, 薪资=2,375.00
2025-09-16 14:31:02.677 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565, 薪资=1,696.00
2025-09-16 14:31:02.677 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5528 | 最大可见行数已更新: 10 -> 50
2025-09-16 14:31:02.677 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5581 | 根据数据量(50)自动调整最大可见行数为: 50
2025-09-16 14:31:02.677 | INFO     | src.modules.data_storage.dynamic_table_manager:_find_best_matching_table_config_corrected:3267 | 开始为新表 'change_data_2025_09_all_active' 寻找最佳匹配的字段映射配置...
2025-09-16 14:31:02.677 | INFO     | src.modules.system_config.config_manager:get_all_table_configs:680 | 已加载字段映射配置: 5 项（表级+模板级）
2025-09-16 14:31:02.677 | INFO     | src.modules.data_storage.dynamic_table_manager:_find_best_matching_table_config_corrected:3341 | 未能为 'change_data_2025_09_all_active' 找到足够好的匹配配置 (最高分: 0.38)。将使用基础映射。
2025-09-16 14:31:02.677 | WARNING  | src.modules.data_storage.dynamic_table_manager:_get_dynamic_field_mappings:3234 | 未找到表 change_data_2025_09_all_active 的专用映射，使用基础映射
2025-09-16 14:31:02.677 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:3489 | 🔧 [修复标识] 字段映射生效，样例: ['工号', '姓名', '部门名称'] -> ['工号', '姓名', '部门名称']
2025-09-16 14:31:02.677 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1438 | 🔧 [智能分析] 开始异动表智能渲染: change_data
2025-09-16 14:31:02.677 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1447 | 🔧 [智能分析] 执行新的结构分析
2025-09-16 14:31:02.677 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:92 | 开始分析Excel结构: change_data
2025-09-16 14:31:02.677 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:93 | 数据维度: 50 行 x 22 列
2025-09-16 14:31:02.724 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:149 | Excel结构分析完成
2025-09-16 14:31:02.724 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:150 | 识别字段类型: 22 个
2025-09-16 14:31:02.724 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:151 | 整体数据质量得分: 0.93
2025-09-16 14:31:02.740 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1456 | 🔧 [智能分析] 分析建议: ["字段 '人员类别代码' 存在重复值，可能影响数据准确性"]
2025-09-16 14:31:02.755 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1507 | 🔧 [智能分析] 数据质量评分: 0.93
2025-09-16 14:31:02.755 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1512 | 🔧 [智能分析] 异动表渲染完成: (50, 22)
2025-09-16 14:31:02.755 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: change_data, 行数: 50, 列数: 22
2025-09-16 14:31:02.755 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7393 | 方案A：安全设置列数: 22
2025-09-16 14:31:02.771 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-09-16 14:31:02.771 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 22列
2025-09-16 14:31:02.771 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时0.0ms, 平均每行0.00ms
2025-09-16 14:31:02.786 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=0.0ms, 策略=small_dataset
2025-09-16 14:31:02.786 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-09-16 14:31:02.786 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第0行数据工号: 19990089
2025-09-16 14:31:02.786 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第1行数据工号: 20161565
2025-09-16 14:31:02.786 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第2行数据工号: 20191782
2025-09-16 14:31:02.786 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第3行数据工号: 20151515
2025-09-16 14:31:02.786 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第4行数据工号: 20181640
2025-09-16 14:31:02.786 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-09-16 14:31:02.786 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=0.00
2025-09-16 14:31:02.786 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=0.00
2025-09-16 14:31:02.786 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=0.00
2025-09-16 14:31:02.786 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=0.00
2025-09-16 14:31:02.786 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=0.00
2025-09-16 14:31:02.786 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:488 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-09-16 14:31:02.786 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:447 | 表格数据已设置: 50 行, 22 列
2025-09-16 14:31:02.802 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: change_data_2025_12_all_active
2025-09-16 14:31:02.808 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:474 | 🔧 [新架构] 为表格 change_data_2025_12_all_active 重新加载 29 个字段映射
2025-09-16 14:31:02.808 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 50 行, 耗时: 124.6ms
2025-09-16 14:31:02.809 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 14:31:02.809 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 14:31:02.810 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1708 | 🆕 [列宽保存] 未找到表格 change_data_2025_12_all_active 的列宽配置
2025-09-16 14:31:02.811 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 14:31:02.812 | INFO     | src.gui.prototype.prototype_main_window:set_data:1047 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=0，等待数据事件纠正
2025-09-16 14:31:02.812 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3984 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 22列
2025-09-16 14:31:02.812 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 22 个, 行号起始 1, 共 50 行
2025-09-16 14:31:02.813 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4000 | 🔧 [P0-新5修复] 表头强制刷新完成: change_data_2025_12_all_active, 传递参数: 22个表头
2025-09-16 14:31:02.814 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4004 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 22列
2025-09-16 14:31:02.877 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:401 | 已清理所有缓存
2025-09-16 14:31:02.878 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:11427 | ✅ [新架构] 组件状态一致性验证通过
2025-09-16 14:31:02.881 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:11390 | 🆕 [新架构] 导航迁移完成
2025-09-16 14:31:02.881 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4077 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-09-16 14:31:02.882 | INFO     | src.services.table_data_service:load_table_data:555 | 📨[data_event] published
2025-09-16 14:31:02.882 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7939 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-09-16 14:31:02.921 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: change_data_2025_12_all_active
2025-09-16 14:31:04.088 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '异动人员表 > 2025年', '异动人员表', '异动人员表 > 2025年 > 12月']
2025-09-16 14:31:04.088 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7713 | 导航变化: 异动人员表 > 2025年 > 12月
2025-09-16 14:31:04.088 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10932 | 🚫 [用户要求] 表格切换不显示加载条: change_data_2025_12_all_active -> None
2025-09-16 14:31:04.088 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10948 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-09-16 14:31:04.088 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8556 | 🔧 [表名生成] 处理异动表路径: ['异动人员表', '2025年', '12月']
2025-09-16 14:31:04.088 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8648 | 🔧 [表名生成] 生成异动表名: change_data_2025_12_all_active
2025-09-16 14:31:04.088 | INFO     | src.gui.prototype.prototype_main_window:_process_navigation_change:7775 | 🔧 [P0-紧急修复] 导航变化时更新状态栏: 异动人员表 > 2025年 > 12月
2025-09-16 14:31:04.088 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:384 | 已清理表 change_data_2025_12_all_active 的缓存
2025-09-16 14:31:04.088 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11620 | 已注册 4 个表格到表头管理器
2025-09-16 14:31:04.088 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1197 | 🔧 [P1-3] 开始智能表头重影检测，共 5 个表格
2025-09-16 14:31:04.088 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1270 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-09-16 14:31:04.088 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1271 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-09-16 14:31:04.104 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7934 | 🆕 使用新架构加载数据: change_data_2025_12_all_active（通过事件系统）
2025-09-16 14:31:04.104 | INFO     | src.services.table_data_service:load_table_data:377 | 加载表格数据: 页码: 1
2025-09-16 14:31:04.104 | INFO     | src.services.table_data_service:load_table_data:401 | [缓存命中] 使用缓存数据: 第1页
2025-09-16 14:31:04.104 | INFO     | src.services.table_data_service:load_table_data:412 | 📨[data_event] publish(cache) | table=change_data_2025_12_all_active | rows=50 | page=1 | size=50 | total=1396 | request_id=SV-1758004264104-d8ddf5e9-C
2025-09-16 14:31:04.104 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3874 | 📥[data_event] received | table=change_data_2025_12_all_active | request_id=SV-1758004264104-d8ddf5e9-C
2025-09-16 14:31:04.104 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3906 | 数据内容: 50行 x 25列
2025-09-16 14:31:04.104 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3937 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: change_data_2025_12_all_active, 50行
2025-09-16 14:31:04.104 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3952 | 🔧 [P0-修复] 排序数据处理前: 25列 - ['月份', '年份', '序号', '工号', '姓名']...
2025-09-16 14:31:04.104 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:04.104 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:04.104 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔧 [P0-修复] 字段映射后: 25列
2025-09-16 14:31:04.119 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:7617 | 🔧 [P0-修复] 系统字段过滤: 隐藏 3 个字段 - ['月份', '年份', '序号']
2025-09-16 14:31:04.119 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3956 | 🔧 [P0-修复] 系统字段过滤后: 22列
2025-09-16 14:31:04.119 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:31:04.119 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:31:04.119 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:31:04.135 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7670 | 表 change_data_2025_12_all_active 没有用户偏好设置，显示所有可见字段
2025-09-16 14:31:04.135 | INFO     | src.gui.prototype.prototype_main_window:set_data:922 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-09-16 14:31:04.135 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:04.135 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:04.135 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 50行，表名: change_data_2025_12_all_active
2025-09-16 14:31:04.135 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089, 薪资=2,375.00
2025-09-16 14:31:04.135 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565, 薪资=1,696.00
2025-09-16 14:31:04.135 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5528 | 最大可见行数已更新: 50 -> 50
2025-09-16 14:31:04.135 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5581 | 根据数据量(50)自动调整最大可见行数为: 50
2025-09-16 14:31:04.151 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1438 | 🔧 [智能分析] 开始异动表智能渲染: change_data
2025-09-16 14:31:04.151 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1443 | 🔧 [智能分析] 使用缓存的分析结果
2025-09-16 14:31:04.169 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1507 | 🔧 [智能分析] 数据质量评分: 0.93
2025-09-16 14:31:04.169 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1512 | 🔧 [智能分析] 异动表渲染完成: (50, 22)
2025-09-16 14:31:04.169 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: change_data, 行数: 50, 列数: 22
2025-09-16 14:31:04.186 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-09-16 14:31:04.186 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 22列
2025-09-16 14:31:04.198 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时12.2ms, 平均每行0.24ms
2025-09-16 14:31:04.198 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=12.2ms, 策略=small_dataset
2025-09-16 14:31:04.198 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-09-16 14:31:04.198 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第0行数据工号: 19990089
2025-09-16 14:31:04.198 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第1行数据工号: 20161565
2025-09-16 14:31:04.198 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第2行数据工号: 20191782
2025-09-16 14:31:04.198 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第3行数据工号: 20151515
2025-09-16 14:31:04.198 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第4行数据工号: 20181640
2025-09-16 14:31:04.198 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-09-16 14:31:04.198 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=0.00
2025-09-16 14:31:04.198 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=0.00
2025-09-16 14:31:04.198 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=0.00
2025-09-16 14:31:04.198 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=0.00
2025-09-16 14:31:04.198 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=0.00
2025-09-16 14:31:04.198 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:488 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-09-16 14:31:04.198 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:447 | 表格数据已设置: 50 行, 22 列
2025-09-16 14:31:04.206 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 50 行, 耗时: 71.1ms
2025-09-16 14:31:04.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 14:31:04.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 14:31:04.217 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1708 | 🆕 [列宽保存] 未找到表格 change_data_2025_12_all_active 的列宽配置
2025-09-16 14:31:04.217 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 14:31:04.218 | INFO     | src.gui.prototype.prototype_main_window:set_data:1047 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=0，等待数据事件纠正
2025-09-16 14:31:04.219 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3984 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 22列
2025-09-16 14:31:04.219 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 22 个, 行号起始 1, 共 50 行
2025-09-16 14:31:04.220 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4000 | 🔧 [P0-新5修复] 表头强制刷新完成: change_data_2025_12_all_active, 传递参数: 22个表头
2025-09-16 14:31:04.220 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4004 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 22列
2025-09-16 14:31:04.280 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:401 | 已清理所有缓存
2025-09-16 14:31:04.281 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:11427 | ✅ [新架构] 组件状态一致性验证通过
2025-09-16 14:31:04.283 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:11390 | 🆕 [新架构] 导航迁移完成
2025-09-16 14:31:04.284 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4077 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-09-16 14:31:04.284 | INFO     | src.services.table_data_service:load_table_data:437 | 📨[data_event] published(cache)
2025-09-16 14:31:04.285 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7939 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-09-16 14:31:04.323 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: change_data_2025_12_all_active
2025-09-16 14:31:06.791 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '异动人员表 > 2025年 > 12月', '异动人员表 > 2025年', '异动人员表', '异动人员表 > 2025年 > 12月 > 离休人员']
2025-09-16 14:31:06.791 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7713 | 导航变化: 异动人员表 > 2025年 > 12月 > 离休人员
2025-09-16 14:31:06.791 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10932 | 🚫 [用户要求] 表格切换不显示加载条: change_data_2025_12_all_active -> None
2025-09-16 14:31:06.791 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10948 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-09-16 14:31:06.806 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8556 | 🔧 [表名生成] 处理异动表路径: ['异动人员表', '2025年', '12月', '离休人员']
2025-09-16 14:31:06.806 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8577 | 🔧 [表名生成] 命中最近导入映射: ('异动人员表', '2025', '12', '离休人员') -> change_data_2025_12_retired
2025-09-16 14:31:06.806 | INFO     | src.gui.prototype.prototype_main_window:_process_navigation_change:7775 | 🔧 [P0-紧急修复] 导航变化时更新状态栏: 异动人员表 > 2025年 > 12月 > 离休人员
2025-09-16 14:31:06.806 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:384 | 已清理表 change_data_2025_12_retired 的缓存
2025-09-16 14:31:06.806 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11620 | 已注册 4 个表格到表头管理器
2025-09-16 14:31:06.806 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1197 | 🔧 [P1-3] 开始智能表头重影检测，共 5 个表格
2025-09-16 14:31:06.806 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1270 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-09-16 14:31:06.806 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1271 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-09-16 14:31:06.806 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7934 | 🆕 使用新架构加载数据: change_data_2025_12_retired（通过事件系统）
2025-09-16 14:31:06.806 | INFO     | src.services.table_data_service:load_table_data:377 | 加载表格数据: 页码: 1
2025-09-16 14:31:06.806 | INFO     | src.services.table_data_service:load_table_data:401 | [缓存命中] 使用缓存数据: 第1页
2025-09-16 14:31:06.806 | INFO     | src.services.table_data_service:load_table_data:412 | 📨[data_event] publish(cache) | table=change_data_2025_12_retired | rows=2 | page=1 | size=50 | total=2 | request_id=SV-1758004266806-4d43889f-C
2025-09-16 14:31:06.806 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3874 | 📥[data_event] received | table=change_data_2025_12_retired | request_id=SV-1758004266806-4d43889f-C
2025-09-16 14:31:06.806 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3906 | 数据内容: 2行 x 19列
2025-09-16 14:31:06.806 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3937 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: change_data_2025_12_retired, 2行
2025-09-16 14:31:06.806 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3952 | 🔧 [P0-修复] 排序数据处理前: 19列 - ['工号', '月份', '年份', '序号', '人员代码']...
2025-09-16 14:31:06.806 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:06.822 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:06.822 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔧 [P0-修复] 字段映射后: 19列
2025-09-16 14:31:06.822 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:7617 | 🔧 [P0-修复] 系统字段过滤: 隐藏 3 个字段 - ['月份', '年份', '序号']
2025-09-16 14:31:06.822 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3956 | 🔧 [P0-修复] 系统字段过滤后: 16列
2025-09-16 14:31:06.822 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:31:06.838 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:31:06.838 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:31:06.838 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7670 | 表 change_data_2025_12_retired 没有用户偏好设置，显示所有可见字段
2025-09-16 14:31:06.838 | INFO     | src.gui.prototype.prototype_main_window:set_data:922 | 通过公共接口设置新的表格数据(DataFrame): 2 行
2025-09-16 14:31:06.838 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:06.838 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:06.838 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 2行，表名: change_data_2025_12_retired
2025-09-16 14:31:06.838 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7393 | 方案A：安全设置列数: 0
2025-09-16 14:31:06.853 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2822 | 🔧 [P1优化] 表切换操作: change_data_2025_12_all_active -> change_data_2025_12_retired
2025-09-16 14:31:06.853 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19289006, 薪资=N/A
2025-09-16 14:31:06.853 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=19339009, 薪资=N/A
2025-09-16 14:31:06.853 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5528 | 最大可见行数已更新: 50 -> 10
2025-09-16 14:31:06.853 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5581 | 根据数据量(2)自动调整最大可见行数为: 10
2025-09-16 14:31:06.869 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1438 | 🔧 [智能分析] 开始异动表智能渲染: change_data
2025-09-16 14:31:06.869 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1443 | 🔧 [智能分析] 使用缓存的分析结果
2025-09-16 14:31:06.869 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1507 | 🔧 [智能分析] 数据质量评分: 0.94
2025-09-16 14:31:06.869 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1512 | 🔧 [智能分析] 异动表渲染完成: (2, 16)
2025-09-16 14:31:06.885 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: change_data, 行数: 2, 列数: 16
2025-09-16 14:31:06.885 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2983 | [列对齐] 已对齐记录与表头: rows=2->2, cols=16
2025-09-16 14:31:06.885 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 2行数据
2025-09-16 14:31:06.885 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 2行 x 16列
2025-09-16 14:31:06.885 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时0.0ms, 平均每行0.00ms
2025-09-16 14:31:06.885 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=2, 渲染时间=0.0ms, 策略=small_dataset
2025-09-16 14:31:06.885 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-09-16 14:31:06.885 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第0行数据工号: 19289006
2025-09-16 14:31:06.885 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第1行数据工号: 19339009
2025-09-16 14:31:06.885 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 2
2025-09-16 14:31:06.885 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[0]: 工号=19289006, 薪资=N/A
2025-09-16 14:31:06.885 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[1]: 工号=19339009, 薪资=N/A
2025-09-16 14:31:06.885 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:488 | 可见行数据顺序: ['19289006', '19339009']
2025-09-16 14:31:06.885 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:447 | 表格数据已设置: 2 行, 16 列
2025-09-16 14:31:06.900 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: change_data_2025_12_retired
2025-09-16 14:31:06.907 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:474 | 🔧 [新架构] 为表格 change_data_2025_12_retired 重新加载 29 个字段映射
2025-09-16 14:31:06.908 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 2 行, 耗时: 62.2ms
2025-09-16 14:31:06.908 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 14:31:06.908 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 14:31:06.909 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1708 | 🆕 [列宽保存] 未找到表格 change_data_2025_12_retired 的列宽配置
2025-09-16 14:31:06.910 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 14:31:06.910 | INFO     | src.gui.prototype.prototype_main_window:set_data:1047 | 📊[pagination-write] 跳过写入当前页数据量(total=2)，保持 total=0，等待数据事件纠正
2025-09-16 14:31:06.911 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3984 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 2行, 16列
2025-09-16 14:31:06.912 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 16 个, 行号起始 1, 共 2 行
2025-09-16 14:31:06.912 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4000 | 🔧 [P0-新5修复] 表头强制刷新完成: change_data_2025_12_retired, 传递参数: 16个表头
2025-09-16 14:31:06.913 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4004 | 🔧 [统一数据设置] 数据已成功设置到UI: 2行, 16列
2025-09-16 14:31:06.965 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:401 | 已清理所有缓存
2025-09-16 14:31:06.965 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:11427 | ✅ [新架构] 组件状态一致性验证通过
2025-09-16 14:31:06.968 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:11390 | 🆕 [新架构] 导航迁移完成
2025-09-16 14:31:06.968 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4077 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-09-16 14:31:06.969 | INFO     | src.services.table_data_service:load_table_data:437 | 📨[data_event] published(cache)
2025-09-16 14:31:06.969 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7939 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-09-16 14:31:06.969 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:1028 | 导航选择: 异动人员表 > 2025年 > 12月 > 离休人员
2025-09-16 14:31:07.018 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: change_data_2025_12_retired
2025-09-16 14:31:08.312 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '异动人员表 > 2025年 > 12月', '异动人员表 > 2025年', '异动人员表', '异动人员表 > 2025年 > 12月 > 退休人员']
2025-09-16 14:31:08.312 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7713 | 导航变化: 异动人员表 > 2025年 > 12月 > 退休人员
2025-09-16 14:31:08.312 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10932 | 🚫 [用户要求] 表格切换不显示加载条: change_data_2025_12_retired -> None
2025-09-16 14:31:08.312 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10948 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-09-16 14:31:08.312 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8556 | 🔧 [表名生成] 处理异动表路径: ['异动人员表', '2025年', '12月', '退休人员']
2025-09-16 14:31:08.312 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8577 | 🔧 [表名生成] 命中最近导入映射: ('异动人员表', '2025', '12', '退休人员') -> change_data_2025_12_pension
2025-09-16 14:31:08.312 | INFO     | src.gui.prototype.prototype_main_window:_process_navigation_change:7775 | 🔧 [P0-紧急修复] 导航变化时更新状态栏: 异动人员表 > 2025年 > 12月 > 退休人员
2025-09-16 14:31:08.312 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:384 | 已清理表 change_data_2025_12_pension 的缓存
2025-09-16 14:31:08.312 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11620 | 已注册 4 个表格到表头管理器
2025-09-16 14:31:08.312 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7934 | 🆕 使用新架构加载数据: change_data_2025_12_pension（通过事件系统）
2025-09-16 14:31:08.312 | INFO     | src.services.table_data_service:load_table_data:377 | 加载表格数据: 页码: 1
2025-09-16 14:31:08.327 | INFO     | src.core.unified_data_request_manager:request_table_data:237 | 开始处理数据请求
2025-09-16 14:31:08.327 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:899 | 正在从表 change_data_2025_12_pension 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-09-16 14:31:08.327 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:1019 | 成功从表 change_data_2025_12_pension 获取第1页数据（含排序）: 13 行，总计13行
2025-09-16 14:31:08.327 | INFO     | src.core.unified_data_request_manager:request_table_data:287 | 数据请求处理完成: 字段=35, 行数=13, 耗时=0.0ms
2025-09-16 14:31:08.327 | INFO     | src.services.table_data_service:load_table_data:476 | [新架构] 使用统一格式管理器（单例优化）
2025-09-16 14:31:08.327 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1438 | 🔧 [智能分析] 开始异动表智能渲染: change_data
2025-09-16 14:31:08.327 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1447 | 🔧 [智能分析] 执行新的结构分析
2025-09-16 14:31:08.343 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:92 | 开始分析Excel结构: change_data
2025-09-16 14:31:08.343 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:93 | 数据维度: 13 行 x 35 列
2025-09-16 14:31:08.405 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:149 | Excel结构分析完成
2025-09-16 14:31:08.405 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:150 | 识别字段类型: 35 个
2025-09-16 14:31:08.421 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:151 | 整体数据质量得分: 0.86
2025-09-16 14:31:08.421 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1456 | 🔧 [智能分析] 分析建议: ['以下字段类型识别置信度较低，建议手动确认: 护理费, 补发, 借支, 公积, 保险扣款', '以下字段存在大量空值: 护理费, 2016待遇调整, 2017待遇调整, 2018待遇调整, 2019待遇调整', "字段 '人员类别代码' 存在重复值，可能影响数据准确性"]
2025-09-16 14:31:08.421 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1474 | 🔧 [智能分析] 已隐藏系统字段: ['import_time', 'id', 'created_at', 'data_source', 'updated_at']
2025-09-16 14:31:08.421 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1507 | 🔧 [智能分析] 数据质量评分: 0.86
2025-09-16 14:31:08.421 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1512 | 🔧 [智能分析] 异动表渲染完成: (13, 30)
2025-09-16 14:31:08.421 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: change_data, 行数: 13, 列数: 30
2025-09-16 14:31:08.436 | INFO     | src.core.unified_state_manager:update_table_state:233 | 表状态已更新: change_data_2025_12_pension, 变更类型: None
2025-09-16 14:31:08.436 | INFO     | src.services.table_data_service:load_table_data:528 | [修复数据发布] 数据加载成功，发布更新事件: 13行
2025-09-16 14:31:08.436 | INFO     | src.services.table_data_service:load_table_data:532 | 📨[data_event] publish | table=change_data_2025_12_pension | rows=13 | page=1 | size=50 | total=13 | req_type=RequestType.INITIAL_LOAD | request_id=SV-1758004268436-a05329d8
2025-09-16 14:31:08.436 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3874 | 📥[data_event] received | table=change_data_2025_12_pension | request_id=SV-1758004268436-a05329d8
2025-09-16 14:31:08.436 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3906 | 数据内容: 13行 x 30列
2025-09-16 14:31:08.436 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3937 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: change_data_2025_12_pension, 13行
2025-09-16 14:31:08.436 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3952 | 🔧 [P0-修复] 排序数据处理前: 30列 - ['工号', '月份', '年份', '序号', '人员代码']...
2025-09-16 14:31:08.436 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:08.436 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:08.436 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔧 [P0-修复] 字段映射后: 30列
2025-09-16 14:31:08.436 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:7617 | 🔧 [P0-修复] 系统字段过滤: 隐藏 3 个字段 - ['月份', '年份', '序号']
2025-09-16 14:31:08.436 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3956 | 🔧 [P0-修复] 系统字段过滤后: 27列
2025-09-16 14:31:08.436 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:31:08.452 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:31:08.452 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:31:08.452 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7670 | 表 change_data_2025_12_pension 没有用户偏好设置，显示所有可见字段
2025-09-16 14:31:08.452 | INFO     | src.gui.prototype.prototype_main_window:set_data:922 | 通过公共接口设置新的表格数据(DataFrame): 13 行
2025-09-16 14:31:08.452 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:08.452 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:08.471 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 13行，表名: change_data_2025_12_pension
2025-09-16 14:31:08.471 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2822 | 🔧 [P1优化] 表切换操作: change_data_2025_12_retired -> change_data_2025_12_pension
2025-09-16 14:31:08.471 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19709165, 薪资=N/A
2025-09-16 14:31:08.471 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=19981259, 薪资=N/A
2025-09-16 14:31:08.471 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5528 | 最大可见行数已更新: 10 -> 13
2025-09-16 14:31:08.471 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5581 | 根据数据量(13)自动调整最大可见行数为: 13
2025-09-16 14:31:08.471 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1438 | 🔧 [智能分析] 开始异动表智能渲染: change_data
2025-09-16 14:31:08.471 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1447 | 🔧 [智能分析] 执行新的结构分析
2025-09-16 14:31:08.471 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:92 | 开始分析Excel结构: change_data
2025-09-16 14:31:08.471 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:93 | 数据维度: 13 行 x 27 列
2025-09-16 14:31:08.530 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:149 | Excel结构分析完成
2025-09-16 14:31:08.530 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:150 | 识别字段类型: 27 个
2025-09-16 14:31:08.546 | INFO     | src.modules.format_management.change_data_analyzer:analyze_excel_structure:151 | 整体数据质量得分: 0.94
2025-09-16 14:31:08.546 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1456 | 🔧 [智能分析] 分析建议: ["字段 '人员类别代码' 存在重复值，可能影响数据准确性"]
2025-09-16 14:31:08.561 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1507 | 🔧 [智能分析] 数据质量评分: 0.94
2025-09-16 14:31:08.561 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1512 | 🔧 [智能分析] 异动表渲染完成: (13, 27)
2025-09-16 14:31:08.561 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: change_data, 行数: 13, 列数: 27
2025-09-16 14:31:08.561 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7393 | 方案A：安全设置列数: 27
2025-09-16 14:31:08.561 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 13行数据
2025-09-16 14:31:08.561 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 13行 x 27列
2025-09-16 14:31:08.577 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时15.7ms, 平均每行1.21ms
2025-09-16 14:31:08.577 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=13, 渲染时间=15.7ms, 策略=small_dataset
2025-09-16 14:31:08.577 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-09-16 14:31:08.577 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第0行数据工号: 19709165
2025-09-16 14:31:08.577 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第1行数据工号: 19981259
2025-09-16 14:31:08.577 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第2行数据工号: 19721294
2025-09-16 14:31:08.577 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第3行数据工号: 19841258
2025-09-16 14:31:08.577 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第4行数据工号: 19499098
2025-09-16 14:31:08.577 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 13
2025-09-16 14:31:08.577 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[0]: 工号=19709165, 薪资=N/A
2025-09-16 14:31:08.577 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[1]: 工号=19981259, 薪资=N/A
2025-09-16 14:31:08.577 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[2]: 工号=19721294, 薪资=N/A
2025-09-16 14:31:08.577 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[3]: 工号=19841258, 薪资=N/A
2025-09-16 14:31:08.577 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[4]: 工号=19499098, 薪资=N/A
2025-09-16 14:31:08.577 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:488 | 可见行数据顺序: ['19709165', '19981259', '19721294', '19841258', '19499098']
2025-09-16 14:31:08.577 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:447 | 表格数据已设置: 13 行, 27 列
2025-09-16 14:31:08.591 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: change_data_2025_12_pension
2025-09-16 14:31:08.600 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:474 | 🔧 [新架构] 为表格 change_data_2025_12_pension 重新加载 29 个字段映射
2025-09-16 14:31:08.600 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 13 行, 耗时: 120.1ms
2025-09-16 14:31:08.601 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 14:31:08.601 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 14:31:08.602 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1708 | 🆕 [列宽保存] 未找到表格 change_data_2025_12_pension 的列宽配置
2025-09-16 14:31:08.603 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 14:31:08.603 | INFO     | src.gui.prototype.prototype_main_window:set_data:1047 | 📊[pagination-write] 跳过写入当前页数据量(total=13)，保持 total=0，等待数据事件纠正
2025-09-16 14:31:08.604 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3984 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 13行, 27列
2025-09-16 14:31:08.604 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 27 个, 行号起始 1, 共 13 行
2025-09-16 14:31:08.605 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4000 | 🔧 [P0-新5修复] 表头强制刷新完成: change_data_2025_12_pension, 传递参数: 27个表头
2025-09-16 14:31:08.605 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4004 | 🔧 [统一数据设置] 数据已成功设置到UI: 13行, 27列
2025-09-16 14:31:08.663 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:401 | 已清理所有缓存
2025-09-16 14:31:08.664 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:11427 | ✅ [新架构] 组件状态一致性验证通过
2025-09-16 14:31:08.666 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:11390 | 🆕 [新架构] 导航迁移完成
2025-09-16 14:31:08.666 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4077 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-09-16 14:31:08.667 | INFO     | src.services.table_data_service:load_table_data:555 | 📨[data_event] published
2025-09-16 14:31:08.667 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7939 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-09-16 14:31:08.706 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: change_data_2025_12_pension
2025-09-16 14:31:10.005 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '异动人员表 > 2025年 > 12月', '异动人员表 > 2025年', '异动人员表', '异动人员表 > 2025年 > 12月 > 全部在职人员']
2025-09-16 14:31:10.005 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7713 | 导航变化: 异动人员表 > 2025年 > 12月 > 全部在职人员
2025-09-16 14:31:10.020 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10932 | 🚫 [用户要求] 表格切换不显示加载条: change_data_2025_12_pension -> None
2025-09-16 14:31:10.020 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10948 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-09-16 14:31:10.020 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8556 | 🔧 [表名生成] 处理异动表路径: ['异动人员表', '2025年', '12月', '全部在职人员']
2025-09-16 14:31:10.020 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8577 | 🔧 [表名生成] 命中最近导入映射: ('异动人员表', '2025', '12', '全部在职人员') -> change_data_2025_12_all_active
2025-09-16 14:31:10.020 | INFO     | src.gui.prototype.prototype_main_window:_process_navigation_change:7775 | 🔧 [P0-紧急修复] 导航变化时更新状态栏: 异动人员表 > 2025年 > 12月 > 全部在职人员
2025-09-16 14:31:10.020 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:384 | 已清理表 change_data_2025_12_all_active 的缓存
2025-09-16 14:31:10.020 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11620 | 已注册 4 个表格到表头管理器
2025-09-16 14:31:10.020 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1197 | 🔧 [P1-3] 开始智能表头重影检测，共 5 个表格
2025-09-16 14:31:10.020 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1270 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-09-16 14:31:10.020 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1271 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-09-16 14:31:10.020 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7934 | 🆕 使用新架构加载数据: change_data_2025_12_all_active（通过事件系统）
2025-09-16 14:31:10.020 | INFO     | src.services.table_data_service:load_table_data:377 | 加载表格数据: 页码: 1
2025-09-16 14:31:10.020 | INFO     | src.services.table_data_service:load_table_data:401 | [缓存命中] 使用缓存数据: 第1页
2025-09-16 14:31:10.020 | INFO     | src.services.table_data_service:load_table_data:412 | 📨[data_event] publish(cache) | table=change_data_2025_12_all_active | rows=50 | page=1 | size=50 | total=1396 | request_id=SV-1758004270020-b0c1e72d-C
2025-09-16 14:31:10.020 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3874 | 📥[data_event] received | table=change_data_2025_12_all_active | request_id=SV-1758004270020-b0c1e72d-C
2025-09-16 14:31:10.020 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3906 | 数据内容: 50行 x 25列
2025-09-16 14:31:10.020 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3937 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: change_data_2025_12_all_active, 50行
2025-09-16 14:31:10.020 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3952 | 🔧 [P0-修复] 排序数据处理前: 25列 - ['月份', '年份', '序号', '工号', '姓名']...
2025-09-16 14:31:10.036 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:10.036 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:10.036 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔧 [P0-修复] 字段映射后: 25列
2025-09-16 14:31:10.036 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:7617 | 🔧 [P0-修复] 系统字段过滤: 隐藏 3 个字段 - ['月份', '年份', '序号']
2025-09-16 14:31:10.036 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3956 | 🔧 [P0-修复] 系统字段过滤后: 22列
2025-09-16 14:31:10.036 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:31:10.051 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:31:10.051 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:31:10.051 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7670 | 表 change_data_2025_12_all_active 没有用户偏好设置，显示所有可见字段
2025-09-16 14:31:10.051 | INFO     | src.gui.prototype.prototype_main_window:set_data:922 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-09-16 14:31:10.051 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:10.051 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:10.067 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 50行，表名: change_data_2025_12_all_active
2025-09-16 14:31:10.067 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7393 | 方案A：安全设置列数: 0
2025-09-16 14:31:10.067 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2822 | 🔧 [P1优化] 表切换操作: change_data_2025_12_pension -> change_data_2025_12_all_active
2025-09-16 14:31:10.067 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089, 薪资=2,375.00
2025-09-16 14:31:10.067 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565, 薪资=1,696.00
2025-09-16 14:31:10.067 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5528 | 最大可见行数已更新: 13 -> 50
2025-09-16 14:31:10.067 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5581 | 根据数据量(50)自动调整最大可见行数为: 50
2025-09-16 14:31:10.067 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1438 | 🔧 [智能分析] 开始异动表智能渲染: change_data
2025-09-16 14:31:10.067 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1443 | 🔧 [智能分析] 使用缓存的分析结果
2025-09-16 14:31:10.083 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1507 | 🔧 [智能分析] 数据质量评分: 0.93
2025-09-16 14:31:10.083 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1512 | 🔧 [智能分析] 异动表渲染完成: (50, 22)
2025-09-16 14:31:10.083 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: change_data, 行数: 50, 列数: 22
2025-09-16 14:31:10.098 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-09-16 14:31:10.098 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 22列
2025-09-16 14:31:10.118 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时19.6ms, 平均每行0.39ms
2025-09-16 14:31:10.118 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=19.6ms, 策略=small_dataset
2025-09-16 14:31:10.118 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-09-16 14:31:10.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第0行数据工号: 19990089
2025-09-16 14:31:10.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第1行数据工号: 20161565
2025-09-16 14:31:10.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第2行数据工号: 20191782
2025-09-16 14:31:10.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第3行数据工号: 20151515
2025-09-16 14:31:10.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第4行数据工号: 20181640
2025-09-16 14:31:10.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-09-16 14:31:10.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=0.00
2025-09-16 14:31:10.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=0.00
2025-09-16 14:31:10.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=0.00
2025-09-16 14:31:10.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=0.00
2025-09-16 14:31:10.130 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=0.00
2025-09-16 14:31:10.130 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:488 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-09-16 14:31:10.130 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:447 | 表格数据已设置: 50 行, 22 列
2025-09-16 14:31:10.133 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: change_data_2025_12_all_active
2025-09-16 14:31:10.141 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:474 | 🔧 [新架构] 为表格 change_data_2025_12_all_active 重新加载 29 个字段映射
2025-09-16 14:31:10.142 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 50 行, 耗时: 65.8ms
2025-09-16 14:31:10.142 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 14:31:10.143 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 14:31:10.144 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1708 | 🆕 [列宽保存] 未找到表格 change_data_2025_12_all_active 的列宽配置
2025-09-16 14:31:10.144 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 14:31:10.145 | INFO     | src.gui.prototype.prototype_main_window:set_data:1047 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=0，等待数据事件纠正
2025-09-16 14:31:10.147 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3984 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 22列
2025-09-16 14:31:10.147 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 22 个, 行号起始 1, 共 50 行
2025-09-16 14:31:10.148 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4000 | 🔧 [P0-新5修复] 表头强制刷新完成: change_data_2025_12_all_active, 传递参数: 22个表头
2025-09-16 14:31:10.148 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4004 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 22列
2025-09-16 14:31:10.207 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:401 | 已清理所有缓存
2025-09-16 14:31:10.208 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:11427 | ✅ [新架构] 组件状态一致性验证通过
2025-09-16 14:31:10.211 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:11390 | 🆕 [新架构] 导航迁移完成
2025-09-16 14:31:10.211 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4077 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-09-16 14:31:10.211 | INFO     | src.services.table_data_service:load_table_data:437 | 📨[data_event] published(cache)
2025-09-16 14:31:10.212 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7939 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-09-16 14:31:10.254 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: change_data_2025_12_all_active
2025-09-16 14:31:11.082 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '异动人员表 > 2025年 > 12月', '异动人员表 > 2025年', '异动人员表', '异动人员表 > 2025年 > 12月 > A岗职工']
2025-09-16 14:31:11.098 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7713 | 导航变化: 异动人员表 > 2025年 > 12月 > A岗职工
2025-09-16 14:31:11.098 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10932 | 🚫 [用户要求] 表格切换不显示加载条: change_data_2025_12_all_active -> None
2025-09-16 14:31:11.098 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10948 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-09-16 14:31:11.098 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8556 | 🔧 [表名生成] 处理异动表路径: ['异动人员表', '2025年', '12月', 'A岗职工']
2025-09-16 14:31:11.098 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8577 | 🔧 [表名生成] 命中最近导入映射: ('异动人员表', '2025', '12', 'A岗职工') -> change_data_2025_12_a_grade
2025-09-16 14:31:11.098 | INFO     | src.gui.prototype.prototype_main_window:_process_navigation_change:7775 | 🔧 [P0-紧急修复] 导航变化时更新状态栏: 异动人员表 > 2025年 > 12月 > A岗职工
2025-09-16 14:31:11.098 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:384 | 已清理表 change_data_2025_12_a_grade 的缓存
2025-09-16 14:31:11.098 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11620 | 已注册 4 个表格到表头管理器
2025-09-16 14:31:11.098 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7934 | 🆕 使用新架构加载数据: change_data_2025_12_a_grade（通过事件系统）
2025-09-16 14:31:11.098 | INFO     | src.services.table_data_service:load_table_data:377 | 加载表格数据: 页码: 1
2025-09-16 14:31:11.098 | INFO     | src.core.unified_data_request_manager:request_table_data:237 | 开始处理数据请求
2025-09-16 14:31:11.098 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:899 | 正在从表 change_data_2025_12_a_grade 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-09-16 14:31:11.113 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:1019 | 成功从表 change_data_2025_12_a_grade 获取第1页数据（含排序）: 50 行，总计62行
2025-09-16 14:31:11.113 | INFO     | src.core.unified_data_request_manager:request_table_data:287 | 数据请求处理完成: 字段=29, 行数=50, 耗时=15.6ms
2025-09-16 14:31:11.113 | INFO     | src.services.table_data_service:load_table_data:476 | [新架构] 使用统一格式管理器（单例优化）
2025-09-16 14:31:11.113 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1438 | 🔧 [智能分析] 开始异动表智能渲染: change_data
2025-09-16 14:31:11.113 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1443 | 🔧 [智能分析] 使用缓存的分析结果
2025-09-16 14:31:11.113 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1474 | 🔧 [智能分析] 已隐藏系统字段: ['import_time', 'id', 'employee_id', 'created_at', 'data_source', 'updated_at']
2025-09-16 14:31:11.129 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1507 | 🔧 [智能分析] 数据质量评分: 0.92
2025-09-16 14:31:11.129 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1512 | 🔧 [智能分析] 异动表渲染完成: (50, 23)
2025-09-16 14:31:11.129 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: change_data, 行数: 50, 列数: 23
2025-09-16 14:31:11.129 | INFO     | src.core.unified_state_manager:update_table_state:233 | 表状态已更新: change_data_2025_12_a_grade, 变更类型: None
2025-09-16 14:31:11.129 | INFO     | src.services.table_data_service:load_table_data:528 | [修复数据发布] 数据加载成功，发布更新事件: 50行
2025-09-16 14:31:11.129 | INFO     | src.services.table_data_service:load_table_data:532 | 📨[data_event] publish | table=change_data_2025_12_a_grade | rows=50 | page=1 | size=50 | total=62 | req_type=RequestType.INITIAL_LOAD | request_id=SV-1758004271129-e8e8e286
2025-09-16 14:31:11.129 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3874 | 📥[data_event] received | table=change_data_2025_12_a_grade | request_id=SV-1758004271129-e8e8e286
2025-09-16 14:31:11.129 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3906 | 数据内容: 50行 x 23列
2025-09-16 14:31:11.129 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3937 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: change_data_2025_12_a_grade, 50行
2025-09-16 14:31:11.129 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3952 | 🔧 [P0-修复] 排序数据处理前: 23列 - ['月份', '年份', '序号', '工号', '姓名']...
2025-09-16 14:31:11.129 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:11.129 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:11.129 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔧 [P0-修复] 字段映射后: 23列
2025-09-16 14:31:11.129 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:7617 | 🔧 [P0-修复] 系统字段过滤: 隐藏 3 个字段 - ['月份', '年份', '序号']
2025-09-16 14:31:11.129 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3956 | 🔧 [P0-修复] 系统字段过滤后: 20列
2025-09-16 14:31:11.129 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:31:11.150 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:31:11.150 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:31:11.150 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7670 | 表 change_data_2025_12_a_grade 没有用户偏好设置，显示所有可见字段
2025-09-16 14:31:11.160 | INFO     | src.gui.prototype.prototype_main_window:set_data:922 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-09-16 14:31:11.160 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:11.160 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:11.160 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 50行，表名: change_data_2025_12_a_grade
2025-09-16 14:31:11.160 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2822 | 🔧 [P1优化] 表切换操作: change_data_2025_12_all_active -> change_data_2025_12_a_grade
2025-09-16 14:31:11.160 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=34660024, 薪资=N/A
2025-09-16 14:31:11.160 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20222002, 薪资=N/A
2025-09-16 14:31:11.160 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5528 | 最大可见行数已更新: 50 -> 50
2025-09-16 14:31:11.160 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5581 | 根据数据量(50)自动调整最大可见行数为: 50
2025-09-16 14:31:11.160 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1438 | 🔧 [智能分析] 开始异动表智能渲染: change_data
2025-09-16 14:31:11.160 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1443 | 🔧 [智能分析] 使用缓存的分析结果
2025-09-16 14:31:11.176 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1507 | 🔧 [智能分析] 数据质量评分: 0.91
2025-09-16 14:31:11.176 | INFO     | src.modules.format_management.format_renderer:_render_change_data_dynamically:1512 | 🔧 [智能分析] 异动表渲染完成: (50, 20)
2025-09-16 14:31:11.176 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: change_data, 行数: 50, 列数: 20
2025-09-16 14:31:11.193 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7393 | 方案A：安全设置列数: 20
2025-09-16 14:31:11.207 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-09-16 14:31:11.207 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 20列
2025-09-16 14:31:11.207 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时0.0ms, 平均每行0.00ms
2025-09-16 14:31:11.207 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=0.0ms, 策略=small_dataset
2025-09-16 14:31:11.207 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-09-16 14:31:11.207 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第0行数据工号: 34660024
2025-09-16 14:31:11.207 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第1行数据工号: 20222002
2025-09-16 14:31:11.207 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第2行数据工号: 14660141
2025-09-16 14:31:11.207 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第3行数据工号: 34660002
2025-09-16 14:31:11.207 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第4行数据工号: 34660010
2025-09-16 14:31:11.207 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-09-16 14:31:11.207 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[0]: 工号=34660024, 薪资=N/A
2025-09-16 14:31:11.207 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[1]: 工号=20222002, 薪资=N/A
2025-09-16 14:31:11.207 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[2]: 工号=14660141, 薪资=N/A
2025-09-16 14:31:11.223 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[3]: 工号=34660002, 薪资=N/A
2025-09-16 14:31:11.223 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[4]: 工号=34660010, 薪资=N/A
2025-09-16 14:31:11.223 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:488 | 可见行数据顺序: ['34660024', '20222002', '14660141', '34660002', '34660010']
2025-09-16 14:31:11.223 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:447 | 表格数据已设置: 50 行, 20 列
2025-09-16 14:31:11.233 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: change_data_2025_12_a_grade
2025-09-16 14:31:11.233 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:474 | 🔧 [新架构] 为表格 change_data_2025_12_a_grade 重新加载 29 个字段映射
2025-09-16 14:31:11.234 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 50 行, 耗时: 73.0ms
2025-09-16 14:31:11.234 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 14:31:11.235 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 14:31:11.236 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1708 | 🆕 [列宽保存] 未找到表格 change_data_2025_12_a_grade 的列宽配置
2025-09-16 14:31:11.236 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 14:31:11.237 | INFO     | src.gui.prototype.prototype_main_window:set_data:1047 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=0，等待数据事件纠正
2025-09-16 14:31:11.239 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3984 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 20列
2025-09-16 14:31:11.239 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 20 个, 行号起始 1, 共 50 行
2025-09-16 14:31:11.240 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4000 | 🔧 [P0-新5修复] 表头强制刷新完成: change_data_2025_12_a_grade, 传递参数: 20个表头
2025-09-16 14:31:11.246 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4004 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 20列
2025-09-16 14:31:11.303 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:401 | 已清理所有缓存
2025-09-16 14:31:11.304 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:11427 | ✅ [新架构] 组件状态一致性验证通过
2025-09-16 14:31:11.306 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:11390 | 🆕 [新架构] 导航迁移完成
2025-09-16 14:31:11.306 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4077 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-09-16 14:31:11.307 | INFO     | src.services.table_data_service:load_table_data:555 | 📨[data_event] published
2025-09-16 14:31:11.307 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7939 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-09-16 14:31:11.344 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: change_data_2025_12_a_grade
2025-09-16 14:31:13.154 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2131 | MainWorkspaceArea 响应式适配: sm
2025-09-16 14:31:14.904 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月', '工资表 > 2025年', '工资表', '工资表 > 2025年 > 05月 > 离休人员']
2025-09-16 14:31:14.904 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7713 | 导航变化: 工资表 > 2025年 > 05月 > 离休人员
2025-09-16 14:31:14.904 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10932 | 🚫 [用户要求] 表格切换不显示加载条: change_data_2025_12_a_grade -> None
2025-09-16 14:31:14.904 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10948 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-09-16 14:31:14.904 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8724 | 🔧 [表名生成] 命中最近导入映射: ('工资表', '2025', '05', '离休人员') -> salary_data_2025_05_retired
2025-09-16 14:31:14.904 | INFO     | src.gui.prototype.prototype_main_window:_process_navigation_change:7775 | 🔧 [P0-紧急修复] 导航变化时更新状态栏: 工资表 > 2025年 > 05月 > 离休人员
2025-09-16 14:31:14.904 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:384 | 已清理表 salary_data_2025_05_retired 的缓存
2025-09-16 14:31:14.904 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11620 | 已注册 4 个表格到表头管理器
2025-09-16 14:31:14.904 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1197 | 🔧 [P1-3] 开始智能表头重影检测，共 5 个表格
2025-09-16 14:31:14.904 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1270 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-09-16 14:31:14.919 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1271 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-09-16 14:31:14.919 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7934 | 🆕 使用新架构加载数据: salary_data_2025_05_retired（通过事件系统）
2025-09-16 14:31:14.919 | INFO     | src.services.table_data_service:load_table_data:377 | 加载表格数据: 页码: 1
2025-09-16 14:31:14.919 | INFO     | src.core.unified_data_request_manager:request_table_data:237 | 开始处理数据请求
2025-09-16 14:31:14.919 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:899 | 正在从表 salary_data_2025_05_retired 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-09-16 14:31:14.919 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:1019 | 成功从表 salary_data_2025_05_retired 获取第1页数据（含排序）: 2 行，总计2行
2025-09-16 14:31:14.919 | INFO     | src.core.unified_data_request_manager:request_table_data:287 | 数据请求处理完成: 字段=24, 行数=2, 耗时=0.0ms
2025-09-16 14:31:14.919 | INFO     | src.services.table_data_service:load_table_data:476 | [新架构] 使用统一格式管理器（单例优化）
2025-09-16 14:31:14.919 | INFO     | src.modules.format_management.format_renderer:render_dataframe:127 | 🎯 [格式渲染] 隐藏字段配置: ['created_at', 'updated_at', 'id', 'sequence_number']
2025-09-16 14:31:14.919 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | 🎯 [格式渲染] 已隐藏字段: ['created_at', 'updated_at', 'id']
2025-09-16 14:31:14.919 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string_extract_last_two
2025-09-16 14:31:14.935 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-09-16 14:31:14.935 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-09-16 14:31:14.935 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 备注 -> 英文名: remarks -> 类型: string
2025-09-16 14:31:14.935 | INFO     | src.modules.format_management.field_registry:get_display_fields:1582 | 🔧 [P0-2修复] display_order转换完成: 17个字段，原始字段数: 17
2025-09-16 14:31:14.935 | INFO     | src.modules.format_management.format_renderer:render_dataframe:186 | 🔧 [DEBUG] table_type=retired_employees, display_fields=17个字段
2025-09-16 14:31:14.935 | INFO     | src.modules.format_management.format_renderer:render_dataframe:244 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 11/17 个字段
2025-09-16 14:31:14.935 | INFO     | src.modules.format_management.format_renderer:render_dataframe:252 | 🔧 [P0-关键修复] 保留未配置的业务字段: 10个
2025-09-16 14:31:14.935 | INFO     | src.modules.format_management.format_renderer:render_dataframe:257 | 🔧 [P0修复] 字段重排完成: 原始21个 -> 最终21个字段
2025-09-16 14:31:14.935 | INFO     | src.modules.format_management.format_renderer:render_dataframe:273 | 🎨 [格式渲染] DataFrame格式化完成: retired_employees, 行数: 2, 列数: 21
2025-09-16 14:31:14.935 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: retired_employees, 行数: 2, 列数: 21
2025-09-16 14:31:14.935 | INFO     | src.core.unified_state_manager:update_table_state:233 | 表状态已更新: salary_data_2025_05_retired, 变更类型: None
2025-09-16 14:31:14.935 | INFO     | src.services.table_data_service:load_table_data:528 | [修复数据发布] 数据加载成功，发布更新事件: 2行
2025-09-16 14:31:14.935 | INFO     | src.services.table_data_service:load_table_data:532 | 📨[data_event] publish | table=salary_data_2025_05_retired | rows=2 | page=1 | size=50 | total=2 | req_type=RequestType.INITIAL_LOAD | request_id=SV-1758004274935-104bd698
2025-09-16 14:31:14.951 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3874 | 📥[data_event] received | table=salary_data_2025_05_retired | request_id=SV-1758004274935-104bd698
2025-09-16 14:31:14.951 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3906 | 数据内容: 2行 x 21列
2025-09-16 14:31:14.951 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3937 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_05_retired, 2行
2025-09-16 14:31:14.951 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3952 | 🔧 [P0-修复] 排序数据处理前: 21列 - ['人员代码', '姓名', '部门名称', '增发一次\n性生活补贴', '护理费']...
2025-09-16 14:31:14.951 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:14.951 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:14.967 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔧 [P0-修复] 字段映射后: 21列
2025-09-16 14:31:14.967 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:7617 | 🔧 [P0-修复] 系统字段过滤: 隐藏 5 个字段 - ['年份', '月份', '序号', 'data_source', 'import_time']
2025-09-16 14:31:14.967 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3956 | 🔧 [P0-修复] 系统字段过滤后: 16列
2025-09-16 14:31:14.967 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:31:14.967 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:31:14.967 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:31:14.967 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7670 | 表 salary_data_2025_05_retired 没有用户偏好设置，显示所有可见字段
2025-09-16 14:31:14.967 | INFO     | src.gui.prototype.prototype_main_window:set_data:922 | 通过公共接口设置新的表格数据(DataFrame): 2 行
2025-09-16 14:31:14.967 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:14.967 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:14.982 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 2行，表名: salary_data_2025_05_retired
2025-09-16 14:31:14.982 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7393 | 方案A：安全设置列数: 0
2025-09-16 14:31:14.982 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2822 | 🔧 [P1优化] 表切换操作: change_data_2025_12_a_grade -> salary_data_2025_05_retired
2025-09-16 14:31:14.982 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19289006, 薪资=N/A
2025-09-16 14:31:14.982 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=19339009, 薪资=N/A
2025-09-16 14:31:15.001 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5528 | 最大可见行数已更新: 50 -> 10
2025-09-16 14:31:15.001 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5581 | 根据数据量(2)自动调整最大可见行数为: 10
2025-09-16 14:31:15.001 | INFO     | src.modules.format_management.format_renderer:render_dataframe:127 | 🎯 [格式渲染] 隐藏字段配置: ['created_at', 'updated_at', 'id', 'sequence_number', 'row_number']
2025-09-16 14:31:15.001 | INFO     | src.modules.format_management.format_renderer:render_dataframe:135 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-09-16 14:31:15.001 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-09-16 14:31:15.001 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-09-16 14:31:15.001 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 备注 -> 英文名: 备注 -> 类型: string
2025-09-16 14:31:15.014 | INFO     | src.modules.format_management.field_registry:get_display_fields:1582 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-09-16 14:31:15.014 | INFO     | src.modules.format_management.format_renderer:render_dataframe:186 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-09-16 14:31:15.014 | INFO     | src.modules.format_management.format_renderer:render_dataframe:244 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 6/25 个字段
2025-09-16 14:31:15.014 | INFO     | src.modules.format_management.format_renderer:render_dataframe:252 | 🔧 [P0-关键修复] 保留未配置的业务字段: 10个
2025-09-16 14:31:15.014 | INFO     | src.modules.format_management.format_renderer:render_dataframe:257 | 🔧 [P0修复] 字段重排完成: 原始16个 -> 最终16个字段
2025-09-16 14:31:15.014 | INFO     | src.modules.format_management.format_renderer:render_dataframe:273 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 2, 列数: 16
2025-09-16 14:31:15.014 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 2, 列数: 16
2025-09-16 14:31:15.014 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2983 | [列对齐] 已对齐记录与表头: rows=2->2, cols=16
2025-09-16 14:31:15.014 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 2行数据
2025-09-16 14:31:15.014 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 2行 x 16列
2025-09-16 14:31:15.014 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时0.0ms, 平均每行0.00ms
2025-09-16 14:31:15.014 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=2, 渲染时间=0.0ms, 策略=small_dataset
2025-09-16 14:31:15.014 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-09-16 14:31:15.014 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第0行数据工号: 19289006
2025-09-16 14:31:15.014 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第1行数据工号: 19339009
2025-09-16 14:31:15.014 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 2
2025-09-16 14:31:15.014 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[0]: 工号=19289006, 薪资=N/A
2025-09-16 14:31:15.014 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[1]: 工号=19339009, 薪资=N/A
2025-09-16 14:31:15.014 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:488 | 可见行数据顺序: ['19289006', '19339009']
2025-09-16 14:31:15.014 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:447 | 表格数据已设置: 2 行, 16 列
2025-09-16 14:31:15.029 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_05_retired
2025-09-16 14:31:15.041 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:474 | 🔧 [新架构] 为表格 salary_data_2025_05_retired 重新加载 29 个字段映射
2025-09-16 14:31:15.042 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 2 行, 耗时: 46.8ms
2025-09-16 14:31:15.042 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 14:31:15.043 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 14:31:15.043 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1708 | 🆕 [列宽保存] 未找到表格 salary_data_2025_05_retired 的列宽配置
2025-09-16 14:31:15.045 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 14:31:15.046 | INFO     | src.gui.prototype.prototype_main_window:set_data:1047 | 📊[pagination-write] 跳过写入当前页数据量(total=2)，保持 total=0，等待数据事件纠正
2025-09-16 14:31:15.047 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3984 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 2行, 16列
2025-09-16 14:31:15.047 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 16 个, 行号起始 1, 共 2 行
2025-09-16 14:31:15.048 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4000 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_05_retired, 传递参数: 16个表头
2025-09-16 14:31:15.048 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4004 | 🔧 [统一数据设置] 数据已成功设置到UI: 2行, 16列
2025-09-16 14:31:15.105 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:401 | 已清理所有缓存
2025-09-16 14:31:15.106 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:11427 | ✅ [新架构] 组件状态一致性验证通过
2025-09-16 14:31:15.109 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:11390 | 🆕 [新架构] 导航迁移完成
2025-09-16 14:31:15.109 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4077 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-09-16 14:31:15.110 | INFO     | src.services.table_data_service:load_table_data:555 | 📨[data_event] published
2025-09-16 14:31:15.110 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7939 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-09-16 14:31:15.163 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_05_retired
2025-09-16 14:31:18.322 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月', '工资表 > 2025年', '工资表', '工资表 > 2025年 > 05月 > 退休人员']
2025-09-16 14:31:18.322 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7713 | 导航变化: 工资表 > 2025年 > 05月 > 退休人员
2025-09-16 14:31:18.322 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10932 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_retired -> None
2025-09-16 14:31:18.322 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10948 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-09-16 14:31:18.322 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8724 | 🔧 [表名生成] 命中最近导入映射: ('工资表', '2025', '05', '退休人员') -> salary_data_2025_05_pension
2025-09-16 14:31:18.322 | INFO     | src.gui.prototype.prototype_main_window:_process_navigation_change:7775 | 🔧 [P0-紧急修复] 导航变化时更新状态栏: 工资表 > 2025年 > 05月 > 退休人员
2025-09-16 14:31:18.322 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:384 | 已清理表 salary_data_2025_05_pension 的缓存
2025-09-16 14:31:18.322 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11620 | 已注册 4 个表格到表头管理器
2025-09-16 14:31:18.322 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1197 | 🔧 [P1-3] 开始智能表头重影检测，共 5 个表格
2025-09-16 14:31:18.322 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1270 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-09-16 14:31:18.322 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1271 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-09-16 14:31:18.322 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7934 | 🆕 使用新架构加载数据: salary_data_2025_05_pension（通过事件系统）
2025-09-16 14:31:18.322 | INFO     | src.services.table_data_service:load_table_data:377 | 加载表格数据: 页码: 1
2025-09-16 14:31:18.337 | INFO     | src.core.unified_data_request_manager:request_table_data:237 | 开始处理数据请求
2025-09-16 14:31:18.337 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:899 | 正在从表 salary_data_2025_05_pension 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-09-16 14:31:18.337 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:1019 | 成功从表 salary_data_2025_05_pension 获取第1页数据（含排序）: 13 行，总计13行
2025-09-16 14:31:18.337 | INFO     | src.core.unified_data_request_manager:request_table_data:287 | 数据请求处理完成: 字段=35, 行数=13, 耗时=0.0ms
2025-09-16 14:31:18.337 | INFO     | src.services.table_data_service:load_table_data:476 | [新架构] 使用统一格式管理器（单例优化）
2025-09-16 14:31:18.353 | INFO     | src.modules.format_management.format_renderer:render_dataframe:127 | 🎯 [格式渲染] 隐藏字段配置: ['created_at', 'updated_at', 'id', 'sequence_number']
2025-09-16 14:31:18.353 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | 🎯 [格式渲染] 已隐藏字段: ['created_at', 'updated_at', 'id']
2025-09-16 14:31:18.353 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string_extract_last_two
2025-09-16 14:31:18.353 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-09-16 14:31:18.353 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-09-16 14:31:18.368 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 备注 -> 英文名: remarks -> 类型: string
2025-09-16 14:31:18.368 | INFO     | src.modules.format_management.field_registry:get_display_fields:1582 | 🔧 [P0-2修复] display_order转换完成: 28个字段，原始字段数: 28
2025-09-16 14:31:18.368 | INFO     | src.modules.format_management.format_renderer:render_dataframe:186 | 🔧 [DEBUG] table_type=pension_employees, display_fields=28个字段
2025-09-16 14:31:18.368 | INFO     | src.modules.format_management.format_renderer:render_dataframe:244 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 28/28 个字段
2025-09-16 14:31:18.368 | INFO     | src.modules.format_management.format_renderer:render_dataframe:252 | 🔧 [P0-关键修复] 保留未配置的业务字段: 4个
2025-09-16 14:31:18.368 | INFO     | src.modules.format_management.format_renderer:render_dataframe:257 | 🔧 [P0修复] 字段重排完成: 原始32个 -> 最终32个字段
2025-09-16 14:31:18.368 | INFO     | src.modules.format_management.format_renderer:render_dataframe:273 | 🎨 [格式渲染] DataFrame格式化完成: pension_employees, 行数: 13, 列数: 32
2025-09-16 14:31:18.368 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: pension_employees, 行数: 13, 列数: 32
2025-09-16 14:31:18.368 | INFO     | src.core.unified_state_manager:update_table_state:233 | 表状态已更新: salary_data_2025_05_pension, 变更类型: None
2025-09-16 14:31:18.368 | INFO     | src.services.table_data_service:load_table_data:528 | [修复数据发布] 数据加载成功，发布更新事件: 13行
2025-09-16 14:31:18.368 | INFO     | src.services.table_data_service:load_table_data:532 | 📨[data_event] publish | table=salary_data_2025_05_pension | rows=13 | page=1 | size=50 | total=13 | req_type=RequestType.INITIAL_LOAD | request_id=SV-1758004278368-0370bae7
2025-09-16 14:31:18.368 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3874 | 📥[data_event] received | table=salary_data_2025_05_pension | request_id=SV-1758004278368-0370bae7
2025-09-16 14:31:18.368 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3906 | 数据内容: 13行 x 32列
2025-09-16 14:31:18.368 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3937 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_05_pension, 13行
2025-09-16 14:31:18.368 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3952 | 🔧 [P0-修复] 排序数据处理前: 32列 - ['人员代码', '姓名', '部门名称', '人员类别代码', '基本退休费']...
2025-09-16 14:31:18.368 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:18.384 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:18.384 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔧 [P0-修复] 字段映射后: 32列
2025-09-16 14:31:18.384 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:7617 | 🔧 [P0-修复] 系统字段过滤: 隐藏 5 个字段 - ['年份', '月份', '序号', 'data_source', 'import_time']
2025-09-16 14:31:18.384 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3956 | 🔧 [P0-修复] 系统字段过滤后: 27列
2025-09-16 14:31:18.384 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:31:18.400 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:31:18.400 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:31:18.400 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7670 | 表 salary_data_2025_05_pension 没有用户偏好设置，显示所有可见字段
2025-09-16 14:31:18.400 | INFO     | src.gui.prototype.prototype_main_window:set_data:922 | 通过公共接口设置新的表格数据(DataFrame): 13 行
2025-09-16 14:31:18.400 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:18.400 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:18.400 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 13行，表名: salary_data_2025_05_pension
2025-09-16 14:31:18.400 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2822 | 🔧 [P1优化] 表切换操作: salary_data_2025_05_retired -> salary_data_2025_05_pension
2025-09-16 14:31:18.415 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19709165, 薪资=N/A
2025-09-16 14:31:18.415 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=19981259, 薪资=N/A
2025-09-16 14:31:18.415 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5528 | 最大可见行数已更新: 10 -> 13
2025-09-16 14:31:18.415 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5581 | 根据数据量(13)自动调整最大可见行数为: 13
2025-09-16 14:31:18.415 | INFO     | src.modules.format_management.format_renderer:render_dataframe:127 | 🎯 [格式渲染] 隐藏字段配置: ['created_at', 'updated_at', 'id', 'sequence_number', 'row_number']
2025-09-16 14:31:18.415 | INFO     | src.modules.format_management.format_renderer:render_dataframe:135 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-09-16 14:31:18.431 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-09-16 14:31:18.431 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-09-16 14:31:18.431 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 备注 -> 英文名: 备注 -> 类型: string
2025-09-16 14:31:18.431 | INFO     | src.modules.format_management.field_registry:get_display_fields:1582 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-09-16 14:31:18.431 | INFO     | src.modules.format_management.format_renderer:render_dataframe:186 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-09-16 14:31:18.431 | INFO     | src.modules.format_management.format_renderer:render_dataframe:244 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 12/25 个字段
2025-09-16 14:31:18.431 | INFO     | src.modules.format_management.format_renderer:render_dataframe:252 | 🔧 [P0-关键修复] 保留未配置的业务字段: 15个
2025-09-16 14:31:18.431 | INFO     | src.modules.format_management.format_renderer:render_dataframe:257 | 🔧 [P0修复] 字段重排完成: 原始27个 -> 最终27个字段
2025-09-16 14:31:18.431 | INFO     | src.modules.format_management.format_renderer:render_dataframe:273 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 13, 列数: 27
2025-09-16 14:31:18.431 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 13, 列数: 27
2025-09-16 14:31:18.431 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7393 | 方案A：安全设置列数: 27
2025-09-16 14:31:18.447 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 13行数据
2025-09-16 14:31:18.447 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 13行 x 27列
2025-09-16 14:31:18.447 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时0.0ms, 平均每行0.00ms
2025-09-16 14:31:18.447 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=13, 渲染时间=0.0ms, 策略=small_dataset
2025-09-16 14:31:18.447 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-09-16 14:31:18.447 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第0行数据工号: 19709165
2025-09-16 14:31:18.447 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第1行数据工号: 19981259
2025-09-16 14:31:18.447 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第2行数据工号: 19721294
2025-09-16 14:31:18.447 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第3行数据工号: 19841258
2025-09-16 14:31:18.447 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第4行数据工号: 19499098
2025-09-16 14:31:18.447 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 13
2025-09-16 14:31:18.447 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[0]: 工号=19709165, 薪资=N/A
2025-09-16 14:31:18.447 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[1]: 工号=19981259, 薪资=N/A
2025-09-16 14:31:18.447 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[2]: 工号=19721294, 薪资=N/A
2025-09-16 14:31:18.447 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[3]: 工号=19841258, 薪资=N/A
2025-09-16 14:31:18.447 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[4]: 工号=19499098, 薪资=N/A
2025-09-16 14:31:18.447 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:488 | 可见行数据顺序: ['19709165', '19981259', '19721294', '19841258', '19499098']
2025-09-16 14:31:18.447 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:447 | 表格数据已设置: 13 行, 27 列
2025-09-16 14:31:18.463 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_05_pension
2025-09-16 14:31:18.473 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:474 | 🔧 [新架构] 为表格 salary_data_2025_05_pension 重新加载 29 个字段映射
2025-09-16 14:31:18.473 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 13 行, 耗时: 63.4ms
2025-09-16 14:31:18.474 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 14:31:18.474 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 14:31:18.476 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1708 | 🆕 [列宽保存] 未找到表格 salary_data_2025_05_pension 的列宽配置
2025-09-16 14:31:18.476 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 14:31:18.477 | INFO     | src.gui.prototype.prototype_main_window:set_data:1047 | 📊[pagination-write] 跳过写入当前页数据量(total=13)，保持 total=0，等待数据事件纠正
2025-09-16 14:31:18.478 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3984 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 13行, 27列
2025-09-16 14:31:18.478 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 27 个, 行号起始 1, 共 13 行
2025-09-16 14:31:18.479 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4000 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_05_pension, 传递参数: 27个表头
2025-09-16 14:31:18.479 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4004 | 🔧 [统一数据设置] 数据已成功设置到UI: 13行, 27列
2025-09-16 14:31:18.542 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:401 | 已清理所有缓存
2025-09-16 14:31:18.543 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:11427 | ✅ [新架构] 组件状态一致性验证通过
2025-09-16 14:31:18.545 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:11390 | 🆕 [新架构] 导航迁移完成
2025-09-16 14:31:18.546 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4077 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-09-16 14:31:18.546 | INFO     | src.services.table_data_service:load_table_data:555 | 📨[data_event] published
2025-09-16 14:31:18.547 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7939 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-09-16 14:31:18.547 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:1028 | 导航选择: 工资表 > 2025年 > 05月 > 退休人员
2025-09-16 14:31:18.582 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_05_pension
2025-09-16 14:31:19.785 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月', '工资表 > 2025年', '工资表', '工资表 > 2025年 > 05月 > A岗职工']
2025-09-16 14:31:19.785 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7713 | 导航变化: 工资表 > 2025年 > 05月 > A岗职工
2025-09-16 14:31:19.785 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10932 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_pension -> None
2025-09-16 14:31:19.785 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10948 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-09-16 14:31:19.785 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8724 | 🔧 [表名生成] 命中最近导入映射: ('工资表', '2025', '05', 'A岗职工') -> salary_data_2025_05_a_grade
2025-09-16 14:31:19.785 | INFO     | src.gui.prototype.prototype_main_window:_process_navigation_change:7775 | 🔧 [P0-紧急修复] 导航变化时更新状态栏: 工资表 > 2025年 > 05月 > A岗职工
2025-09-16 14:31:19.785 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:384 | 已清理表 salary_data_2025_05_a_grade 的缓存
2025-09-16 14:31:19.785 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11620 | 已注册 4 个表格到表头管理器
2025-09-16 14:31:19.785 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7934 | 🆕 使用新架构加载数据: salary_data_2025_05_a_grade（通过事件系统）
2025-09-16 14:31:19.785 | INFO     | src.services.table_data_service:load_table_data:377 | 加载表格数据: 页码: 1
2025-09-16 14:31:19.785 | INFO     | src.core.unified_data_request_manager:request_table_data:237 | 开始处理数据请求
2025-09-16 14:31:19.801 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:899 | 正在从表 salary_data_2025_05_a_grade 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-09-16 14:31:19.801 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:1019 | 成功从表 salary_data_2025_05_a_grade 获取第1页数据（含排序）: 50 行，总计62行
2025-09-16 14:31:19.801 | INFO     | src.core.unified_data_request_manager:request_table_data:287 | 数据请求处理完成: 字段=29, 行数=50, 耗时=15.7ms
2025-09-16 14:31:19.801 | INFO     | src.modules.format_management.format_renderer:render_dataframe:127 | 🎯 [格式渲染] 隐藏字段配置: ['created_at', 'updated_at', 'id', 'sequence_number']
2025-09-16 14:31:19.801 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | 🎯 [格式渲染] 已隐藏字段: ['created_at', 'updated_at', 'id']
2025-09-16 14:31:19.801 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-09-16 14:31:19.816 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-09-16 14:31:19.816 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-09-16 14:31:19.816 | INFO     | src.modules.format_management.field_registry:get_display_fields:1582 | 🔧 [P0-2修复] display_order转换完成: 22个字段，原始字段数: 22
2025-09-16 14:31:19.816 | INFO     | src.modules.format_management.format_renderer:render_dataframe:186 | 🔧 [DEBUG] table_type=a_grade_employees, display_fields=22个字段
2025-09-16 14:31:19.816 | INFO     | src.modules.format_management.format_renderer:render_dataframe:244 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 22/22 个字段
2025-09-16 14:31:19.816 | INFO     | src.modules.format_management.format_renderer:render_dataframe:252 | 🔧 [P0-关键修复] 保留未配置的业务字段: 4个
2025-09-16 14:31:19.816 | INFO     | src.modules.format_management.format_renderer:render_dataframe:257 | 🔧 [P0修复] 字段重排完成: 原始26个 -> 最终26个字段
2025-09-16 14:31:19.816 | INFO     | src.modules.format_management.format_renderer:render_dataframe:273 | 🎨 [格式渲染] DataFrame格式化完成: a_grade_employees, 行数: 50, 列数: 26
2025-09-16 14:31:19.816 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: a_grade_employees, 行数: 50, 列数: 26
2025-09-16 14:31:19.831 | INFO     | src.core.unified_state_manager:update_table_state:233 | 表状态已更新: salary_data_2025_05_a_grade, 变更类型: None
2025-09-16 14:31:19.831 | INFO     | src.services.table_data_service:load_table_data:528 | [修复数据发布] 数据加载成功，发布更新事件: 50行
2025-09-16 14:31:19.831 | INFO     | src.services.table_data_service:load_table_data:532 | 📨[data_event] publish | table=salary_data_2025_05_a_grade | rows=50 | page=1 | size=50 | total=62 | req_type=RequestType.INITIAL_LOAD | request_id=SV-1758004279831-6706599f
2025-09-16 14:31:19.831 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3874 | 📥[data_event] received | table=salary_data_2025_05_a_grade | request_id=SV-1758004279831-6706599f
2025-09-16 14:31:19.831 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3906 | 数据内容: 50行 x 26列
2025-09-16 14:31:19.831 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3937 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_05_a_grade, 50行
2025-09-16 14:31:19.831 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3952 | 🔧 [P0-修复] 排序数据处理前: 26列 - ['工号', '姓名', '部门名称', '人员类别', '人员类别代码']...
2025-09-16 14:31:19.831 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:19.831 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:19.831 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔧 [P0-修复] 字段映射后: 26列
2025-09-16 14:31:19.831 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:7617 | 🔧 [P0-修复] 系统字段过滤: 隐藏 5 个字段 - ['年份', '月份', '序号', 'data_source', 'import_time']
2025-09-16 14:31:19.831 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3956 | 🔧 [P0-修复] 系统字段过滤后: 23列
2025-09-16 14:31:19.831 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:31:19.847 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:31:19.847 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:31:19.847 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7670 | 表 salary_data_2025_05_a_grade 没有用户偏好设置，显示所有可见字段
2025-09-16 14:31:19.847 | INFO     | src.gui.prototype.prototype_main_window:set_data:922 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-09-16 14:31:19.847 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:19.847 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:19.863 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_05_a_grade
2025-09-16 14:31:19.863 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引1, 名称'工号'
2025-09-16 14:31:19.863 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引2, 名称'工号'
2025-09-16 14:31:19.863 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引3, 名称'工号'
2025-09-16 14:31:19.863 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引4, 名称'工号'
2025-09-16 14:31:19.863 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引5, 名称'工号'
2025-09-16 14:31:19.863 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引6, 名称'工号'
2025-09-16 14:31:19.863 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引7, 名称'工号'
2025-09-16 14:31:19.863 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引27, 名称'工号'
2025-09-16 14:31:19.863 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引28, 名称'工号'
2025-09-16 14:31:19.863 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引29, 名称'工号'
2025-09-16 14:31:19.863 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引30, 名称'工号'
2025-09-16 14:31:19.863 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引31, 名称'工号'
2025-09-16 14:31:19.863 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引32, 名称'工号'
2025-09-16 14:31:19.863 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引33, 名称'工号'
2025-09-16 14:31:19.863 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引34, 名称'工号'
2025-09-16 14:31:19.863 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4899 | 🔧 [方案A2] 表头去重完成: 35 -> 20
2025-09-16 14:31:19.863 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4902 | 🔧 [方案A2] 移除的重复项: 工号(索引1), 工号(索引2), 工号(索引3), 工号(索引4), 工号(索引5), 工号(索引6), 工号(索引7), 工号(索引27), 工号(索引28), 工号(索引29), 工号(索引30), 工号(索引31), 工号(索引32), 工号(索引33), 工号(索引34)
2025-09-16 14:31:19.878 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7393 | 方案A：安全设置列数: 0
2025-09-16 14:31:19.878 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2822 | 🔧 [P1优化] 表切换操作: salary_data_2025_05_pension -> salary_data_2025_05_a_grade
2025-09-16 14:31:19.878 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=34660024, 薪资=N/A
2025-09-16 14:31:19.878 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20222002, 薪资=N/A
2025-09-16 14:31:19.878 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5528 | 最大可见行数已更新: 13 -> 50
2025-09-16 14:31:19.878 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5581 | 根据数据量(50)自动调整最大可见行数为: 50
2025-09-16 14:31:19.878 | INFO     | src.modules.format_management.format_renderer:render_dataframe:127 | 🎯 [格式渲染] 隐藏字段配置: ['created_at', 'updated_at', 'id', 'sequence_number']
2025-09-16 14:31:19.878 | INFO     | src.modules.format_management.format_renderer:render_dataframe:135 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-09-16 14:31:19.894 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-09-16 14:31:19.894 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-09-16 14:31:19.894 | INFO     | src.modules.format_management.field_registry:get_display_fields:1582 | 🔧 [P0-2修复] display_order转换完成: 22个字段，原始字段数: 22
2025-09-16 14:31:19.894 | INFO     | src.modules.format_management.format_renderer:render_dataframe:186 | 🔧 [DEBUG] table_type=a_grade_employees, display_fields=22个字段
2025-09-16 14:31:19.894 | INFO     | src.modules.format_management.format_renderer:render_dataframe:244 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 20/22 个字段
2025-09-16 14:31:19.894 | INFO     | src.modules.format_management.format_renderer:render_dataframe:257 | 🔧 [P0修复] 字段重排完成: 原始20个 -> 最终20个字段
2025-09-16 14:31:19.894 | INFO     | src.modules.format_management.format_renderer:render_dataframe:273 | 🎨 [格式渲染] DataFrame格式化完成: a_grade_employees, 行数: 50, 列数: 20
2025-09-16 14:31:19.894 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: a_grade_employees, 行数: 50, 列数: 20
2025-09-16 14:31:19.910 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-09-16 14:31:19.910 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 20列
2025-09-16 14:31:19.928 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时18.6ms, 平均每行0.37ms
2025-09-16 14:31:19.928 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=18.6ms, 策略=small_dataset
2025-09-16 14:31:19.928 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-09-16 14:31:19.928 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第0行数据工号: 34660024
2025-09-16 14:31:19.928 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第1行数据工号: 20222002
2025-09-16 14:31:19.928 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第2行数据工号: 14660141
2025-09-16 14:31:19.928 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第3行数据工号: 34660002
2025-09-16 14:31:19.928 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第4行数据工号: 34660010
2025-09-16 14:31:19.928 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-09-16 14:31:19.928 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[0]: 工号=34660024, 薪资=N/A
2025-09-16 14:31:19.928 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[1]: 工号=20222002, 薪资=N/A
2025-09-16 14:31:19.928 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[2]: 工号=14660141, 薪资=N/A
2025-09-16 14:31:19.928 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[3]: 工号=34660002, 薪资=N/A
2025-09-16 14:31:19.928 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[4]: 工号=34660010, 薪资=N/A
2025-09-16 14:31:19.928 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:488 | 可见行数据顺序: ['34660024', '20222002', '14660141', '34660002', '34660010']
2025-09-16 14:31:19.928 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:447 | 表格数据已设置: 50 行, 20 列
2025-09-16 14:31:19.940 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_05_a_grade
2025-09-16 14:31:19.948 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:474 | 🔧 [新架构] 为表格 salary_data_2025_05_a_grade 重新加载 29 个字段映射
2025-09-16 14:31:19.949 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 50 行, 耗时: 77.1ms
2025-09-16 14:31:19.950 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 14:31:19.950 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 14:31:19.951 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1708 | 🆕 [列宽保存] 未找到表格 salary_data_2025_05_a_grade 的列宽配置
2025-09-16 14:31:19.952 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 14:31:19.953 | INFO     | src.gui.prototype.prototype_main_window:set_data:1047 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=0，等待数据事件纠正
2025-09-16 14:31:19.954 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3984 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 23列
2025-09-16 14:31:19.954 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 23 个, 行号起始 1, 共 50 行
2025-09-16 14:31:19.958 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4000 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_05_a_grade, 传递参数: 23个表头
2025-09-16 14:31:19.962 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4004 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 23列
2025-09-16 14:31:20.024 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:401 | 已清理所有缓存
2025-09-16 14:31:20.027 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:11427 | ✅ [新架构] 组件状态一致性验证通过
2025-09-16 14:31:20.028 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:11390 | 🆕 [新架构] 导航迁移完成
2025-09-16 14:31:20.028 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4077 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-09-16 14:31:20.029 | INFO     | src.services.table_data_service:load_table_data:555 | 📨[data_event] published
2025-09-16 14:31:20.029 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7939 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-09-16 14:31:20.031 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed:4939 | 🔧 [P1-强化] 延迟调整时发现列数不匹配: 期望20列, 实际23列
2025-09-16 14:31:20.032 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7393 | 方案A：安全设置列数: 20
2025-09-16 14:31:20.033 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed:4956 | 🔧 [P1-强化] 最终列数修复: 20列
2025-09-16 14:31:20.066 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_05_a_grade
2025-09-16 14:31:20.104 | WARNING  | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2429 | 🔧 [方案A1] 发现重复表头: 列1(工号)，原始列在0
2025-09-16 14:31:20.104 | INFO     | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2440 | 🔧 [方案A1] 检测到1个重复表头，开始移除
2025-09-16 14:31:20.104 | INFO     | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2448 | 🔧 [方案A1] 已移除重复列: 列1(工号)
2025-09-16 14:31:20.104 | INFO     | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2464 | 🔧 [方案A1] 表格显示已刷新，列宽已调整
2025-09-16 14:31:21.635 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月', '工资表 > 2025年', '工资表', '工资表 > 2025年 > 05月 > A岗职工']
2025-09-16 14:31:21.635 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7713 | 导航变化: 工资表 > 2025年 > 05月 > 全部在职人员
2025-09-16 14:31:21.635 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10932 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_a_grade -> None
2025-09-16 14:31:21.635 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10948 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-09-16 14:31:21.635 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8724 | 🔧 [表名生成] 命中最近导入映射: ('工资表', '2025', '05', '全部在职人员') -> salary_data_2025_05_all_active
2025-09-16 14:31:21.635 | INFO     | src.gui.prototype.prototype_main_window:_process_navigation_change:7775 | 🔧 [P0-紧急修复] 导航变化时更新状态栏: 工资表 > 2025年 > 05月 > 全部在职人员
2025-09-16 14:31:21.635 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:384 | 已清理表 salary_data_2025_05_all_active 的缓存
2025-09-16 14:31:21.651 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11620 | 已注册 4 个表格到表头管理器
2025-09-16 14:31:21.651 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7934 | 🆕 使用新架构加载数据: salary_data_2025_05_all_active（通过事件系统）
2025-09-16 14:31:21.651 | INFO     | src.services.table_data_service:load_table_data:377 | 加载表格数据: 页码: 1
2025-09-16 14:31:21.651 | INFO     | src.services.table_data_service:load_table_data:401 | [缓存命中] 使用缓存数据: 第1页
2025-09-16 14:31:21.651 | INFO     | src.services.table_data_service:load_table_data:412 | 📨[data_event] publish(cache) | table=salary_data_2025_05_all_active | rows=50 | page=1 | size=50 | total=1396 | request_id=SV-1758004281651-b5115d15-C
2025-09-16 14:31:21.651 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3874 | 📥[data_event] received | table=salary_data_2025_05_all_active | request_id=SV-1758004281651-b5115d15-C
2025-09-16 14:31:21.651 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3906 | 数据内容: 50行 x 28列
2025-09-16 14:31:21.651 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3937 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_05_all_active, 50行
2025-09-16 14:31:21.651 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3952 | 🔧 [P0-修复] 排序数据处理前: 28列 - ['工号', '姓名', '部门名称', '人员类别', '人员类别代码']...
2025-09-16 14:31:21.651 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:21.651 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:21.651 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔧 [P0-修复] 字段映射后: 28列
2025-09-16 14:31:21.651 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:7617 | 🔧 [P0-修复] 系统字段过滤: 隐藏 5 个字段 - ['年份', '月份', '序号', 'data_source', 'import_time']
2025-09-16 14:31:21.651 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3956 | 🔧 [P0-修复] 系统字段过滤后: 25列
2025-09-16 14:31:21.651 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:31:21.666 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:31:21.666 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:31:21.666 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7670 | 表 salary_data_2025_05_all_active 没有用户偏好设置，显示所有可见字段
2025-09-16 14:31:21.666 | INFO     | src.gui.prototype.prototype_main_window:set_data:922 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-09-16 14:31:21.666 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:21.666 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:21.682 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_05_all_active
2025-09-16 14:31:21.682 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引1, 名称'工号'
2025-09-16 14:31:21.682 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引2, 名称'工号'
2025-09-16 14:31:21.682 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引3, 名称'工号'
2025-09-16 14:31:21.682 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引4, 名称'工号'
2025-09-16 14:31:21.682 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引5, 名称'工号'
2025-09-16 14:31:21.682 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引6, 名称'工号'
2025-09-16 14:31:21.682 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引7, 名称'工号'
2025-09-16 14:31:21.682 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引24, 名称'工号'
2025-09-16 14:31:21.682 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引25, 名称'工号'
2025-09-16 14:31:21.682 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引26, 名称'工号'
2025-09-16 14:31:21.682 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引27, 名称'工号'
2025-09-16 14:31:21.682 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引28, 名称'工号'
2025-09-16 14:31:21.682 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引29, 名称'工号'
2025-09-16 14:31:21.682 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引30, 名称'工号'
2025-09-16 14:31:21.682 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4874 | 🔧 [方案A2] 发现重复表头: 索引31, 名称'工号'
2025-09-16 14:31:21.682 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4899 | 🔧 [方案A2] 表头去重完成: 37 -> 22
2025-09-16 14:31:21.682 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_preprocess_headers:4902 | 🔧 [方案A2] 移除的重复项: 工号(索引1), 工号(索引2), 工号(索引3), 工号(索引4), 工号(索引5), 工号(索引6), 工号(索引7), 工号(索引24), 工号(索引25), 工号(索引26), 工号(索引27), 工号(索引28), 工号(索引29), 工号(索引30), 工号(索引31)
2025-09-16 14:31:21.682 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7393 | 方案A：安全设置列数: 0
2025-09-16 14:31:21.698 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2822 | 🔧 [P1优化] 表切换操作: salary_data_2025_05_a_grade -> salary_data_2025_05_all_active
2025-09-16 14:31:21.698 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089, 薪资=2375
2025-09-16 14:31:21.698 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565, 薪资=1696
2025-09-16 14:31:21.698 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5528 | 最大可见行数已更新: 50 -> 50
2025-09-16 14:31:21.698 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5581 | 根据数据量(50)自动调整最大可见行数为: 50
2025-09-16 14:31:21.698 | INFO     | src.modules.format_management.format_renderer:render_dataframe:127 | 🎯 [格式渲染] 隐藏字段配置: ['created_at', 'updated_at', 'id', 'sequence_number', 'row_number']
2025-09-16 14:31:21.698 | INFO     | src.modules.format_management.format_renderer:render_dataframe:135 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-09-16 14:31:21.713 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-09-16 14:31:21.713 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-09-16 14:31:21.713 | INFO     | src.modules.format_management.field_registry:get_display_fields:1582 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-09-16 14:31:21.713 | INFO     | src.modules.format_management.format_renderer:render_dataframe:186 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-09-16 14:31:21.713 | INFO     | src.modules.format_management.format_renderer:render_dataframe:244 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 22/25 个字段
2025-09-16 14:31:21.713 | INFO     | src.modules.format_management.format_renderer:render_dataframe:257 | 🔧 [P0修复] 字段重排完成: 原始22个 -> 最终22个字段
2025-09-16 14:31:21.713 | INFO     | src.modules.format_management.format_renderer:render_dataframe:273 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 22
2025-09-16 14:31:21.729 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 22
2025-09-16 14:31:21.744 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-09-16 14:31:21.744 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 22列
2025-09-16 14:31:21.760 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时15.6ms, 平均每行0.31ms
2025-09-16 14:31:21.760 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=15.6ms, 策略=small_dataset
2025-09-16 14:31:21.760 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-09-16 14:31:21.760 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第0行数据工号: 19990089
2025-09-16 14:31:21.760 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第1行数据工号: 20161565
2025-09-16 14:31:21.760 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第2行数据工号: 20191782
2025-09-16 14:31:21.760 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第3行数据工号: 20151515
2025-09-16 14:31:21.760 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第4行数据工号: 20181640
2025-09-16 14:31:21.760 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-09-16 14:31:21.760 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=2375.00
2025-09-16 14:31:21.760 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=1696.00
2025-09-16 14:31:21.760 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=1427.00
2025-09-16 14:31:21.760 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=2175.00
2025-09-16 14:31:21.760 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=2582.00
2025-09-16 14:31:21.760 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:488 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-09-16 14:31:21.760 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:447 | 表格数据已设置: 50 行, 22 列
2025-09-16 14:31:21.773 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_05_all_active
2025-09-16 14:31:21.782 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:474 | 🔧 [新架构] 为表格 salary_data_2025_05_all_active 重新加载 29 个字段映射
2025-09-16 14:31:21.783 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 50 行, 耗时: 90.6ms
2025-09-16 14:31:21.783 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 14:31:21.784 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 14:31:21.787 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1736 | 🔧 [P2-3修复] 列宽已恢复: salary_data_2025_05_all_active (22/22 列, 表头数量: 22)
2025-09-16 14:31:21.788 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 14:31:21.789 | INFO     | src.gui.prototype.prototype_main_window:set_data:1047 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=0，等待数据事件纠正
2025-09-16 14:31:21.789 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3984 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 25列
2025-09-16 14:31:21.790 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 25 个, 行号起始 1, 共 50 行
2025-09-16 14:31:21.798 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4000 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_05_all_active, 传递参数: 25个表头
2025-09-16 14:31:21.799 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4004 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 25列
2025-09-16 14:31:21.859 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:401 | 已清理所有缓存
2025-09-16 14:31:21.860 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:11427 | ✅ [新架构] 组件状态一致性验证通过
2025-09-16 14:31:21.862 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:11390 | 🆕 [新架构] 导航迁移完成
2025-09-16 14:31:21.863 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4077 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-09-16 14:31:21.863 | INFO     | src.services.table_data_service:load_table_data:437 | 📨[data_event] published(cache)
2025-09-16 14:31:21.864 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7939 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-09-16 14:31:21.867 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed:4939 | 🔧 [P1-强化] 延迟调整时发现列数不匹配: 期望22列, 实际25列
2025-09-16 14:31:21.867 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7393 | 方案A：安全设置列数: 22
2025-09-16 14:31:21.868 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed:4956 | 🔧 [P1-强化] 最终列数修复: 22列
2025-09-16 14:31:21.902 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_05_all_active
2025-09-16 14:31:21.951 | WARNING  | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2429 | 🔧 [方案A1] 发现重复表头: 列1(工号)，原始列在0
2025-09-16 14:31:21.951 | WARNING  | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2429 | 🔧 [方案A1] 发现重复表头: 列18(工号)，原始列在0
2025-09-16 14:31:21.951 | WARNING  | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2429 | 🔧 [方案A1] 发现重复表头: 列19(工号)，原始列在0
2025-09-16 14:31:21.951 | INFO     | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2440 | 🔧 [方案A1] 检测到3个重复表头，开始移除
2025-09-16 14:31:21.951 | INFO     | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2448 | 🔧 [方案A1] 已移除重复列: 列19(工号)
2025-09-16 14:31:21.951 | INFO     | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2448 | 🔧 [方案A1] 已移除重复列: 列18(工号)
2025-09-16 14:31:21.951 | INFO     | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2448 | 🔧 [方案A1] 已移除重复列: 列1(工号)
2025-09-16 14:31:21.967 | INFO     | src.gui.prototype.prototype_main_window:_post_data_header_cleanup:2464 | 🔧 [方案A1] 表格显示已刷新，列宽已调整
2025-09-16 14:31:22.858 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月', '工资表 > 2025年', '工资表', '工资表 > 2025年 > 05月 > 退休人员']
2025-09-16 14:31:22.873 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7713 | 导航变化: 工资表 > 2025年 > 05月 > 退休人员
2025-09-16 14:31:22.873 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10932 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_all_active -> None
2025-09-16 14:31:22.873 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10948 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-09-16 14:31:22.873 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8724 | 🔧 [表名生成] 命中最近导入映射: ('工资表', '2025', '05', '退休人员') -> salary_data_2025_05_pension
2025-09-16 14:31:22.873 | INFO     | src.gui.prototype.prototype_main_window:_process_navigation_change:7775 | 🔧 [P0-紧急修复] 导航变化时更新状态栏: 工资表 > 2025年 > 05月 > 退休人员
2025-09-16 14:31:22.873 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:384 | 已清理表 salary_data_2025_05_pension 的缓存
2025-09-16 14:31:22.873 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11620 | 已注册 4 个表格到表头管理器
2025-09-16 14:31:22.873 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1197 | 🔧 [P1-3] 开始智能表头重影检测，共 5 个表格
2025-09-16 14:31:22.873 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1270 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-09-16 14:31:22.873 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1271 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-09-16 14:31:22.873 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7934 | 🆕 使用新架构加载数据: salary_data_2025_05_pension（通过事件系统）
2025-09-16 14:31:22.889 | INFO     | src.services.table_data_service:load_table_data:377 | 加载表格数据: 页码: 1
2025-09-16 14:31:22.889 | INFO     | src.services.table_data_service:load_table_data:401 | [缓存命中] 使用缓存数据: 第1页
2025-09-16 14:31:22.889 | INFO     | src.services.table_data_service:load_table_data:412 | 📨[data_event] publish(cache) | table=salary_data_2025_05_pension | rows=13 | page=1 | size=50 | total=13 | request_id=SV-1758004282889-afeebd36-C
2025-09-16 14:31:22.889 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3874 | 📥[data_event] received | table=salary_data_2025_05_pension | request_id=SV-1758004282889-afeebd36-C
2025-09-16 14:31:22.889 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3906 | 数据内容: 13行 x 32列
2025-09-16 14:31:22.889 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3937 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_05_pension, 13行
2025-09-16 14:31:22.889 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3952 | 🔧 [P0-修复] 排序数据处理前: 32列 - ['人员代码', '姓名', '部门名称', '人员类别代码', '基本退休费']...
2025-09-16 14:31:22.889 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:22.889 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:22.889 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔧 [P0-修复] 字段映射后: 32列
2025-09-16 14:31:22.889 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:7617 | 🔧 [P0-修复] 系统字段过滤: 隐藏 5 个字段 - ['年份', '月份', '序号', 'data_source', 'import_time']
2025-09-16 14:31:22.889 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3956 | 🔧 [P0-修复] 系统字段过滤后: 27列
2025-09-16 14:31:22.889 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:31:22.905 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:31:22.905 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:31:22.905 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7670 | 表 salary_data_2025_05_pension 没有用户偏好设置，显示所有可见字段
2025-09-16 14:31:22.905 | INFO     | src.gui.prototype.prototype_main_window:set_data:922 | 通过公共接口设置新的表格数据(DataFrame): 13 行
2025-09-16 14:31:22.905 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:22.905 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:22.920 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 13行，表名: salary_data_2025_05_pension
2025-09-16 14:31:22.920 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7393 | 方案A：安全设置列数: 0
2025-09-16 14:31:22.920 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2822 | 🔧 [P1优化] 表切换操作: salary_data_2025_05_all_active -> salary_data_2025_05_pension
2025-09-16 14:31:22.920 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19709165, 薪资=N/A
2025-09-16 14:31:22.920 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=19981259, 薪资=N/A
2025-09-16 14:31:22.937 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5528 | 最大可见行数已更新: 50 -> 13
2025-09-16 14:31:22.937 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5581 | 根据数据量(13)自动调整最大可见行数为: 13
2025-09-16 14:31:22.937 | INFO     | src.modules.format_management.format_renderer:render_dataframe:127 | 🎯 [格式渲染] 隐藏字段配置: ['created_at', 'updated_at', 'id', 'sequence_number', 'row_number']
2025-09-16 14:31:22.937 | INFO     | src.modules.format_management.format_renderer:render_dataframe:135 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-09-16 14:31:22.937 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-09-16 14:31:22.937 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-09-16 14:31:22.952 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 备注 -> 英文名: 备注 -> 类型: string
2025-09-16 14:31:22.952 | INFO     | src.modules.format_management.field_registry:get_display_fields:1582 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-09-16 14:31:22.952 | INFO     | src.modules.format_management.format_renderer:render_dataframe:186 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-09-16 14:31:22.952 | INFO     | src.modules.format_management.format_renderer:render_dataframe:244 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 12/25 个字段
2025-09-16 14:31:22.952 | INFO     | src.modules.format_management.format_renderer:render_dataframe:252 | 🔧 [P0-关键修复] 保留未配置的业务字段: 15个
2025-09-16 14:31:22.952 | INFO     | src.modules.format_management.format_renderer:render_dataframe:257 | 🔧 [P0修复] 字段重排完成: 原始27个 -> 最终27个字段
2025-09-16 14:31:22.952 | INFO     | src.modules.format_management.format_renderer:render_dataframe:273 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 13, 列数: 27
2025-09-16 14:31:22.952 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 13, 列数: 27
2025-09-16 14:31:22.969 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 13行数据
2025-09-16 14:31:22.969 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 13行 x 27列
2025-09-16 14:31:22.969 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时0.0ms, 平均每行0.00ms
2025-09-16 14:31:22.969 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=13, 渲染时间=0.0ms, 策略=small_dataset
2025-09-16 14:31:22.969 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-09-16 14:31:22.969 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第0行数据工号: 19709165
2025-09-16 14:31:22.969 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第1行数据工号: 19981259
2025-09-16 14:31:22.969 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第2行数据工号: 19721294
2025-09-16 14:31:22.969 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第3行数据工号: 19841258
2025-09-16 14:31:22.969 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第4行数据工号: 19499098
2025-09-16 14:31:22.969 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 13
2025-09-16 14:31:22.969 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[0]: 工号=19709165, 薪资=N/A
2025-09-16 14:31:22.969 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[1]: 工号=19981259, 薪资=N/A
2025-09-16 14:31:22.983 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[2]: 工号=19721294, 薪资=N/A
2025-09-16 14:31:22.983 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[3]: 工号=19841258, 薪资=N/A
2025-09-16 14:31:22.983 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[4]: 工号=19499098, 薪资=N/A
2025-09-16 14:31:22.983 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:488 | 可见行数据顺序: ['19709165', '19981259', '19721294', '19841258', '19499098']
2025-09-16 14:31:22.983 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:447 | 表格数据已设置: 13 行, 27 列
2025-09-16 14:31:22.985 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_05_pension
2025-09-16 14:31:22.994 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:474 | 🔧 [新架构] 为表格 salary_data_2025_05_pension 重新加载 29 个字段映射
2025-09-16 14:31:22.995 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 13 行, 耗时: 64.7ms
2025-09-16 14:31:22.996 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 14:31:22.996 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 14:31:22.997 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1708 | 🆕 [列宽保存] 未找到表格 salary_data_2025_05_pension 的列宽配置
2025-09-16 14:31:22.998 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 14:31:22.998 | INFO     | src.gui.prototype.prototype_main_window:set_data:1047 | 📊[pagination-write] 跳过写入当前页数据量(total=13)，保持 total=0，等待数据事件纠正
2025-09-16 14:31:22.999 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3984 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 13行, 27列
2025-09-16 14:31:22.999 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 27 个, 行号起始 1, 共 13 行
2025-09-16 14:31:23.000 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4000 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_05_pension, 传递参数: 27个表头
2025-09-16 14:31:23.001 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4004 | 🔧 [统一数据设置] 数据已成功设置到UI: 13行, 27列
2025-09-16 14:31:23.061 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:401 | 已清理所有缓存
2025-09-16 14:31:23.062 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:11427 | ✅ [新架构] 组件状态一致性验证通过
2025-09-16 14:31:23.064 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:11390 | 🆕 [新架构] 导航迁移完成
2025-09-16 14:31:23.064 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4077 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-09-16 14:31:23.065 | INFO     | src.services.table_data_service:load_table_data:437 | 📨[data_event] published(cache)
2025-09-16 14:31:23.065 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7939 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-09-16 14:31:23.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_05_pension
2025-09-16 14:31:25.290 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月', '工资表 > 2025年', '工资表', '工资表 > 2025年 > 05月 > 退休人员']
2025-09-16 14:31:25.290 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7713 | 导航变化: 工资表 > 2025年 > 05月 > 离休人员
2025-09-16 14:31:25.290 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10932 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_pension -> None
2025-09-16 14:31:25.290 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10948 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-09-16 14:31:25.290 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8724 | 🔧 [表名生成] 命中最近导入映射: ('工资表', '2025', '05', '离休人员') -> salary_data_2025_05_retired
2025-09-16 14:31:25.290 | INFO     | src.gui.prototype.prototype_main_window:_process_navigation_change:7775 | 🔧 [P0-紧急修复] 导航变化时更新状态栏: 工资表 > 2025年 > 05月 > 离休人员
2025-09-16 14:31:25.290 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:384 | 已清理表 salary_data_2025_05_retired 的缓存
2025-09-16 14:31:25.306 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11620 | 已注册 4 个表格到表头管理器
2025-09-16 14:31:25.306 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7934 | 🆕 使用新架构加载数据: salary_data_2025_05_retired（通过事件系统）
2025-09-16 14:31:25.306 | INFO     | src.services.table_data_service:load_table_data:377 | 加载表格数据: 页码: 1
2025-09-16 14:31:25.306 | INFO     | src.services.table_data_service:load_table_data:401 | [缓存命中] 使用缓存数据: 第1页
2025-09-16 14:31:25.306 | INFO     | src.services.table_data_service:load_table_data:412 | 📨[data_event] publish(cache) | table=salary_data_2025_05_retired | rows=2 | page=1 | size=50 | total=2 | request_id=SV-1758004285306-bd77de65-C
2025-09-16 14:31:25.306 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3874 | 📥[data_event] received | table=salary_data_2025_05_retired | request_id=SV-1758004285306-bd77de65-C
2025-09-16 14:31:25.306 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3906 | 数据内容: 2行 x 21列
2025-09-16 14:31:25.306 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3937 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_05_retired, 2行
2025-09-16 14:31:25.306 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3952 | 🔧 [P0-修复] 排序数据处理前: 21列 - ['人员代码', '姓名', '部门名称', '增发一次\n性生活补贴', '护理费']...
2025-09-16 14:31:25.306 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:25.306 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:25.306 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔧 [P0-修复] 字段映射后: 21列
2025-09-16 14:31:25.306 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:7617 | 🔧 [P0-修复] 系统字段过滤: 隐藏 5 个字段 - ['年份', '月份', '序号', 'data_source', 'import_time']
2025-09-16 14:31:25.306 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3956 | 🔧 [P0-修复] 系统字段过滤后: 16列
2025-09-16 14:31:25.306 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 14:31:25.322 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 14:31:25.322 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 14:31:25.322 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7670 | 表 salary_data_2025_05_retired 没有用户偏好设置，显示所有可见字段
2025-09-16 14:31:25.322 | INFO     | src.gui.prototype.prototype_main_window:set_data:922 | 通过公共接口设置新的表格数据(DataFrame): 2 行
2025-09-16 14:31:25.322 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state\data\field_mappings.json
2025-09-16 14:31:25.322 | INFO     | src.modules.format_management.field_registry:load_mappings:633 | 🏷️ [字段注册] 字段映射加载成功
2025-09-16 14:31:25.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 2行，表名: salary_data_2025_05_retired
2025-09-16 14:31:25.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2822 | 🔧 [P1优化] 表切换操作: salary_data_2025_05_pension -> salary_data_2025_05_retired
2025-09-16 14:31:25.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19289006, 薪资=N/A
2025-09-16 14:31:25.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2847 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=19339009, 薪资=N/A
2025-09-16 14:31:25.353 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5528 | 最大可见行数已更新: 13 -> 10
2025-09-16 14:31:25.353 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5581 | 根据数据量(2)自动调整最大可见行数为: 10
2025-09-16 14:31:25.353 | INFO     | src.modules.format_management.format_renderer:render_dataframe:127 | 🎯 [格式渲染] 隐藏字段配置: ['created_at', 'updated_at', 'id', 'sequence_number', 'row_number']
2025-09-16 14:31:25.353 | INFO     | src.modules.format_management.format_renderer:render_dataframe:135 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-09-16 14:31:25.353 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-09-16 14:31:25.353 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-09-16 14:31:25.353 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | [关键修复] 字段: 备注 -> 英文名: 备注 -> 类型: string
2025-09-16 14:31:25.353 | INFO     | src.modules.format_management.field_registry:get_display_fields:1582 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-09-16 14:31:25.353 | INFO     | src.modules.format_management.format_renderer:render_dataframe:186 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-09-16 14:31:25.369 | INFO     | src.modules.format_management.format_renderer:render_dataframe:244 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 6/25 个字段
2025-09-16 14:31:25.369 | INFO     | src.modules.format_management.format_renderer:render_dataframe:252 | 🔧 [P0-关键修复] 保留未配置的业务字段: 10个
2025-09-16 14:31:25.369 | INFO     | src.modules.format_management.format_renderer:render_dataframe:257 | 🔧 [P0修复] 字段重排完成: 原始16个 -> 最终16个字段
2025-09-16 14:31:25.369 | INFO     | src.modules.format_management.format_renderer:render_dataframe:273 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 2, 列数: 16
2025-09-16 14:31:25.369 | INFO     | src.modules.format_management.unified_format_manager:format_data:371 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 2, 列数: 16
2025-09-16 14:31:25.369 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2983 | [列对齐] 已对齐记录与表头: rows=2->2, cols=16
2025-09-16 14:31:25.369 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7393 | 方案A：安全设置列数: 16
2025-09-16 14:31:25.369 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 2行数据
2025-09-16 14:31:25.369 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 2行 x 16列
2025-09-16 14:31:25.369 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时0.0ms, 平均每行0.00ms
2025-09-16 14:31:25.369 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=2, 渲染时间=0.0ms, 策略=small_dataset
2025-09-16 14:31:25.369 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-09-16 14:31:25.369 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第0行数据工号: 19289006
2025-09-16 14:31:25.384 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:438 | 第1行数据工号: 19339009
2025-09-16 14:31:25.384 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 2
2025-09-16 14:31:25.384 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[0]: 工号=19289006, 薪资=N/A
2025-09-16 14:31:25.384 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:487 | 🚨 [UI数据修复] row_data[1]: 工号=19339009, 薪资=N/A
2025-09-16 14:31:25.384 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:488 | 可见行数据顺序: ['19289006', '19339009']
2025-09-16 14:31:25.384 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:447 | 表格数据已设置: 2 行, 16 列
2025-09-16 14:31:25.395 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_05_retired
2025-09-16 14:31:25.397 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:474 | 🔧 [新架构] 为表格 salary_data_2025_05_retired 重新加载 29 个字段映射
2025-09-16 14:31:25.397 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 2 行, 耗时: 54.8ms
2025-09-16 14:31:25.398 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 14:31:25.398 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 14:31:25.399 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1708 | 🆕 [列宽保存] 未找到表格 salary_data_2025_05_retired 的列宽配置
2025-09-16 14:31:25.400 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 14:31:25.400 | INFO     | src.gui.prototype.prototype_main_window:set_data:1047 | 📊[pagination-write] 跳过写入当前页数据量(total=2)，保持 total=0，等待数据事件纠正
2025-09-16 14:31:25.401 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3984 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 2行, 16列
2025-09-16 14:31:25.408 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 16 个, 行号起始 1, 共 2 行
2025-09-16 14:31:25.409 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4000 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_05_retired, 传递参数: 16个表头
2025-09-16 14:31:25.410 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4004 | 🔧 [统一数据设置] 数据已成功设置到UI: 2行, 16列
2025-09-16 14:31:25.468 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:401 | 已清理所有缓存
2025-09-16 14:31:25.468 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:11427 | ✅ [新架构] 组件状态一致性验证通过
2025-09-16 14:31:25.471 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:11390 | 🆕 [新架构] 导航迁移完成
2025-09-16 14:31:25.471 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:4077 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-09-16 14:31:25.472 | INFO     | src.services.table_data_service:load_table_data:437 | 📨[data_event] published(cache)
2025-09-16 14:31:25.472 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7939 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-09-16 14:31:25.502 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_05_retired
2025-09-16 14:31:32.656 | INFO     | __main__:main:519 | 应用程序正常退出
2025-09-16 14:31:32.688 | INFO     | src.gui.table_header_manager:cleanup_callback:162 | 🔧 [P1-1] 表格 table_0_2966717647904 已自动清理（弱引用回调）
