{"name": "异动表_自动保存模板", "description": "从C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls自动生成", "table_type": "异动表", "mapping_config": {"序号": {"target_field": "工号", "display_name": "序号", "field_type": "employee_id_string", "data_type": "string", "is_required": false}, "工号": {"target_field": "姓名", "display_name": "工号", "field_type": "name_string", "data_type": "string", "is_required": false}, "姓名": {"target_field": "organization", "display_name": "姓名", "field_type": "text_string", "data_type": "string", "is_required": false}, "部门名称": {"target_field": "position", "display_name": "部门名称", "field_type": "salary_float", "data_type": "string", "is_required": false}, "人员类别": {"target_field": "money", "display_name": "人员类别", "field_type": "salary_float", "data_type": "string", "is_required": false}, "人员类别代码": {"target_field": "money", "display_name": "人员类别代码", "field_type": "salary_float", "data_type": "string", "is_required": false}, "2025年岗位工资": {"target_field": "money", "display_name": "2025年岗位工资", "field_type": "salary_float", "data_type": "string", "is_required": false}, "2025年校龄工资": {"target_field": "money", "display_name": "2025年校龄工资", "field_type": "salary_float", "data_type": "string", "is_required": false}, "津贴": {"target_field": "money", "display_name": "津贴", "field_type": "salary_float", "data_type": "string", "is_required": false}, "结余津贴": {"target_field": "money", "display_name": "结余津贴", "field_type": "salary_float", "data_type": "string", "is_required": false}, "2025年基础性绩效": {"target_field": "money", "display_name": "2025年基础性绩效", "field_type": "salary_float", "data_type": "float", "is_required": false}, "卫生费": {"target_field": "money", "display_name": "卫生费", "field_type": "salary_float", "data_type": "float", "is_required": false}, "2025年生活补贴": {"target_field": "money", "display_name": "2025年生活补贴", "field_type": "salary_float", "data_type": "string", "is_required": false}, "车补": {"target_field": "序号", "display_name": "车补", "field_type": "employee_id_string", "data_type": "string", "is_required": false}, "2025年奖励性绩效预发": {"target_field": "人员类别", "display_name": "2025年奖励性绩效预发", "field_type": "text_string", "data_type": "float", "is_required": false}, "补发": {"target_field": "人员类别代码", "display_name": "补发", "field_type": "text_string", "data_type": "float", "is_required": false}, "借支": {"target_field": "卫生费", "display_name": "借支", "field_type": "text_string", "data_type": "float", "is_required": false}, "应发工资": {"target_field": "车补", "display_name": "应发工资", "field_type": "text_string", "data_type": "string", "is_required": false}, "2025公积金": {"target_field": "补发", "display_name": "2025公积金", "field_type": "salary_float", "data_type": "float", "is_required": false}, "保险扣款": {"target_field": "借支", "display_name": "保险扣款", "field_type": "salary_float", "data_type": "float", "is_required": false}, "代扣代存养老保险": {"target_field": "2025公积金", "display_name": "代扣代存养老保险", "field_type": "salary_float", "data_type": "float", "is_required": false}}, "field_count": 21, "auto_generated": true, "id": "template_20250916_143054", "created_at": "2025-09-16T14:30:54.720681", "last_modified": "2025-09-16T14:30:54.720681", "file_path": "config/templates\\异动表_自动保存模板_20250916_143054.json", "template_format_version": "2.0"}