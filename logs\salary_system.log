2025-09-16 13:01:16.179 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-09-16 13:01:16.179 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-09-16 13:01:16.179 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-09-16 13:01:16.181 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-09-16 13:01:16.181 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-09-16 13:01:16.181 | INFO     | src:<module>:20 | 月度工资异动处理系统 v2.0.0-refactored 初始化完成
2025-09-16 13:01:20.303 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-09-16 13:01:20.306 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-09-16 13:01:20.307 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-09-16 13:01:20.308 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-09-16 13:01:20.310 | INFO     | __main__:setup_app_logging:423 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-09-16 13:01:20.311 | INFO     | __main__:main:487 | 初始化核心管理器...
2025-09-16 13:01:20.312 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-16 13:01:20.313 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 13:01:20.325 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 13:01:20.325 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 13:01:20.331 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-09-16 13:01:20.383 | INFO     | src.modules.data_storage.database_manager:_initialize_database:166 | 数据库初始化完成
2025-09-16 13:01:20.384 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-09-16 13:01:20.384 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-16 13:01:20.396 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:117 | 动态表管理器初始化完成
2025-09-16 13:01:20.396 | INFO     | __main__:main:492 | 核心管理器初始化完成。
2025-09-16 13:01:20.396 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-09-16 13:01:20.396 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-09-16 13:01:20.401 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-09-16 13:01:20.410 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-09-16 13:01:20.410 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-09-16 13:01:20.410 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-09-16 13:01:20.410 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-09-16 13:01:20.410 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:12209 | 🔧 [P2-3] 错误恢复策略注册完成
2025-09-16 13:01:20.415 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-09-16 13:01:20.415 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:12064 | 🔧 [P2-3] 错误处理机制设置完成
2025-09-16 13:01:20.415 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:12102 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-09-16 13:01:20.615 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-09-16 13:01:20.618 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-09-16 13:01:20.619 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-09-16 13:01:20.625 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 13:01:20.631 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 13:01:20.633 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 13:01:20.633 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 13:01:20.641 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 13:01:20.646 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 13:01:20.646 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 13:01:20.650 | INFO     | src.modules.system_config.config_manager:_create_backup:510 | 配置文件备份已创建: C:\test\salary_changes\salary_changes\config_backups\config_20250916_130120.json
2025-09-16 13:01:20.651 | INFO     | src.modules.system_config.config_manager:save_config:413 | 正在保存配置文件: C:\test\salary_changes\salary_changes\config.json
2025-09-16 13:01:20.690 | INFO     | src.modules.system_config.config_manager:save_config:418 | 配置文件保存成功
2025-09-16 13:01:20.690 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-09-16 13:01:20.694 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-16 13:01:20.694 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-16 13:01:20.697 | INFO     | src.core.field_mapping_manager:_load_config:93 | 🔧 [P3优化] 创建默认字段映射配置
2025-09-16 13:01:20.697 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-09-16 13:01:20.697 | INFO     | src.core.unified_mapping_service:__init__:52 | UnifiedMappingService 初始化完成
2025-09-16 13:01:20.697 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-09-16 13:01:20.697 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-09-16 13:01:20.697 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-09-16 13:01:20.697 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-16 13:01:20.697 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-09-16 13:01:20.697 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 78.6ms
2025-09-16 13:01:20.760 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-09-16 13:01:20.763 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-09-16 13:01:20.763 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-09-16 13:01:20.766 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-09-16 13:01:20.766 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-09-16 13:01:20.768 | INFO     | src.gui.prototype.prototype_main_window:__init__:3702 | 🚀 性能管理器已集成
2025-09-16 13:01:20.768 | INFO     | src.gui.prototype.prototype_main_window:__init__:3704 | ✅ 新架构集成成功！
2025-09-16 13:01:20.768 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3817 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-09-16 13:01:20.768 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3782 | ✅ 新架构事件监听器设置完成
2025-09-16 13:01:20.768 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-09-16 13:01:20.773 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-09-16 13:01:20.773 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-09-16 13:01:21.046 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2778 | 菜单栏创建完成
2025-09-16 13:01:21.048 | INFO     | src.gui.prototype.prototype_main_window:__init__:2753 | 菜单栏管理器初始化完成
2025-09-16 13:01:21.052 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-09-16 13:01:21.052 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5510 | 管理器设置完成，包含增强版表头管理器
2025-09-16 13:01:21.053 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5515 | 🔧 开始应用窗口级Material Design样式...
2025-09-16 13:01:21.054 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-09-16 13:01:21.058 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-09-16 13:01:21.059 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5522 | ✅ 窗口级样式应用成功
2025-09-16 13:01:21.065 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5563 | ✅ 响应式样式监听设置完成
2025-09-16 13:01:21.070 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 13:01:21.076 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 13:01:21.081 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 13:01:21.083 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-09-16 13:01:21.087 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-09-16 13:01:21.097 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:2357 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-09-16 13:01:21.104 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:2069 | 🏠 使用示例数据结构加载导航（首次启动或暂无数据时显示）
2025-09-16 13:01:21.110 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:2441 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-09-16 13:01:21.111 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:893 | 导航面板已重构：移除功能性导航，专注数据导航
2025-09-16 13:01:21.112 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:927 | 恢复导航状态: 0个展开项
2025-09-16 13:01:21.113 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月', '工资表 > 2025年 > 05月 > 全部在职人员']
2025-09-16 13:01:21.135 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:646 | 增强导航面板初始化完成
2025-09-16 13:01:21.136 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-09-16 13:01:21.151 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-09-16 13:01:21.155 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:2357 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-09-16 13:01:21.159 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:2441 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-09-16 13:01:21.166 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:893 | 导航面板已重构：移除功能性导航，专注数据导航
2025-09-16 13:01:21.177 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:646 | 增强导航面板初始化完成
2025-09-16 13:01:21.182 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1399 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-09-16 13:01:21.183 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1408 | 🔧 [P2修复] 未找到最新工资数据路径（可能是首次启动）
2025-09-16 13:01:21.488 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-09-16 13:01:21.490 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2126 | 🚨 [架构修复] 启用模型数据同步机制
2025-09-16 13:01:21.495 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1350 | 快捷键注册完成: 18/18 个
2025-09-16 13:01:21.495 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1793 | 拖拽排序管理器初始化完成
2025-09-16 13:01:21.523 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-09-16 13:01:21.525 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-09-16 13:01:21.528 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-09-16 13:01:21.530 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2179 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-09-16 13:01:21.532 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-09-16 13:01:21.533 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-09-16 13:01:21.534 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-16 13:01:21.534 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2231 | 🎯 [统一格式管理] 使用统一格式管理器
2025-09-16 13:01:21.548 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:370 | 🔧 [新架构] 成功加载 0 个字段映射
2025-09-16 13:01:21.568 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-09-16 13:01:21.569 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2278 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-09-16 13:01:21.574 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1537 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-09-16 13:01:21.574 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1538 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-09-16 13:01:21.577 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1539 | 🔧 [列宽保存修复] 配置文件存在: False
2025-09-16 13:01:21.583 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1540 | 🔧 [列宽保存修复] 父目录存在: True
2025-09-16 13:01:21.583 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1541 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-09-16 13:01:21.584 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2285 | 列宽管理器初始化完成
2025-09-16 13:01:21.585 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2412 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-09-16 13:01:21.588 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2299 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-09-16 13:01:21.588 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2358 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-09-16 13:01:21.589 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-09-16 13:01:21.590 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4653 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-09-16 13:01:21.618 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-09-16 13:01:21.622 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7393 | 方案A：安全设置列数: 0
2025-09-16 13:01:21.623 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2822 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-09-16 13:01:21.625 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5557 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-09-16 13:01:21.642 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-09-16 13:01:21.643 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2874 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-09-16 13:01:21.644 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2959 | 表格格式化完成: default_table, 类型: active_employees
2025-09-16 13:01:21.667 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-09-16 13:01:21.670 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-09-16 13:01:21.681 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-09-16 13:01:21.685 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-09-16 13:01:21.702 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:447 | 表格数据已设置: 0 行, 22 列
2025-09-16 13:01:21.734 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-09-16 13:01:21.739 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 0 行, 耗时: 125.2ms
2025-09-16 13:01:21.740 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 13:01:21.742 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 13:01:21.744 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1694 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-09-16 13:01:21.750 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 13:01:21.759 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2383 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-09-16 13:01:21.776 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-09-16 13:01:21.788 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-09-16 13:01:21.789 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-09-16 13:01:21.851 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:595 | 控制面板按钮信号连接完成
2025-09-16 13:01:21.909 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5472 | 快捷键设置完成
2025-09-16 13:01:21.910 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5429 | 主窗口UI设置完成。
2025-09-16 13:01:21.914 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5666 | 🔧 [全局排序] 全局排序开关连接成功
2025-09-16 13:01:21.915 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5698 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-09-16 13:01:21.916 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5722 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-09-16 13:01:21.917 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5725 | 信号连接设置完成
2025-09-16 13:01:21.918 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-16 13:01:21.926 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-16 13:01:21.927 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-16 13:01:21.928 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:7137 | 🔧 [P1-2修复] 发现 20 个表的配置
2025-09-16 13:01:21.956 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:7147 | ✅ [P1-2修复] 已加载字段映射信息，共20个表的映射
2025-09-16 13:01:21.968 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2358 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-09-16 13:01:21.970 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-09-16 13:01:21.971 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4653 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-09-16 13:01:21.972 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5557 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-09-16 13:01:21.973 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2959 | 表格格式化完成: default_table, 类型: active_employees
2025-09-16 13:01:21.973 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7393 | 方案A：安全设置列数: 22
2025-09-16 13:01:21.974 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-09-16 13:01:21.975 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-09-16 13:01:21.976 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-09-16 13:01:21.976 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-09-16 13:01:21.978 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 0 行, 耗时: 5.8ms
2025-09-16 13:01:21.978 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 13:01:21.987 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 13:01:21.988 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1694 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-09-16 13:01:21.989 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 13:01:21.989 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2383 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-09-16 13:01:21.990 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-09-16 13:01:21.991 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8941 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-09-16 13:01:21.992 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2358 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-09-16 13:01:21.994 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2720 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-09-16 13:01:22.001 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4653 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-09-16 13:01:22.007 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5557 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-09-16 13:01:22.008 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2959 | 表格格式化完成: default_table, 类型: active_employees
2025-09-16 13:01:22.009 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-09-16 13:01:22.009 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-09-16 13:01:22.010 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-09-16 13:01:22.010 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:470 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-09-16 13:01:22.011 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3143 | 表格数据设置完成: 0 行, 耗时: 4.9ms
2025-09-16 13:01:22.013 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8331 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-16 13:01:22.014 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8344 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-16 13:01:22.021 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1694 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-09-16 13:01:22.022 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3222 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-16 13:01:22.023 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2383 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-09-16 13:01:22.034 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8959 | 已显示标准空表格，表头数量: 22
2025-09-16 13:01:22.038 | INFO     | src.gui.prototype.prototype_main_window:__init__:3756 | 原型主窗口初始化完成
2025-09-16 13:01:22.050 | INFO     | src.gui.style_manager:update_responsive_style:227 | 响应式样式更新: medium -> small
2025-09-16 13:01:22.051 | INFO     | src.gui.style_manager:update_responsive_style:238 | 响应式样式更新成功: small
2025-09-16 13:01:22.052 | INFO     | src.gui.style_manager:update_responsive_style:227 | 响应式样式更新: small -> medium
2025-09-16 13:01:22.053 | INFO     | src.gui.style_manager:update_responsive_style:238 | 响应式样式更新成功: medium
2025-09-16 13:01:22.124 | INFO     | __main__:main:514 | 应用程序启动成功
2025-09-16 13:01:22.131 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-09-16 13:01:22.132 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1708 | 执行延迟的自动选择最新数据...
2025-09-16 13:01:22.133 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1731 | 延迟自动选择最新数据失败，可能没有可用数据
2025-09-16 13:01:22.134 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-09-16 13:01:22.135 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8366 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-09-16 13:01:22.342 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-09-16 13:01:22.343 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2131 | MainWorkspaceArea 响应式适配: sm
2025-09-16 13:01:22.850 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9843 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-09-16 13:01:22.850 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9753 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-09-16 13:01:22.854 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9767 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-09-16 13:01:22.855 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:10301 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-09-16 13:01:22.872 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9773 | 🔧 [P0-1] 智能显示亮度修复完成
2025-09-16 13:03:31.757 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:655 | 数据导入功能被触发，发出 import_requested 信号。
2025-09-16 13:03:31.757 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5958 | 首次创建UnifiedDataImportWindow
2025-09-16 13:03:31 - src.gui.unified_data_import_window - INFO - 初始化统一数据导入窗口
2025-09-16 13:03:31 - src.gui.unified_data_import_window - INFO - 核心服务已通过依赖注入成功加载
2025-09-16 13:03:31.764 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-09-16 13:03:31.764 | INFO     | src.modules.data_import.change_data_config_manager:__init__:55 | 异动表配置管理器重构版初始化完成，使用统一配置系统
2025-09-16 13:03:31.769 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:96 | 多Sheet导入器初始化完成
2025-09-16 13:03:31.769 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-09-16 13:03:31.775 | INFO     | src.modules.data_import.change_data_config_manager:__init__:55 | 异动表配置管理器重构版初始化完成，使用统一配置系统
2025-09-16 13:03:31.783 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:96 | 多Sheet导入器初始化完成
2025-09-16 13:03:31 - src.modules.config.advanced_config_manager - INFO - 🚀 [P2-1修复] 配置文件监控已启动
2025-09-16 13:03:31 - src.modules.config.advanced_config_manager - INFO - 🚀 [P2-1修复] 高级配置管理器初始化完成
2025-09-16 13:03:31 - src.gui.core.smart_mapping_engine - INFO - 智能映射引擎初始化完成
2025-09-16 13:03:31 - src.gui.core.template_manager - INFO - 模板管理器初始化完成: 内置模板 3 个, 用户模板 0 个
2025-09-16 13:03:31 - src.gui.core.validation_engine - INFO - 验证引擎初始化完成
2025-09-16 13:03:31.895 | INFO     | src.modules.data_import.sheet_config_manager:__init__:128 | Sheet配置管理器初始化完成，配置目录: state\sheet_configs
2025-09-16 13:03:31 - src.gui.unified_data_import_window - INFO - 统一导入管理器初始化完成（包含第二阶段功能）
2025-09-16 13:03:31 - src.gui.unified_data_import_window - INFO - 核心服务与业务组件初始化完成
2025-09-16 13:03:31 - src.gui.unified_data_import_window - INFO - 🔧 未找到已保存的高级配置，使用默认设置
2025-09-16 13:03:31 - src.gui.unified_data_import_window - INFO - 工作线程初始化完成
2025-09-16 13:03:31 - src.gui.unified_data_import_window - INFO - 用户选择表类型: 工资表
2025-09-16 13:03:31 - src.gui.unified_data_import_window - INFO - 开始创建EnhancedSheetManagementWidget...
2025-09-16 13:03:31.910 | INFO     | src.modules.data_import.sheet_config_manager:__init__:128 | Sheet配置管理器初始化完成，配置目录: state\sheet_configs
2025-09-16 13:03:31 - src.gui.unified_data_import_window - INFO - ✅ EnhancedSheetManagementWidget创建成功: <class 'src.gui.unified_data_import_window.EnhancedSheetManagementWidget'>
2025-09-16 13:03:31 - src.modules.config.advanced_config_manager - INFO - 🚀 [P2-1修复] 配置文件监控已启动
2025-09-16 13:03:31 - src.modules.config.advanced_config_manager - INFO - 🚀 [P2-1修复] 高级配置管理器初始化完成
2025-09-16 13:03:31 - src.gui.core.smart_mapping_engine - INFO - 智能映射引擎初始化完成
2025-09-16 13:03:31 - src.gui.core.template_manager - INFO - 模板管理器初始化完成: 内置模板 3 个, 用户模板 0 个
2025-09-16 13:03:31 - src.gui.core.validation_engine - INFO - 验证引擎初始化完成
2025-09-16 13:03:31 - src.gui.performance_optimizer - INFO - 性能优化器初始化完成
2025-09-16 13:03:31.950 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-09-16 13:03:31 - src.gui.unified_data_import_window - INFO - 🔧 [P3紧急修复] 开始初始化ConfigSyncManager...
2025-09-16 13:03:31.954 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-09-16 13:03:31.954 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-09-16 13:03:31.954 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-09-16 13:03:31.956 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-16 13:03:31.964 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-09-16 13:03:31.964 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-09-16 13:03:31.967 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-09-16 13:03:31.969 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-16 13:03:31.969 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-09-16 13:03:31.969 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 14.6ms
2025-09-16 13:03:31 - src.gui.unified_data_import_window - INFO - 💾 [P1-1修复] ✅ 架构工厂初始化成功
2025-09-16 13:03:31 - src.gui.unified_data_import_window - INFO - 💾 [P1-1修复] ✅ 通过架构工厂获取ConfigSyncManager成功
2025-09-16 13:03:32.064 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-09-16 13:03:32.064 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:557 | 注册规则类型: number
2025-09-16 13:03:32.068 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:557 | 注册规则类型: string
2025-09-16 13:03:32.068 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:557 | 注册规则类型: date
2025-09-16 13:03:32.068 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:557 | 注册规则类型: code
2025-09-16 13:03:32.068 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:557 | 注册规则类型: custom
2025-09-16 13:03:32.068 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:568 | 🔧 [方案一实施] 注册内置字段类型: salary_float - 工资金额
2025-09-16 13:03:32.068 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:568 | 🔧 [方案一实施] 注册内置字段类型: employee_id_string - 工号
2025-09-16 13:03:32.068 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:568 | 🔧 [方案一实施] 注册内置字段类型: name_string - 姓名
2025-09-16 13:03:32.075 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:568 | 🔧 [方案一实施] 注册内置字段类型: date_string - 日期
2025-09-16 13:03:32.075 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:568 | 🔧 [方案一实施] 注册内置字段类型: id_number_string - 身份证号
2025-09-16 13:03:32.076 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:568 | 🔧 [方案一实施] 注册内置字段类型: code_string - 代码
2025-09-16 13:03:32.076 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:568 | 🔧 [方案一实施] 注册内置字段类型: float - 浮点数
2025-09-16 13:03:32.076 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:568 | 🔧 [方案一实施] 注册内置字段类型: integer - 整数
2025-09-16 13:03:32.076 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:568 | 🔧 [方案一实施] 注册内置字段类型: text_string - 文本字符串
2025-09-16 13:03:32.076 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:568 | 🔧 [方案一实施] 注册内置字段类型: personnel_category_code - 人员类别代码
2025-09-16 13:03:32.080 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-09-16 13:03:32.090 | INFO     | src.modules.data_import.formatting_engine:reload_custom_field_types:937 | 🔧 [方案一实施] 重新加载了 0 个自定义字段类型
2025-09-16 13:03:32 - src.gui.widgets.field_type_config_widget - INFO - 已加载 10 个内置类型和 0 个自定义类型
2025-09-16 13:03:32 - src.gui.widgets.field_type_config_widget - INFO - 字段类型配置组件初始化完成
2025-09-16 13:03:32 - src.gui.unified_data_import_window - INFO - 响应式分割器设置完成: 左侧=308px, 右侧=1092px
2025-09-16 13:03:32 - src.gui.unified_data_import_window - INFO - UI界面创建完成
2025-09-16 13:03:32 - src.gui.unified_data_import_window - INFO - 信号连接完成
2025-09-16 13:03:32 - ConfigMonitor - INFO - 🔔 [配置监控] 开始监控配置文件: state\data\field_mappings.json
2025-09-16 13:03:32 - src.gui.unified_data_import_window - INFO - [S3-PROBE] ConfigMonitor callbacks=1
2025-09-16 13:03:32 - src.gui.unified_data_import_window - INFO - 🔔 [快速修复] 配置监控初始化完成
2025-09-16 13:03:32 - src.gui.unified_data_import_window - INFO - 统一数据导入窗口初始化完成
2025-09-16 13:03:32.168 | INFO     | src.gui.prototype.prototype_main_window:_get_suggested_target_path:8764 | 检测到当前在工资表TAB，生成工资表默认路径
2025-09-16 13:03:32.169 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5969 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 09月 > 全部在职人员。打开导入对话框。
2025-09-16 13:03:32 - src.gui.unified_data_import_window - INFO - 响应式分割器设置完成: 左侧=307px, 右侧=1090px
2025-09-16 13:03:32 - src.gui.unified_data_import_window - INFO - 响应式布局初始化完成
2025-09-16 13:03:32 - src.gui.unified_data_import_window - INFO - 响应式分割器设置完成: 左侧=307px, 右侧=1090px
2025-09-16 13:03:34 - src.gui.unified_data_import_window - INFO - 开始打开高级配置对话框...
2025-09-16 13:03:34 - src.gui.unified_data_import_window - INFO - AdvancedConfigDialog 导入成功，开始初始化...
2025-09-16 13:03:34 - src.gui.advanced_config_dialog - INFO - 高级配置对话框开始初始化...
2025-09-16 13:03:34 - src.gui.advanced_config_dialog - INFO - 🚀 [P2-1修复] 配置文件监控已启动
2025-09-16 13:03:34 - src.gui.advanced_config_dialog - INFO - 🚀 [P2-1修复] 高级配置管理器初始化完成
2025-09-16 13:03:34 - src.gui.advanced_config_dialog - INFO - 开始初始化UI...
2025-09-16 13:03:34 - src.gui.advanced_config_dialog - ERROR - 高级配置对话框初始化失败: 'AdvancedConfigDialog' object has no attribute '_create_performance_tab'
2025-09-16 13:03:34 - src.gui.advanced_config_dialog - ERROR - 详细错误: Traceback (most recent call last):
  File "C:\test\salary_changes\salary_changes\src\gui\advanced_config_dialog.py", line 43, in __init__
    self._init_ui()
  File "C:\test\salary_changes\salary_changes\src\gui\advanced_config_dialog.py", line 114, in _init_ui
    self.perf_tab = self._create_performance_tab()
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AdvancedConfigDialog' object has no attribute '_create_performance_tab'

2025-09-16 13:03:34 - src.gui.unified_data_import_window - ERROR - 打开高级配置失败: 'AdvancedConfigDialog' object has no attribute '_create_performance_tab'
2025-09-16 13:03:34 - src.gui.unified_data_import_window - ERROR - 详细错误信息: Traceback (most recent call last):
  File "C:\test\salary_changes\salary_changes\src\gui\unified_data_import_window.py", line 2821, in _open_unified_advanced_settings
    dialog = AdvancedConfigDialog(self)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\test\salary_changes\salary_changes\src\gui\advanced_config_dialog.py", line 43, in __init__
    self._init_ui()
  File "C:\test\salary_changes\salary_changes\src\gui\advanced_config_dialog.py", line 114, in _init_ui
    self.perf_tab = self._create_performance_tab()
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AdvancedConfigDialog' object has no attribute '_create_performance_tab'

2025-09-16 13:06:32 - ConfigMonitor - INFO - 🔔 [配置监控] 停止监控
2025-09-16 13:06:32 - src.gui.unified_data_import_window - INFO - 🔔 [快速修复] 配置监控已停止
2025-09-16 13:06:32.159 | INFO     | src.gui.prototype.prototype_main_window:_show_unified_import_dialog:6081 | 用户取消了数据导入
2025-09-16 13:06:34.173 | INFO     | __main__:main:519 | 应用程序正常退出
