# 数据流与字段规范 v1.0

更新时间：2025-09-15
适用范围：工资表（全部在职人员、A岗职工、退休人员、离休人员）与异动表的导入、存储、读取、渲染全链路

## 1. 目标
- 统一字段命名与别名策略，确保“显示字段”稳定、可预期
- 明确数据流各阶段的职责与入参/出参
- 给出短中长期演进路径，逐步从“中文/英文双轨列”走向“英文规范列唯一真源”

## 2. 总体数据流

```mermaid
flowchart TD
  classDef node fill:#f5f7fa,stroke:#999,color:#222;
  A[Excel 原始表头(多为中文)]:::node --> B[导入器: 读取 + 清洗]:::node
  B --> C{建表策略}:::node
  C -->|工资表-当前| D[create_salary_data_table\n动态建表(含中文列)]:::node
  C -->|工资表-目标| D2[create_specialized_salary_table\n专用模板(英文规范列)]:::node
  C -->|异动表| E[create_change_data_table\n动态建表(中文业务列+系统列)]:::node
  D --> F[save_dataframe_to_table\n补齐缺失字段]:::node
  D2 --> F
  E --> F
  F --> G[SQLite 表数据]:::node
  G --> H[读取DataFrame]:::node --> I[统一格式管理/渲染]:::node --> J[UI表格展示]:::node
```

## 3. 字段规范（核心字段与别名）

- 核心英文规范字段（示例，按业务加扩展）：
  - employee_id（工号/人员代码）
  - employee_name（姓名）
  - department（部门名称/部门）
  - employee_type（人员类别）
  - employee_type_code（人员类别代码）
  - total_salary（应发工资）
  - 其余金额类使用浮点英文字段，如 allowance, nursing_fee, ...

- 典型中文→英文映射（非穷举，详见代码 FieldRegistry 与 DTM）：
  - 工号/人员代码 → employee_id
  - 姓名/员工姓名 → employee_name
  - 部门名称/部门 → department
  - 应发工资 → total_salary
  - 津贴 → allowance

- 合并（coalesce）策略（短期在渲染/读取侧实施）：
  - employee_id = coalesce(employee_id, 工号, 人员代码)
  - employee_name = coalesce(employee_name, 姓名)
  - department = coalesce(department, 部门名称, 部门)
  - 合并后仅保留英文规范列作为数据源参与渲染，中文列仅作为显示名映射

## 4. 表类型识别与传递
- 工资表表名：salary_data_YYYY_MM_{suffix}
  - suffix ∈ {all_active, a_grade, pension, retired}
- 映射关系：
  - all_active → active_employees
  - a_grade → a_grade_employees
  - pension → pension_employees
  - retired → retired_employees
- 渲染时应传递准确的 table_type，避免一律 active_employees

## 5. 导入与建表策略

- 当前实现（证据：multi_sheet_importer → create_salary_data_table）：
  - 工资表采用“动态模式”建表，直接把 DataFrame 列（多为中文）作为动态列写入表结构
  - 保存时对“表结构存在、DataFrame缺失”的列写默认值，中文别名列如“姓名/部门名称”被写成空，UI 使用中文列显示时出现空白

- 目标与阶段性方案：
  - P0（止血）：
    - 读取/渲染前执行字段合并（coalesce），优先英文规范列取值；修正 table_type 识别；再做表头去重
  - P1（修复）：
    - 保存阶段避免/同步中文别名列默认值（若英文已有值则不写空）；
    - 动态建表前先用 FieldRegistry 规范化可映射列，减少中文别名列入库
    - 导入器在可识别模板时优先 create_specialized_salary_table
  - P2（治理）：
    - 全面采用专用模板（英文规范列唯一真源）
    - 字段字典/别名中心前置统一
    - FieldRegistry/Renderer 单例与缓存

## 6. 验收与回归
- 显示验证：四类工资表的“工号/姓名/部门名称”应完整显示；抽查金额字段格式与小数位
- 日志核对：渲染阶段不再出现“表头重复”大规模去重；save_dataframe_to_table 不再频繁对“姓名/部门名称”写默认值
- 性能与稳定性：字段注册与渲染不重复初始化，分页统计正确

## 7. 风险与兼容
- 历史表中“中文/英文双轨”并存，读侧需要长期保持合并策略，直至数据迁移完成
- 切换专用模板需要测试导入/显示/导出全链路

## 8. 时序图（导入一次完整链路）

```mermaid
sequenceDiagram
  autonumber
  participant UI as UI
  participant MSI as 导入器
  participant DTM as 动态表管家
  participant DB as SQLite
  participant FR as 渲染器
  UI->>MSI: 选择Excel并导入
  MSI->>DTM: 动态建表(工资表-当前)
  DTM->>DB: 创建表(系统英文字段+中文动态列)
  MSI->>DTM: 保存数据
  DTM->>DTM: 补齐缺失(中文别名列被写默认)
  DTM->>DB: 事务提交
  UI->>DTM: 读取表
  DTM->>UI: 返回DataFrame
  UI->>FR: 字段合并+渲染
  FR->>UI: 正确显示
```

