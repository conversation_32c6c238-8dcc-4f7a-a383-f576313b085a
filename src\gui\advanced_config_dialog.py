"""
高级配置对话框
提供个性化设置、数据处理选项等高级配置功能
"""

import json
import os
from typing import Dict, Any
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QTabWidget, QWidget, QLabel, QPushButton, QComboBox,
    QLineEdit, QSpinBox, QDoubleSpinBox, QCheckBox, QSlider,
    QTextEdit, QListWidget, QGroupBox, QFrame, QMessageBox,
    QProgressBar, QSizePolicy, QScrollArea, QFileDialog
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont
from datetime import datetime

from src.modules.logging.setup_logger import setup_logger
from src.modules.config.advanced_config_manager import AdvancedConfigManager, ConfigLevel


class AdvancedConfigDialog(QDialog):
    """高级配置对话框"""
    
    config_changed = pyqtSignal(dict)  # 配置变化信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        try:
            self.logger = setup_logger(__name__)
            self.logger.info("高级配置对话框开始初始化...")
            
            self.config_file = "config/advanced_settings.json"
            self.config = {}
            # 复用父窗口的高级配置管理器，否则自行创建（使用统一目录 config_advanced/）
            self.config_manager = getattr(parent, 'advanced_config_manager', None)
            if self.config_manager is None:
                self.config_manager = AdvancedConfigManager(base_dir="config_advanced", logger=self.logger)
            
            self.logger.info("开始初始化UI...")
            self._init_ui()
            
            self.logger.info("开始加载配置...")
            self._load_config()
            
            self.logger.info("开始连接信号...")
            self._connect_signals()
            
            self.logger.info("高级配置对话框初始化完成")
        except Exception as e:
            import traceback
            error_msg = f"高级配置对话框初始化失败: {e}"
            traceback_msg = f"详细错误: {traceback.format_exc()}"
            
            # 同时输出到控制台和日志文件
            print(error_msg)
            print(traceback_msg)
            
            if hasattr(self, 'logger'):
                self.logger.error(error_msg)
                self.logger.error(traceback_msg)
            else:
                # 如果日志系统也失败了，至少保证控制台有输出
                print("警告: 日志系统不可用，仅输出到控制台")
            raise
    
    def _init_ui(self):
        """初始化UI"""
        self.setWindowTitle("高级配置")
        self.setMinimumSize(900, 650)
        self.resize(1000, 720)
        self.setSizeGripEnabled(True)  # 允许用户调整大小
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)
        
        # 标题
        title_label = QLabel("🔧 高级配置设置")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        # 选项卡
        self.tab_widget = QTabWidget()
        
        # 文件导入设置
        self.file_tab = self._create_file_import_tab()
        self.tab_widget.addTab(self.file_tab, "📄 文件导入")
        
        # 字段映射设置
        self.mapping_tab = self._create_field_mapping_tab()
        self.tab_widget.addTab(self.mapping_tab, "🚨 字段映射")
        
        # 智能推荐设置
        self.smart_tab = self._create_smart_recommendations_tab()
        self.tab_widget.addTab(self.smart_tab, "🤖 智能推荐")
        
        # 数据处理设置
        self.data_tab = self._create_data_processing_tab()
        self.tab_widget.addTab(self.data_tab, "📊 数据处理")
        
        # 界面个性化
        self.ui_tab = self._create_ui_customization_tab()
        self.tab_widget.addTab(self.ui_tab, "🎨 界面设置")
        
        # 性能优化
        self.perf_tab = self._create_performance_tab()
        self.tab_widget.addTab(self.perf_tab, "⚡ 性能优化")
        
        main_layout.addWidget(self.tab_widget)
        
        # 底部按钮
        button_layout = self._create_bottom_buttons()
        main_layout.addLayout(button_layout)

    def _create_file_import_tab(self) -> QWidget:
        """创建 文件导入 设置选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(12)

        group = QGroupBox("文件与Excel结构")
        form = QFormLayout(group)

        # 默认导入路径与浏览按钮
        path_row = QHBoxLayout()
        self.default_import_path = QLineEdit()
        self.browse_import_path_btn = QPushButton("浏览...")
        path_row.addWidget(self.default_import_path)
        path_row.addWidget(self.browse_import_path_btn)
        form.addRow("默认导入路径:", path_row)

        # 支持格式
        fmt_row = QHBoxLayout()
        self.support_xlsx = QCheckBox("xlsx")
        self.support_xls = QCheckBox("xls")
        self.support_csv = QCheckBox("csv")
        self.support_xlsx.setChecked(True)
        self.support_xls.setChecked(True)
        self.support_csv.setChecked(True)
        fmt_row.addWidget(self.support_xlsx)
        fmt_row.addWidget(self.support_xls)
        fmt_row.addWidget(self.support_csv)
        fmt_row.addStretch()
        form.addRow("支持格式:", fmt_row)

        # 文件大小与编码检测
        self.max_file_size = QSpinBox()
        self.max_file_size.setRange(1, 10240)
        self.max_file_size.setSuffix(" MB")
        self.max_file_size.setValue(100)
        form.addRow("最大文件大小:", self.max_file_size)

        self.auto_detect_encoding = QCheckBox("自动检测编码")
        self.auto_detect_encoding.setChecked(True)
        form.addRow("编码检测:", self.auto_detect_encoding)

        # Sheet 选择策略
        self.sheet_selection_strategy = QComboBox()
        self.sheet_selection_strategy.addItems(["全部", "第一个", "手动选择"])
        # internal mapping: all/first/manual
        form.addRow("Sheet选择:", self.sheet_selection_strategy)

        # Excel 结构
        self.data_start_row = QSpinBox(); self.data_start_row.setRange(1, 9999); self.data_start_row.setValue(2)
        self.header_row = QSpinBox(); self.header_row.setRange(1, 9999); self.header_row.setValue(1)
        self.skip_rows = QSpinBox(); self.skip_rows.setRange(0, 9999)
        self.skip_footer_rows = QSpinBox(); self.skip_footer_rows.setRange(0, 9999)
        self.auto_detect_header = QCheckBox("自动检测表头"); self.auto_detect_header.setChecked(True)
        self.ignore_empty_rows = QCheckBox("忽略空行"); self.ignore_empty_rows.setChecked(True)

        form.addRow("数据起始行:", self.data_start_row)
        form.addRow("表头所在行:", self.header_row)
        form.addRow("跳过行数:", self.skip_rows)
        form.addRow("跳过尾部行:", self.skip_footer_rows)
        form.addRow("表头自动检测:", self.auto_detect_header)
        form.addRow("忽略空行:", self.ignore_empty_rows)

        layout.addWidget(group)
        layout.addStretch()
        return tab

    def _create_field_mapping_tab(self) -> QWidget:
        """创建 字段映射 设置选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        form = QFormLayout()

        self.mapping_algorithm = QComboBox()
        self.mapping_algorithm.addItems(["模糊匹配", "精确匹配", "语义匹配", "混合"])
        self.similarity_threshold = QSpinBox(); self.similarity_threshold.setRange(0, 100); self.similarity_threshold.setValue(80); self.similarity_threshold.setSuffix(" %")
        self.auto_mapping_enabled = QCheckBox("启用自动映射"); self.auto_mapping_enabled.setChecked(True)
        self.required_field_check = QCheckBox("启用必填字段检查"); self.required_field_check.setChecked(True)
        self.field_type_validation = QCheckBox("启用字段类型验证"); self.field_type_validation.setChecked(True)
        self.save_mapping_history = QCheckBox("保存映射历史"); self.save_mapping_history.setChecked(True)

        form.addRow("映射算法:", self.mapping_algorithm)
        form.addRow("相似度阈值:", self.similarity_threshold)
        form.addRow("自动映射:", self.auto_mapping_enabled)
        form.addRow("必填检查:", self.required_field_check)
        form.addRow("类型验证:", self.field_type_validation)
        form.addRow("保存映射历史:", self.save_mapping_history)

        layout.addLayout(form)
        layout.addStretch()
        return tab

    def _create_smart_recommendations_tab(self) -> QWidget:
        """创建智能推荐设置选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)

        # 使用可滚动容器，避免内容被裁剪
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(15)

        # 映射推荐设置
        mapping_group = QGroupBox("映射推荐设置")
        mapping_layout = QFormLayout(mapping_group)
        mapping_layout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)

        # 推荐置信度阈值
        self.confidence_threshold = QSlider(Qt.Horizontal)
        self.confidence_threshold.setRange(50, 95)
        self.confidence_threshold.setValue(70)
        self.confidence_threshold.setTickPosition(QSlider.TicksBelow)
        self.confidence_threshold.setTickInterval(10)

        confidence_layout = QHBoxLayout()
        confidence_layout.addWidget(self.confidence_threshold)
        self.confidence_label = QLabel("70%")
        confidence_layout.addWidget(self.confidence_label)

        mapping_layout.addRow("推荐置信度阈值:", confidence_layout)

        # 历史学习开关
        self.enable_history_learning = QCheckBox("启用历史学习")
        self.enable_history_learning.setChecked(True)
        mapping_layout.addRow("", self.enable_history_learning)

        # 语义分析开关
        self.enable_semantic_analysis = QCheckBox("启用语义分析")
        self.enable_semantic_analysis.setChecked(True)
        mapping_layout.addRow("", self.enable_semantic_analysis)

        # 自动应用高置信度推荐
        self.auto_apply_high_confidence = QCheckBox("自动应用高置信度推荐")
        self.auto_apply_high_confidence.setChecked(False)
        mapping_layout.addRow("", self.auto_apply_high_confidence)

        content_layout.addWidget(mapping_group)

        # 模板管理设置
        template_group = QGroupBox("模板管理设置")
        template_layout = QFormLayout(template_group)
        template_layout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)

        # 模板推荐优先级
        self.template_priority = QComboBox()
        self.template_priority.addItems([
            "优先用户模板", "优先系统模板", "混合推荐"
        ])
        template_layout.addRow("模板推荐优先级:", self.template_priority)

        # 最大保存模板数
        self.max_saved_templates = QSpinBox()
        self.max_saved_templates.setRange(10, 100)
        self.max_saved_templates.setValue(50)
        template_layout.addRow("最大保存模板数:", self.max_saved_templates)

        content_layout.addWidget(template_group)

        # LLM 映射设置
        llm_group = QGroupBox("LLM 映射设置")
        llm_layout = QFormLayout(llm_group)
        llm_layout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)

        # 启用LLM映射
        self.use_llm_mapping = QCheckBox("启用大模型映射（LLM）")
        self.use_llm_mapping.setChecked(False)
        llm_layout.addRow("", self.use_llm_mapping)

        # 提供方
        self.llm_provider = QComboBox()
        self.llm_provider.addItems(["anthropic", "openai", "azure", "local_http"])  # anthropic: Claude API 兼容
        self.llm_provider.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        llm_layout.addRow("提供方:", self.llm_provider)

        # API Base
        self.llm_api_base = QLineEdit()
        self.llm_api_base.setPlaceholderText("可选，例如 https://open.bigmodel.cn/api/anthropic 或 https://api.openai.com/v1")
        self.llm_api_base.setMinimumWidth(420)
        self.llm_api_base.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        llm_layout.addRow("API Base:", self.llm_api_base)

        # 模型
        self.llm_model = QLineEdit()
        self.llm_model.setPlaceholderText("模型名称，例如 glm-4.5")
        self.llm_model.setMinimumWidth(420)
        self.llm_model.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        llm_layout.addRow("模型:", self.llm_model)

        # 超时/重试/缓存TTL/脱敏/采样行数
        self.llm_timeout = QSpinBox()
        self.llm_timeout.setRange(5, 120)
        self.llm_timeout.setValue(15)
        self.llm_timeout.setSuffix(" 秒")
        llm_layout.addRow("超时:", self.llm_timeout)

        self.llm_retries = QSpinBox()
        self.llm_retries.setRange(0, 5)
        self.llm_retries.setValue(1)
        llm_layout.addRow("最大重试次数:", self.llm_retries)

        self.llm_cache_ttl = QSpinBox()
        self.llm_cache_ttl.setRange(5, 10080)  # 最多7天
        self.llm_cache_ttl.setValue(1440)
        self.llm_cache_ttl.setSuffix(" 分钟")
        llm_layout.addRow("缓存TTL:", self.llm_cache_ttl)

        self.llm_redact_pii = QCheckBox("上传前脱敏（推荐）")
        self.llm_redact_pii.setChecked(True)
        llm_layout.addRow("", self.llm_redact_pii)

        self.llm_sample_rows = QSpinBox()
        self.llm_sample_rows.setRange(0, 50)
        self.llm_sample_rows.setValue(8)
        self.llm_sample_rows.setSuffix(" 行/列")
        llm_layout.addRow("每列采样行数:", self.llm_sample_rows)

        content_layout.addWidget(llm_group)
        content_layout.addStretch()

        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)
        return tab

    def _create_data_processing_tab(self) -> QWidget:
        """创建 数据处理 设置选项卡（含模板区）"""
        tab = QWidget()
        outer = QVBoxLayout(tab)
        outer.setSpacing(12)
        
        # 使用滚动区域以适配较多配置项
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(12)

        # 基本设置
        basic_group = QGroupBox("基本设置")
        basic_form = QFormLayout(basic_group)
        basic_form.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)

        self.strict_validation = QCheckBox("严格验证模式"); self.strict_validation.setChecked(False)
        self.null_value_strategy = QComboBox(); self.null_value_strategy.addItems(["保留", "默认值", "跳过", "提示"])
        self.auto_type_conversion = QCheckBox("自动类型转换"); self.auto_type_conversion.setChecked(True)
        self.duplicate_strategy = QComboBox(); self.duplicate_strategy.addItems(["保留", "去重"])
        self.batch_size = QSpinBox(); self.batch_size.setRange(1, 1000000); self.batch_size.setValue(1000)
        self.error_tolerance = QSpinBox(); self.error_tolerance.setRange(0, 100); self.error_tolerance.setValue(10); self.error_tolerance.setSuffix(" %")

        basic_form.addRow("严格验证:", self.strict_validation)
        basic_form.addRow("空值处理:", self.null_value_strategy)
        basic_form.addRow("自动类型转换:", self.auto_type_conversion)
        basic_form.addRow("重复策略:", self.duplicate_strategy)
        basic_form.addRow("批量大小:", self.batch_size)
        basic_form.addRow("错误容忍度:", self.error_tolerance)
        content_layout.addWidget(basic_group)

        # 进阶开关
        adv_group = QGroupBox("进阶与清洗")
        adv_form = QFormLayout(adv_group)
        adv_form.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)
        self.remove_duplicates_cb = QCheckBox("去重")
        self.missing_values_combo = QComboBox(); self.missing_values_combo.addItem("保留空值", userData="keep"); self.missing_values_combo.addItem("删除含空值行", userData="remove"); self.missing_values_combo.addItem("空值置0", userData="fill_zero"); self.missing_values_combo.addItem("空值置均值", userData="fill_mean")
        self.format_numbers_cb = QCheckBox("格式化数字"); self.format_numbers_cb.setChecked(True)
        self.format_dates_cb = QCheckBox("格式化日期"); self.format_dates_cb.setChecked(True)
        self.trim_whitespace_cb = QCheckBox("去除空白"); self.trim_whitespace_cb.setChecked(True)
        self.convert_types_cb = QCheckBox("类型转换"); self.convert_types_cb.setChecked(True)
        self.data_validation_cb = QCheckBox("数据验证"); self.data_validation_cb.setChecked(True)

        adv_form.addRow("去重:", self.remove_duplicates_cb)
        adv_form.addRow("缺失值:", self.missing_values_combo)
        adv_form.addRow("格式化数字:", self.format_numbers_cb)
        adv_form.addRow("格式化日期:", self.format_dates_cb)
        adv_form.addRow("去除空白:", self.trim_whitespace_cb)
        adv_form.addRow("类型转换:", self.convert_types_cb)
        adv_form.addRow("数据验证:", self.data_validation_cb)
        content_layout.addWidget(adv_group)

        # 模板与自定义规则
        tpl_group = QGroupBox("模板与自定义规则")
        tpl_layout = QVBoxLayout(tpl_group)
        self.rules_list = QListWidget()
        self.rules_list.setMinimumHeight(160)
        tpl_btns = QHBoxLayout()
        self.add_rule_btn = QPushButton("添加规则")
        self.edit_rule_btn = QPushButton("编辑规则")
        self.remove_rule_btn = QPushButton("删除规则")
        tpl_btns.addWidget(self.add_rule_btn)
        tpl_btns.addWidget(self.edit_rule_btn)
        tpl_btns.addWidget(self.remove_rule_btn)
        tpl_layout.addWidget(self.rules_list)
        tpl_layout.addLayout(tpl_btns)
        content_layout.addWidget(tpl_group)

        # 状态栏
        self.status_label = QLabel("")
        content_layout.addWidget(self.status_label)
        content_layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        outer.addWidget(scroll_area)
        return tab

    def _create_ui_customization_tab(self) -> QWidget:
        """创建 界面设置 选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(12)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)

        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(16)

        # 界面显示设置组
        ui_group = QGroupBox("界面显示设置")
        ui_layout = QFormLayout(ui_group)

        self.table_row_limit = QSpinBox()
        self.table_row_limit.setRange(50, 10000)
        self.table_row_limit.setValue(200)
        self.table_row_limit.setSuffix(" 行")
        ui_layout.addRow("表格行数限制:", self.table_row_limit)

        self.show_detailed_logs = QCheckBox("显示详细日志")
        ui_layout.addRow("日志设置:", self.show_detailed_logs)

        self.show_confidence_indicators = QCheckBox("显示置信度指示器")
        self.show_confidence_indicators.setChecked(True)
        ui_layout.addRow("置信度显示:", self.show_confidence_indicators)

        self.auto_save_interval = QSpinBox()
        self.auto_save_interval.setRange(1, 60)
        self.auto_save_interval.setValue(5)
        self.auto_save_interval.setSuffix(" 分钟")
        ui_layout.addRow("自动保存间隔:", self.auto_save_interval)

        self.show_confirmation_dialogs = QCheckBox("显示确认对话框")
        self.show_confirmation_dialogs.setChecked(True)
        ui_layout.addRow("确认对话框:", self.show_confirmation_dialogs)

        self.show_shortcuts = QCheckBox("显示快捷键提示")
        self.show_shortcuts.setChecked(True)
        ui_layout.addRow("快捷键提示:", self.show_shortcuts)

        content_layout.addWidget(ui_group)
        content_layout.addStretch()

        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)
        return tab
    
    def _set_default_config(self):
        """设置默认配置"""
        self.config = {
            'file_import': {
                'default_import_path': '',
                'supported_formats': ['xlsx', 'xls', 'csv'],
                'max_file_size_mb': 100,
                'auto_detect_encoding': True,
                'sheet_selection_strategy': 'all',
                # Excel表结构配置
                'data_start_row': 2,
                'header_row': 1,
                'skip_rows': 0,
                'skip_footer_rows': 0,
                'auto_detect_header': True,
                'ignore_empty_rows': True
            },
            'field_mapping': {
                'mapping_algorithm': 'fuzzy_match',
                'similarity_threshold': 80,
                'auto_mapping_enabled': True,
                'required_field_check': True,
                'field_type_validation': True,
                'save_mapping_history': True
            },
            'smart_recommendations': {
                'confidence_threshold': 70,
                'enable_history_learning': True,
                'enable_semantic_analysis': True,
                'auto_apply_high_confidence': False,
                'template_priority': 0,
                'max_saved_templates': 50
            },
            'llm': {
                'use_llm_mapping': False,
                'provider': 'anthropic',
                'api_base': 'https://open.bigmodel.cn/api/anthropic',
                'model': 'glm-4.5',
                'timeout_seconds': 15,
                'max_retries': 1,
                'cache_ttl_minutes': 1440,
                'redact_pii': True,
                'sample_rows_per_field': 8
            },
            'data_processing': {
                'strict_validation': False,
                'null_value_strategy': 0,
                'auto_type_conversion': True,
                'duplicate_strategy': 0,
                'batch_size': 1000,
                'error_tolerance': 10,
                'remove_duplicates': False,
                'handle_missing_values': 'keep',
                'format_numbers': True,
                'format_dates': True,
                'trim_whitespace': True,
                'convert_data_types': True,
                'data_validation': True,
                'custom_rules': [],
                'saved_templates': []
            },
            'ui_customization': {
                'table_row_limit': 200,
                'show_detailed_logs': False,
                'show_confidence_indicators': True,
                'auto_save_interval': 5,
                'show_confirmation_dialogs': True,
                'show_shortcuts': True
            },
            'performance': {
                'max_memory_usage': 2048,
                'enable_caching': True,
                'preload_data': False,
                'thread_count': 4,
                'enable_async_processing': True,
                'progress_update_frequency': 100
            }
        }
    
    def _apply_config_to_ui(self):
        """将配置应用到UI"""
        # 文件导入配置
        file_import = self.config.get('file_import', {})
        self.default_import_path.setText(file_import.get('default_import_path', ''))
        supported_formats = file_import.get('supported_formats', ['xlsx', 'xls', 'csv'])
        self.support_xlsx.setChecked('xlsx' in supported_formats)
        self.support_xls.setChecked('xls' in supported_formats)
        self.support_csv.setChecked('csv' in supported_formats)
        self.max_file_size.setValue(file_import.get('max_file_size_mb', 100))
        self.auto_detect_encoding.setChecked(file_import.get('auto_detect_encoding', True))
        strategy_map = {'all': 0, 'first': 1, 'manual': 2}
        strategy = file_import.get('sheet_selection_strategy', 'all')
        self.sheet_selection_strategy.setCurrentIndex(strategy_map.get(strategy, 0))
        
        # Excel表结构配置
        self.data_start_row.setValue(file_import.get('data_start_row', 2))
        self.header_row.setValue(file_import.get('header_row', 1))
        self.skip_rows.setValue(file_import.get('skip_rows', 0))
        self.skip_footer_rows.setValue(file_import.get('skip_footer_rows', 0))
        self.auto_detect_header.setChecked(file_import.get('auto_detect_header', True))
        self.ignore_empty_rows.setChecked(file_import.get('ignore_empty_rows', True))
        
        # 字段映射配置
        field_mapping = self.config.get('field_mapping', {})
        algorithm_map = {'fuzzy_match': 0, 'exact_match': 1, 'semantic_match': 2, 'hybrid': 3}
        algorithm = field_mapping.get('mapping_algorithm', 'fuzzy_match')
        self.mapping_algorithm.setCurrentIndex(algorithm_map.get(algorithm, 0))
        self.similarity_threshold.setValue(field_mapping.get('similarity_threshold', 80))
        self.auto_mapping_enabled.setChecked(field_mapping.get('auto_mapping_enabled', True))
        self.required_field_check.setChecked(field_mapping.get('required_field_check', True))
        self.field_type_validation.setChecked(field_mapping.get('field_type_validation', True))
        self.save_mapping_history.setChecked(field_mapping.get('save_mapping_history', True))
        
        # 智能推荐配置
        smart = self.config.get('smart_recommendations', {})
        self.confidence_threshold.setValue(smart.get('confidence_threshold', 70))
        self.enable_history_learning.setChecked(smart.get('enable_history_learning', True))
        self.enable_semantic_analysis.setChecked(smart.get('enable_semantic_analysis', True))
        self.auto_apply_high_confidence.setChecked(smart.get('auto_apply_high_confidence', False))
        self.template_priority.setCurrentIndex(smart.get('template_priority', 0))
        self.max_saved_templates.setValue(smart.get('max_saved_templates', 50))
        
        # LLM配置
        llm = self.config.get('llm', {})
        if hasattr(self, 'use_llm_mapping'):
            self.use_llm_mapping.setChecked(llm.get('use_llm_mapping', False))
        if hasattr(self, 'llm_provider'):
            prov = llm.get('provider', 'anthropic')
            idx = self.llm_provider.findText(prov)
            self.llm_provider.setCurrentIndex(idx if idx >= 0 else 0)
        if hasattr(self, 'llm_api_base'):
            self.llm_api_base.setText(llm.get('api_base', 'https://open.bigmodel.cn/api/anthropic'))
        if hasattr(self, 'llm_model'):
            self.llm_model.setText(llm.get('model', 'glm-4.5'))
        if hasattr(self, 'llm_timeout'):
            self.llm_timeout.setValue(llm.get('timeout_seconds', 15))
        if hasattr(self, 'llm_retries'):
            self.llm_retries.setValue(llm.get('max_retries', 1))
        if hasattr(self, 'llm_cache_ttl'):
            self.llm_cache_ttl.setValue(llm.get('cache_ttl_minutes', 1440))
        if hasattr(self, 'llm_redact_pii'):
            self.llm_redact_pii.setChecked(llm.get('redact_pii', True))
        if hasattr(self, 'llm_sample_rows'):
            self.llm_sample_rows.setValue(llm.get('sample_rows_per_field', 8))
        
        # 数据处理配置
        data = self.config.get('data_processing', {})
        self.strict_validation.setChecked(data.get('strict_validation', False))
        self.null_value_strategy.setCurrentIndex(data.get('null_value_strategy', 0))
        self.auto_type_conversion.setChecked(data.get('auto_type_conversion', True))
        self.duplicate_strategy.setCurrentIndex(data.get('duplicate_strategy', 0))
        self.batch_size.setValue(data.get('batch_size', 1000))
        self.error_tolerance.setValue(data.get('error_tolerance', 10))

        # 新增的数据处理配置项
        if hasattr(self, 'remove_duplicates_cb'):
            self.remove_duplicates_cb.setChecked(data.get('remove_duplicates', False))
        if hasattr(self, 'missing_values_combo'):
            missing_value = data.get('handle_missing_values', 'keep')
            for i in range(self.missing_values_combo.count()):
                if self.missing_values_combo.itemData(i) == missing_value:
                    self.missing_values_combo.setCurrentIndex(i)
                    break
        if hasattr(self, 'format_numbers_cb'):
            self.format_numbers_cb.setChecked(data.get('format_numbers', True))
        if hasattr(self, 'format_dates_cb'):
            self.format_dates_cb.setChecked(data.get('format_dates', True))
        if hasattr(self, 'trim_whitespace_cb'):
            self.trim_whitespace_cb.setChecked(data.get('trim_whitespace', True))
        if hasattr(self, 'convert_types_cb'):
            self.convert_types_cb.setChecked(data.get('convert_data_types', True))
        if hasattr(self, 'data_validation_cb'):
            self.data_validation_cb.setChecked(data.get('data_validation', True))

        # 加载自定义规则
        if hasattr(self, 'rules_list'):
            self.rules_list.clear()
            custom_rules = data.get('custom_rules', [])
            for rule in custom_rules:
                self.rules_list.addItem(rule)

        # 更新处理配置
        if hasattr(self, 'processing_config'):
            self.processing_config.update({
                'remove_duplicates': data.get('remove_duplicates', False),
                'handle_missing_values': data.get('handle_missing_values', 'keep'),
                'format_numbers': data.get('format_numbers', True),
                'format_dates': data.get('format_dates', True),
                'trim_whitespace': data.get('trim_whitespace', True),
                'convert_data_types': data.get('convert_data_types', True),
                'data_validation': data.get('data_validation', True),
                'custom_rules': data.get('custom_rules', [])
            })
        
        # UI个性化配置
        ui = self.config.get('ui_customization', {})
        self.table_row_limit.setValue(ui.get('table_row_limit', 200))
        self.show_detailed_logs.setChecked(ui.get('show_detailed_logs', False))
        self.show_confidence_indicators.setChecked(ui.get('show_confidence_indicators', True))
        self.auto_save_interval.setValue(ui.get('auto_save_interval', 5))
        self.show_confirmation_dialogs.setChecked(ui.get('show_confirmation_dialogs', True))
        self.show_shortcuts.setChecked(ui.get('show_shortcuts', True))
        
        # 性能优化配置
        perf = self.config.get('performance', {})
        self.max_memory_usage.setValue(perf.get('max_memory_usage', 2048))
        self.enable_caching.setChecked(perf.get('enable_caching', True))
        self.preload_data.setChecked(perf.get('preload_data', False))
        self.thread_count.setValue(perf.get('thread_count', 4))
        self.enable_async_processing.setChecked(perf.get('enable_async_processing', True))
        self.progress_update_frequency.setValue(perf.get('progress_update_frequency', 100))
    
    def _collect_config_from_ui(self) -> Dict[str, Any]:
        """从UI收集配置"""
        # 获取支持的文件格式
        supported_formats = []
        if self.support_xlsx.isChecked():
            supported_formats.append('xlsx')
        if self.support_xls.isChecked():
            supported_formats.append('xls')
        if self.support_csv.isChecked():
            supported_formats.append('csv')
        
        # 工作表选择策略映射
        strategy_names = ['all', 'first', 'manual']
        sheet_strategy = strategy_names[self.sheet_selection_strategy.currentIndex()]
        
        # 映射算法映射
        algorithm_names = ['fuzzy_match', 'exact_match', 'semantic_match', 'hybrid']
        mapping_algorithm = algorithm_names[self.mapping_algorithm.currentIndex()]
        
        return {
            'file_import': {
                'default_import_path': self.default_import_path.text(),
                'supported_formats': supported_formats,
                'max_file_size_mb': self.max_file_size.value(),
                'auto_detect_encoding': self.auto_detect_encoding.isChecked(),
                'sheet_selection_strategy': sheet_strategy,
                # Excel表结构配置
                'data_start_row': self.data_start_row.value(),
                'header_row': self.header_row.value(),
                'skip_rows': self.skip_rows.value(),
                'skip_footer_rows': self.skip_footer_rows.value(),
                'auto_detect_header': self.auto_detect_header.isChecked(),
                'ignore_empty_rows': self.ignore_empty_rows.isChecked()
            },
            'field_mapping': {
                'mapping_algorithm': mapping_algorithm,
                'similarity_threshold': self.similarity_threshold.value(),
                'auto_mapping_enabled': self.auto_mapping_enabled.isChecked(),
                'required_field_check': self.required_field_check.isChecked(),
                'field_type_validation': self.field_type_validation.isChecked(),
                'save_mapping_history': self.save_mapping_history.isChecked()
            },
            'smart_recommendations': {
                'confidence_threshold': self.confidence_threshold.value(),
                'enable_history_learning': self.enable_history_learning.isChecked(),
                'enable_semantic_analysis': self.enable_semantic_analysis.isChecked(),
                'auto_apply_high_confidence': self.auto_apply_high_confidence.isChecked(),
                'template_priority': self.template_priority.currentIndex(),
                'max_saved_templates': self.max_saved_templates.value()
            },
            'llm': {
                'use_llm_mapping': getattr(self, 'use_llm_mapping', None) and self.use_llm_mapping.isChecked(),
                'provider': getattr(self, 'llm_provider', None) and self.llm_provider.currentText() or 'anthropic',
                'api_base': getattr(self, 'llm_api_base', None) and self.llm_api_base.text() or 'https://open.bigmodel.cn/api/anthropic',
                'model': getattr(self, 'llm_model', None) and self.llm_model.text() or 'glm-4.5',
                'timeout_seconds': getattr(self, 'llm_timeout', None) and self.llm_timeout.value() or 15,
                'max_retries': getattr(self, 'llm_retries', None) and self.llm_retries.value() or 1,
                'cache_ttl_minutes': getattr(self, 'llm_cache_ttl', None) and self.llm_cache_ttl.value() or 1440,
                'redact_pii': getattr(self, 'llm_redact_pii', None) and self.llm_redact_pii.isChecked(),
                'sample_rows_per_field': getattr(self, 'llm_sample_rows', None) and self.llm_sample_rows.value() or 8
            },
            'data_processing': {
                'strict_validation': self.strict_validation.isChecked(),
                'null_value_strategy': self.null_value_strategy.currentIndex(),
                'auto_type_conversion': self.auto_type_conversion.isChecked(),
                'duplicate_strategy': self.duplicate_strategy.currentIndex(),
                'batch_size': self.batch_size.value(),
                'error_tolerance': self.error_tolerance.value(),
                'remove_duplicates': getattr(self, 'remove_duplicates_cb', None) and self.remove_duplicates_cb.isChecked(),
                'handle_missing_values': getattr(self, 'missing_values_combo', None) and (self.missing_values_combo.currentData() or 'keep'),
                'format_numbers': getattr(self, 'format_numbers_cb', None) and self.format_numbers_cb.isChecked(),
                'format_dates': getattr(self, 'format_dates_cb', None) and self.format_dates_cb.isChecked(),
                'trim_whitespace': getattr(self, 'trim_whitespace_cb', None) and self.trim_whitespace_cb.isChecked(),
                'convert_data_types': getattr(self, 'convert_types_cb', None) and self.convert_types_cb.isChecked(),
                'data_validation': getattr(self, 'data_validation_cb', None) and self.data_validation_cb.isChecked(),
                'custom_rules': [self.rules_list.item(i).text() for i in range(self.rules_list.count())] if hasattr(self, 'rules_list') else [],
                'saved_templates': []
            },
            'ui_customization': {
                'table_row_limit': self.table_row_limit.value(),
                'show_detailed_logs': self.show_detailed_logs.isChecked(),
                'show_confidence_indicators': self.show_confidence_indicators.isChecked(),
                'auto_save_interval': self.auto_save_interval.value(),
                'show_confirmation_dialogs': self.show_confirmation_dialogs.isChecked(),
                'show_shortcuts': self.show_shortcuts.isChecked()
            },
            'performance': {
                'max_memory_usage': self.max_memory_usage.value(),
                'enable_caching': self.enable_caching.isChecked(),
                'preload_data': self.preload_data.isChecked(),
                'thread_count': self.thread_count.value(),
                'enable_async_processing': self.enable_async_processing.isChecked(),
                'progress_update_frequency': self.progress_update_frequency.value()
            }
        }
    
    def _save_config(self):
        """保存配置"""
        try:
            # 保存到 AdvancedConfigManager（用户层），启用版本
            ok = self.config_manager.set_config("advanced_settings", self.config, level=ConfigLevel.USER, create_version=True)
            if ok:
                self.logger.info("高级配置保存成功 (AdvancedConfigManager: USER)")
            else:
                self.logger.warning("高级配置保存失败（AdvancedConfigManager 返回 False）")
            
        except Exception as e:
            self.logger.error(f"保存高级配置失败: {e}")
            QMessageBox.warning(self, "保存失败", f"配置保存失败: {e}")
    
    def _reset_to_defaults(self):
        """重置为默认设置"""
        reply = QMessageBox.question(
            self, "确认重置", 
            "确定要重置所有设置为默认值吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self._set_default_config()
            self._apply_config_to_ui()
            self.logger.info("配置已重置为默认值")
    
    def _export_config(self):
        """导出配置"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出配置", 
            "advanced_config.json",
            "JSON Files (*.json)"
        )
        
        if file_path:
            try:
                current_config = self._collect_config_from_ui()
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(current_config, f, ensure_ascii=False, indent=2)
                
                QMessageBox.information(self, "导出成功", f"配置已导出到: {file_path}")
                self.logger.info(f"配置导出成功: {file_path}")
                
            except Exception as e:
                QMessageBox.warning(self, "导出失败", f"配置导出失败: {e}")
                self.logger.error(f"配置导出失败: {e}")
    
    def _import_config(self):
        """导入配置"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入配置",
            "",
            "JSON Files (*.json)"
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    imported_config = json.load(f)
                
                self.config = imported_config
                self._apply_config_to_ui()
                
                QMessageBox.information(self, "导入成功", "配置导入成功")
                self.logger.info(f"配置导入成功: {file_path}")
                
            except Exception as e:
                QMessageBox.warning(self, "导入失败", f"配置导入失败: {e}")
                self.logger.error(f"配置导入失败: {e}")
    
    def _save_and_close(self):
        """保存配置并关闭"""
        self.config = self._collect_config_from_ui()
        self._save_config()
        
        # 发送配置变化信号
        self.config_changed.emit(self.config)
        
        self.accept()
    
    def _update_system_info(self):
        """更新系统信息"""
        import psutil
        import platform
        
        try:
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            
            info_text = f"""系统: {platform.system()} {platform.release()}
CPU使用率: {cpu_percent}%
内存使用: {memory.percent}% ({memory.used // 1024**2}MB / {memory.total // 1024**2}MB)
Python版本: {platform.python_version()}"""
            
            self.system_info.setPlainText(info_text)
            
        except ImportError:
            self.system_info.setPlainText("系统信息获取需要安装psutil模块")
        except Exception as e:
            self.system_info.setPlainText(f"获取系统信息失败: {e}")
    
    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        return self.config.copy()

    # ==================== 数据处理功能方法 ====================

    def _on_processing_config_changed(self):
        """数据处理配置变化处理"""
        if hasattr(self, 'processing_config'):
            self._update_processing_config()
            if hasattr(self, 'status_label'):
                self.status_label.setText("配置已更新")
                self.status_label.setStyleSheet("color: green; font-weight: bold;")

    def _update_processing_config(self):
        """更新处理配置"""
        if hasattr(self, 'processing_config'):
            self.processing_config.update({
                'remove_duplicates': getattr(self, 'remove_duplicates_cb', None) and self.remove_duplicates_cb.isChecked(),
                'handle_missing_values': getattr(self, 'missing_values_combo', None) and (self.missing_values_combo.currentData() or 'keep'),
                'data_validation': getattr(self, 'data_validation_cb', None) and self.data_validation_cb.isChecked(),
                'format_numbers': getattr(self, 'format_numbers_cb', None) and self.format_numbers_cb.isChecked(),
                'format_dates': getattr(self, 'format_dates_cb', None) and self.format_dates_cb.isChecked(),
                'trim_whitespace': getattr(self, 'trim_whitespace_cb', None) and self.trim_whitespace_cb.isChecked(),
                'convert_data_types': getattr(self, 'convert_types_cb', None) and self.convert_types_cb.isChecked(),
            })

    def _preview_processing(self):
        """预览处理效果"""
        try:
            if hasattr(self, 'status_label'):
                self.status_label.setText("🔍 正在预览数据处理效果...")
                self.status_label.setStyleSheet("color: blue; font-weight: bold;")

            # 更新配置
            self._update_processing_config()

            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(
                self, "预览处理效果",
                f"数据处理配置预览：\n\n"
                f"• 去除重复数据: {'是' if self.processing_config.get('remove_duplicates', False) else '否'}\n"
                f"• 缺失值处理: {self._get_missing_value_text()}\n"
                f"• 数据验证: {'启用' if self.processing_config.get('data_validation', True) else '禁用'}\n"
                f"• 格式化数字: {'是' if self.processing_config.get('format_numbers', True) else '否'}\n"
                f"• 格式化日期: {'是' if self.processing_config.get('format_dates', True) else '否'}\n"
                f"• 去除空白: {'是' if self.processing_config.get('trim_whitespace', True) else '否'}\n"
                f"• 类型转换: {'是' if self.processing_config.get('convert_data_types', True) else '否'}"
            )

            if hasattr(self, 'status_label'):
                self.status_label.setText("✅ 预览完成")
                self.status_label.setStyleSheet("color: green; font-weight: bold;")

        except Exception as e:
            if hasattr(self, 'status_label'):
                self.status_label.setText(f"❌ 预览失败: {str(e)}")
                self.status_label.setStyleSheet("color: red; font-weight: bold;")
            if hasattr(self, 'logger'):
                self.logger.error(f"预览处理效果失败: {e}")

    def _get_missing_value_text(self):
        """获取缺失值处理方式的中文描述"""
        mapping = {
            'keep': '保留空值',
            'remove': '删除包含空值的行',
            'fill_zero': '用0填充',
            'fill_mean': '用平均值填充'
        }
        return mapping.get(self.processing_config.get('handle_missing_values', 'keep'), '保留空值')

    def _reset_processing_config(self):
        """重置数据处理配置"""
        from PyQt5.QtWidgets import QMessageBox
        reply = QMessageBox.question(
            self, "确认重置",
            "确定要重置所有数据处理配置到默认值吗？",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 重置到默认配置
            if hasattr(self, 'remove_duplicates_cb'):
                self.remove_duplicates_cb.setChecked(False)
            if hasattr(self, 'missing_values_combo'):
                self.missing_values_combo.setCurrentIndex(0)  # 保留空值
            if hasattr(self, 'trim_whitespace_cb'):
                self.trim_whitespace_cb.setChecked(True)
            if hasattr(self, 'format_numbers_cb'):
                self.format_numbers_cb.setChecked(True)
            if hasattr(self, 'format_dates_cb'):
                self.format_dates_cb.setChecked(True)
            if hasattr(self, 'convert_types_cb'):
                self.convert_types_cb.setChecked(True)
            if hasattr(self, 'data_validation_cb'):
                self.data_validation_cb.setChecked(True)

            # 清空自定义规则
            if hasattr(self, 'rules_list'):
                self.rules_list.clear()

            if hasattr(self, 'status_label'):
                self.status_label.setText("✅ 配置已重置到默认值")
                self.status_label.setStyleSheet("color: green; font-weight: bold;")

    def _save_processing_config(self):
        """保存数据处理配置"""
        from PyQt5.QtWidgets import QInputDialog, QMessageBox

        name, ok = QInputDialog.getText(
            self, "保存配置", "请输入配置模板名称:"
        )

        if ok and name:
            try:
                # 实现配置保存逻辑
                template_config = self._gather_current_config()
                template_data = {
                    'name': name,
                    'config': template_config,
                    'created_time': str(datetime.now()),
                    'version': '1.0'
                }
                
                # 保存到配置文件中的saved_templates列表
                if 'data_processing' not in self.config:
                    self.config['data_processing'] = {}
                if 'saved_templates' not in self.config['data_processing']:
                    self.config['data_processing']['saved_templates'] = []
                
                self.config['data_processing']['saved_templates'].append(template_data)
                
                # 保存配置文件
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(self.config, f, ensure_ascii=False, indent=2)
                
                if hasattr(self, 'status_label'):
                    self.status_label.setText(f"✅ 配置已保存为模板: {name}")
                    self.status_label.setStyleSheet("color: green; font-weight: bold;")
                
                self.logger.info(f"配置模板保存成功: {name}")

                QMessageBox.information(
                    self, "保存成功",
                    f"数据处理配置已保存为模板: {name}"
                )

            except Exception as e:
                if hasattr(self, 'status_label'):
                    self.status_label.setText(f"❌ 保存失败: {str(e)}")
                    self.status_label.setStyleSheet("color: red; font-weight: bold;")
                if hasattr(self, 'logger'):
                    self.logger.error(f"保存配置失败: {e}")

    def _load_processing_config(self):
        """加载数据处理配置"""
        from PyQt5.QtWidgets import QInputDialog, QMessageBox

        # 获取已保存的模板列表
        saved_templates = self.config.get('data_processing', {}).get('saved_templates', [])
        templates = ["默认配置", "工资表标准配置", "异动表标准配置"]
        
        # 添加用户保存的模板
        for template in saved_templates:
            templates.append(template.get('name', '未命名模板'))

        template, ok = QInputDialog.getItem(
            self, "加载配置", "选择要加载的配置模板:", templates, 0, False
        )

        if ok and template:
            try:
                # 实现配置加载逻辑
                saved_templates = self.config.get('data_processing', {}).get('saved_templates', [])
                
                # 查找匹配的模板
                template_data = None
                for saved_template in saved_templates:
                    if saved_template.get('name') == template:
                        template_data = saved_template
                        break
                
                if template_data:
                    # 应用模板配置到UI
                    template_config = template_data.get('config', {})
                    self._apply_template_config_to_ui(template_config)
                    
                    if hasattr(self, 'status_label'):
                        self.status_label.setText(f"✅ 已加载配置模板: {template}")
                        self.status_label.setStyleSheet("color: green; font-weight: bold;")
                    
                    self.logger.info(f"配置模板加载成功: {template}")
                else:
                    if hasattr(self, 'status_label'):
                        self.status_label.setText(f"❌ 未找到模板: {template}")
                        self.status_label.setStyleSheet("color: red; font-weight: bold;")
                    
                    self.logger.warning(f"配置模板未找到: {template}")

                QMessageBox.information(
                    self, "加载成功",
                    f"已加载配置模板: {template}"
                )

            except Exception as e:
                if hasattr(self, 'status_label'):
                    self.status_label.setText(f"❌ 加载失败: {str(e)}")
                    self.status_label.setStyleSheet("color: red; font-weight: bold;")
                if hasattr(self, 'logger'):
                    self.logger.error(f"加载配置失败: {e}")
    
    def _gather_current_config(self) -> dict:
        """收集当前的UI配置"""
        config = {}
        
        # 收集数据处理配置
        if hasattr(self, 'remove_duplicates_cb'):
            config['remove_duplicates'] = self.remove_duplicates_cb.isChecked()
        if hasattr(self, 'missing_values_combo'):
            config['handle_missing_values'] = self.missing_values_combo.currentData() or 'keep'
        if hasattr(self, 'data_validation_cb'):
            config['data_validation'] = self.data_validation_cb.isChecked()
        if hasattr(self, 'format_numbers_cb'):
            config['format_numbers'] = self.format_numbers_cb.isChecked()
        if hasattr(self, 'format_dates_cb'):
            config['format_dates'] = self.format_dates_cb.isChecked()
        if hasattr(self, 'trim_whitespace_cb'):
            config['trim_whitespace'] = self.trim_whitespace_cb.isChecked()
        if hasattr(self, 'convert_types_cb'):
            config['convert_data_types'] = self.convert_types_cb.isChecked()
        
        # 收集性能配置
        if hasattr(self, 'batch_size_spin'):
            config['batch_size'] = self.batch_size_spin.value()
        if hasattr(self, 'error_tolerance_spin'):
            config['error_tolerance'] = self.error_tolerance_spin.value()
        
        return config
    
    def _apply_template_config_to_ui(self, template_config: dict):
        """将模板配置应用到UI"""
        try:
            # 应用数据处理配置
            if hasattr(self, 'remove_duplicates_cb') and 'remove_duplicates' in template_config:
                self.remove_duplicates_cb.setChecked(template_config['remove_duplicates'])
            
            if hasattr(self, 'missing_values_combo') and 'handle_missing_values' in template_config:
                missing_value_strategy = template_config['handle_missing_values']
                combo = self.missing_values_combo
                for i in range(combo.count()):
                    if combo.itemData(i) == missing_value_strategy:
                        combo.setCurrentIndex(i)
                        break
            
            if hasattr(self, 'data_validation_cb') and 'data_validation' in template_config:
                self.data_validation_cb.setChecked(template_config['data_validation'])
            
            if hasattr(self, 'format_numbers_cb') and 'format_numbers' in template_config:
                self.format_numbers_cb.setChecked(template_config['format_numbers'])
            
            if hasattr(self, 'format_dates_cb') and 'format_dates' in template_config:
                self.format_dates_cb.setChecked(template_config['format_dates'])
            
            if hasattr(self, 'trim_whitespace_cb') and 'trim_whitespace' in template_config:
                self.trim_whitespace_cb.setChecked(template_config['trim_whitespace'])
            
            if hasattr(self, 'convert_types_cb') and 'convert_data_types' in template_config:
                self.convert_types_cb.setChecked(template_config['convert_data_types'])
            
            # 应用性能配置
            if hasattr(self, 'batch_size_spin') and 'batch_size' in template_config:
                self.batch_size_spin.setValue(template_config['batch_size'])
            
            if hasattr(self, 'error_tolerance_spin') and 'error_tolerance' in template_config:
                self.error_tolerance_spin.setValue(template_config['error_tolerance'])
            
            # 更新内部配置
            self._update_processing_config()
            
            self.logger.info("模板配置应用成功")
            
        except Exception as e:
            self.logger.error(f"应用模板配置失败: {e}")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 自动保存当前配置
            self._update_config_from_ui()
            self.config_manager.set_config("advanced_settings", self.config, level=ConfigLevel.USER, create_version=False)
            self.logger.info("高级配置已自动保存（AdvancedConfigManager）")
            
        except Exception as e:
            self.logger.error(f"自动保存配置失败: {e}")
        
        event.accept()
    
    def _gather_current_config(self) -> dict:
        """收集当前的UI配置"""
        config = {}
        
        # 收集数据处理配置
        if hasattr(self, 'remove_duplicates_cb'):
            config['remove_duplicates'] = self.remove_duplicates_cb.isChecked()
        if hasattr(self, 'missing_values_combo'):
            config['handle_missing_values'] = self.missing_values_combo.currentData() or 'keep'
        if hasattr(self, 'data_validation_cb'):
            config['data_validation'] = self.data_validation_cb.isChecked()
        if hasattr(self, 'format_numbers_cb'):
            config['format_numbers'] = self.format_numbers_cb.isChecked()
        if hasattr(self, 'format_dates_cb'):
            config['format_dates'] = self.format_dates_cb.isChecked()
        if hasattr(self, 'trim_whitespace_cb'):
            config['trim_whitespace'] = self.trim_whitespace_cb.isChecked()
        if hasattr(self, 'convert_types_cb'):
            config['convert_data_types'] = self.convert_types_cb.isChecked()
        
        # 收集性能配置
        if hasattr(self, 'batch_size_spin'):
            config['batch_size'] = self.batch_size_spin.value()
        if hasattr(self, 'error_tolerance_spin'):
            config['error_tolerance'] = self.error_tolerance_spin.value()
        
        return config
    
    def _apply_template_config_to_ui(self, template_config: dict):
        """将模板配置应用到UI"""
        try:
            # 应用数据处理配置
            if hasattr(self, 'remove_duplicates_cb') and 'remove_duplicates' in template_config:
                self.remove_duplicates_cb.setChecked(template_config['remove_duplicates'])
            
            if hasattr(self, 'missing_values_combo') and 'handle_missing_values' in template_config:
                missing_value_strategy = template_config['handle_missing_values']
                combo = self.missing_values_combo
                for i in range(combo.count()):
                    if combo.itemData(i) == missing_value_strategy:
                        combo.setCurrentIndex(i)
                        break
            
            if hasattr(self, 'data_validation_cb') and 'data_validation' in template_config:
                self.data_validation_cb.setChecked(template_config['data_validation'])
            
            if hasattr(self, 'format_numbers_cb') and 'format_numbers' in template_config:
                self.format_numbers_cb.setChecked(template_config['format_numbers'])
            
            if hasattr(self, 'format_dates_cb') and 'format_dates' in template_config:
                self.format_dates_cb.setChecked(template_config['format_dates'])
            
            if hasattr(self, 'trim_whitespace_cb') and 'trim_whitespace' in template_config:
                self.trim_whitespace_cb.setChecked(template_config['trim_whitespace'])
            
            if hasattr(self, 'convert_types_cb') and 'convert_data_types' in template_config:
                self.convert_types_cb.setChecked(template_config['convert_data_types'])
            
            # 应用性能配置
            if hasattr(self, 'batch_size_spin') and 'batch_size' in template_config:
                self.batch_size_spin.setValue(template_config['batch_size'])
            
            if hasattr(self, 'error_tolerance_spin') and 'error_tolerance' in template_config:
                self.error_tolerance_spin.setValue(template_config['error_tolerance'])
            
            # 更新内部配置
            self._update_processing_config()
            
            self.logger.info("模板配置应用成功")
            
        except Exception as e:
            self.logger.error(f"应用模板配置失败: {e}")

    def _add_custom_rule(self):
        """添加自定义规则"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLineEdit, QPushButton, QTextEdit, QLabel

        dialog = QDialog(self)
        dialog.setWindowTitle("添加自定义处理规则")
        dialog.setMinimumSize(400, 300)

        layout = QVBoxLayout(dialog)

        # 规则名称
        layout.addWidget(QLabel("规则名称:"))
        name_edit = QLineEdit()
        layout.addWidget(name_edit)

        # 规则描述
        layout.addWidget(QLabel("规则描述:"))
        desc_edit = QTextEdit()
        desc_edit.setMaximumHeight(100)
        layout.addWidget(desc_edit)

        # 按钮
        button_layout = QHBoxLayout()
        ok_btn = QPushButton("确定")
        cancel_btn = QPushButton("取消")

        ok_btn.clicked.connect(dialog.accept)
        cancel_btn.clicked.connect(dialog.reject)

        button_layout.addWidget(ok_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)

        if dialog.exec_() == QDialog.Accepted:
            rule_name = name_edit.text().strip()
            rule_desc = desc_edit.toPlainText().strip()

            if rule_name and hasattr(self, 'rules_list'):
                self.rules_list.addItem(f"{rule_name}: {rule_desc}")
                if hasattr(self, 'status_label'):
                    self.status_label.setText(f"✅ 已添加自定义规则: {rule_name}")
                    self.status_label.setStyleSheet("color: green; font-weight: bold;")

    def _edit_custom_rule(self):
        """编辑自定义规则"""
        if hasattr(self, 'rules_list'):
            current_item = self.rules_list.currentItem()
            if current_item:
                # TODO: 实现规则编辑逻辑
                if hasattr(self, 'status_label'):
                    self.status_label.setText("⚠️ 规则编辑功能待完善")
                    self.status_label.setStyleSheet("color: orange; font-weight: bold;")
            else:
                if hasattr(self, 'status_label'):
                    self.status_label.setText("⚠️ 请先选择要编辑的规则")
                    self.status_label.setStyleSheet("color: orange; font-weight: bold;")

    def _remove_custom_rule(self):
        """删除自定义规则"""
        if hasattr(self, 'rules_list'):
            current_row = self.rules_list.currentRow()
            if current_row >= 0:
                from PyQt5.QtWidgets import QMessageBox
                reply = QMessageBox.question(
                    self, "确认删除",
                    "确定要删除选中的自定义规则吗？",
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    self.rules_list.takeItem(current_row)
                    if hasattr(self, 'status_label'):
                        self.status_label.setText("✅ 自定义规则已删除")
                        self.status_label.setStyleSheet("color: green; font-weight: bold;")
            else:
                if hasattr(self, 'status_label'):
                    self.status_label.setText("⚠️ 请先选择要删除的规则")
                    self.status_label.setStyleSheet("color: orange; font-weight: bold;")

    def get_processing_config(self):
        """获取当前数据处理配置"""
        if hasattr(self, 'processing_config'):
            self._update_processing_config()
            return self.processing_config.copy()
        return {}


# 测试用主函数
if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    dialog = AdvancedConfigDialog()
    dialog.show()
    
    sys.exit(app.exec_())
