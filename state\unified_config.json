{"version": "3.0", "global_settings": {"auto_save": true, "sync_enabled": true, "validation_strict": false, "preserve_chinese_headers": true, "auto_generate_mappings": true, "enable_smart_suggestions": true, "save_edit_history": true, "max_history_items": 100}, "field_definitions": {"global": {"employee_id": {"display_name": "工号", "field_type": "employee_id_string", "data_type": "string", "is_required": true, "validation_pattern": "^\\d{4,10}$"}, "employee_name": {"display_name": "姓名", "field_type": "name_string", "data_type": "string", "is_required": true}, "department": {"display_name": "部门名称", "field_type": "text_string", "data_type": "string", "is_required": false}, "employee_type": {"display_name": "人员类别", "field_type": "text_string", "data_type": "string", "is_required": false}, "position_salary_2025": {"display_name": "2025年岗位工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "format_rules": {"decimal_places": 2, "thousands_separator": true, "trim": true}}, "grade_salary_2025": {"display_name": "2025年薪级工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "format_rules": {"decimal_places": 2, "thousands_separator": true, "trim": true}}, "total_salary": {"display_name": "应发工资", "field_type": "salary_float", "data_type": "float", "is_required": true, "format_rules": {"decimal_places": 2, "thousands_separator": true, "trim": true}}, "year": {"display_name": "年份", "field_type": "year_string", "data_type": "string", "is_required": false}, "month": {"display_name": "月份", "field_type": "month_string", "data_type": "string", "is_required": false}, "created_at": {"display_name": "创建时间", "field_type": "date", "data_type": "datetime", "is_required": false}, "updated_at": {"display_name": "更新时间", "field_type": "date", "data_type": "datetime", "is_required": false}}}, "table_configs": {"salary_data_*": {"inherit_from": "global", "table_type": "salary"}, "change_data_*": {"inherit_from": "global", "table_type": "change"}, "mapping_config_4ee9c648_离休人员工资表": {"inherit_from": "global", "specific_fields": {"employee_id": {"display_name": "工号", "field_type": "string", "data_type": "string", "is_required": false}, "employee_name": {"display_name": "姓名", "field_type": "string", "data_type": "string", "is_required": false}, "department": {"display_name": "部门名称", "field_type": "string", "data_type": "string", "is_required": false}, "employee_type": {"display_name": "人员类别", "field_type": "string", "data_type": "string", "is_required": false}, "position_salary_2025": {"display_name": "2025年岗位工资", "field_type": "float", "data_type": "float", "is_required": false}, "grade_salary_2025": {"display_name": "2025年薪级工资", "field_type": "float", "data_type": "float", "is_required": false}, "total_salary": {"display_name": "应发工资", "field_type": "float", "data_type": "float", "is_required": false}, "year": {"display_name": "年份", "field_type": "string", "data_type": "string", "is_required": false}, "month": {"display_name": "月份", "field_type": "string", "data_type": "string", "is_required": false}, "created_at": {"display_name": "创建时间", "field_type": "datetime", "data_type": "datetime", "is_required": false}, "updated_at": {"display_name": "更新时间", "field_type": "datetime", "data_type": "datetime", "is_required": false}, "基本\n离休费": {"display_name": "基本\n离休费", "field_type": "salary_float", "data_type": "salary_float", "is_required": false}, "合计": {"display_name": "合计", "field_type": "salary_float", "data_type": "salary_float", "is_required": false}, "护理费": {"display_name": "护理费", "field_type": "salary_float", "data_type": "salary_float", "is_required": false}, "补发": {"display_name": "补发", "field_type": "salary_float", "data_type": "salary_float", "is_required": false}, "借支": {"display_name": "借支", "field_type": "salary_float", "data_type": "salary_float", "is_required": false}}}, "salary_data_2025_05_retired_employees": {"inherit_from": "global", "specific_fields": {"sequence_number": {"display_name": "序号", "field_type": "string", "data_type": "string", "is_required": false}, "employee_id": {"display_name": "人员代码", "field_type": "string", "data_type": "string", "is_required": false}, "employee_name": {"display_name": "姓名", "field_type": "string", "data_type": "string", "is_required": false}, "department": {"display_name": "部门名称", "field_type": "string", "data_type": "string", "is_required": false}, "basic_retirement_salary": {"display_name": "基本离休费", "field_type": "string", "data_type": "string", "is_required": false}, "balance_allowance": {"display_name": "结余津贴", "field_type": "string", "data_type": "string", "is_required": false}, "living_allowance": {"display_name": "生活补贴", "field_type": "string", "data_type": "string", "is_required": false}, "housing_allowance": {"display_name": "住房补贴", "field_type": "string", "data_type": "string", "is_required": false}, "property_allowance": {"display_name": "物业补贴", "field_type": "string", "data_type": "string", "is_required": false}, "retirement_allowance": {"display_name": "离休补贴", "field_type": "string", "data_type": "string", "is_required": false}, "nursing_fee": {"display_name": "护理费", "field_type": "string", "data_type": "string", "is_required": false}, "one_time_living_allowance": {"display_name": "增发一次性生活补贴", "field_type": "string", "data_type": "string", "is_required": false}, "supplement": {"display_name": "补发", "field_type": "string", "data_type": "string", "is_required": false}, "total": {"display_name": "合计", "field_type": "string", "data_type": "string", "is_required": false}, "advance": {"display_name": "借支", "field_type": "string", "data_type": "string", "is_required": false}, "remarks": {"display_name": "备注", "field_type": "string", "data_type": "string", "is_required": false}, "id": {"display_name": "自增主键", "field_type": "string", "data_type": "string", "is_required": false}, "month": {"display_name": "月份", "field_type": "string", "data_type": "string", "is_required": false}, "year": {"display_name": "年份", "field_type": "string", "data_type": "string", "is_required": false}, "created_at": {"display_name": "创建时间", "field_type": "string", "data_type": "string", "is_required": false}, "updated_at": {"display_name": "更新时间", "field_type": "string", "data_type": "string", "is_required": false}, "field_mappings": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "field_types": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "field_descriptions": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "data_types": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "is_required": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "metadata": {"display_name": {"created_at": "2025-09-16T14:30:22.802773", "updated_at": "2025-09-16T14:30:22.802773", "version": "1.0"}, "field_type": "text_string", "data_type": "text_string", "is_required": false}}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-09-16T14:30:22.802773", "sheet_name": "离休人员工资表", "has_chinese_headers": true}}, "salary_data_2025_05_pension_employees": {"inherit_from": "global", "specific_fields": {"sequence_number": {"display_name": "序号", "field_type": "string", "data_type": "string", "is_required": false}, "employee_id": {"display_name": "人员代码", "field_type": "string", "data_type": "string", "is_required": false}, "employee_name": {"display_name": "姓名", "field_type": "string", "data_type": "string", "is_required": false}, "department": {"display_name": "部门名称", "field_type": "string", "data_type": "string", "is_required": false}, "employee_type_code": {"display_name": "人员类别代码", "field_type": "string", "data_type": "string", "is_required": false}, "basic_retirement_salary": {"display_name": "基本退休费", "field_type": "string", "data_type": "string", "is_required": false}, "allowance": {"display_name": "津贴", "field_type": "string", "data_type": "string", "is_required": false}, "balance_allowance": {"display_name": "结余津贴", "field_type": "string", "data_type": "string", "is_required": false}, "retirement_living_allowance": {"display_name": "离退休生活补贴", "field_type": "string", "data_type": "string", "is_required": false}, "nursing_fee": {"display_name": "护理费", "field_type": "string", "data_type": "string", "is_required": false}, "property_allowance": {"display_name": "物业补贴", "field_type": "string", "data_type": "string", "is_required": false}, "housing_allowance": {"display_name": "住房补贴", "field_type": "string", "data_type": "string", "is_required": false}, "salary_advance": {"display_name": "增资预付", "field_type": "string", "data_type": "string", "is_required": false}, "adjustment_2016": {"display_name": "2016待遇调整", "field_type": "string", "data_type": "string", "is_required": false}, "adjustment_2017": {"display_name": "2017待遇调整", "field_type": "string", "data_type": "string", "is_required": false}, "adjustment_2018": {"display_name": "2018待遇调整", "field_type": "string", "data_type": "string", "is_required": false}, "adjustment_2019": {"display_name": "2019待遇调整", "field_type": "string", "data_type": "string", "is_required": false}, "adjustment_2020": {"display_name": "2020待遇调整", "field_type": "string", "data_type": "string", "is_required": false}, "adjustment_2021": {"display_name": "2021待遇调整", "field_type": "string", "data_type": "string", "is_required": false}, "adjustment_2022": {"display_name": "2022待遇调整", "field_type": "string", "data_type": "string", "is_required": false}, "adjustment_2023": {"display_name": "2023待遇调整", "field_type": "string", "data_type": "string", "is_required": false}, "supplement": {"display_name": "补发", "field_type": "string", "data_type": "string", "is_required": false}, "advance": {"display_name": "借支", "field_type": "string", "data_type": "string", "is_required": false}, "total_salary": {"display_name": "应发工资", "field_type": "string", "data_type": "string", "is_required": false}, "provident_fund": {"display_name": "公积", "field_type": "string", "data_type": "string", "is_required": false}, "insurance_deduction": {"display_name": "保险扣款", "field_type": "string", "data_type": "string", "is_required": false}, "remarks": {"display_name": "备注", "field_type": "string", "data_type": "string", "is_required": false}, "id": {"display_name": "自增主键", "field_type": "string", "data_type": "string", "is_required": false}, "month": {"display_name": "月份", "field_type": "string", "data_type": "string", "is_required": false}, "year": {"display_name": "年份", "field_type": "string", "data_type": "string", "is_required": false}, "created_at": {"display_name": "创建时间", "field_type": "string", "data_type": "string", "is_required": false}, "updated_at": {"display_name": "更新时间", "field_type": "string", "data_type": "string", "is_required": false}, "field_mappings": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "field_types": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "field_descriptions": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "data_types": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "is_required": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "metadata": {"display_name": {"created_at": "2025-09-16T14:30:23.099092", "updated_at": "2025-09-16T14:30:23.099092", "version": "1.0"}, "field_type": "text_string", "data_type": "text_string", "is_required": false}}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-09-16T14:30:23.099092", "sheet_name": "退休人员工资表", "has_chinese_headers": true}}, "salary_data_2025_05_active_employees": {"inherit_from": "global", "specific_fields": {"sequence_number": {"display_name": "序号", "field_type": "string", "data_type": "string", "is_required": false}, "employee_id": {"display_name": "工号", "field_type": "string", "data_type": "string", "is_required": false}, "employee_name": {"display_name": "姓名", "field_type": "string", "data_type": "string", "is_required": false}, "department": {"display_name": "部门名称", "field_type": "string", "data_type": "string", "is_required": false}, "employee_type_code": {"display_name": "人员类别代码", "field_type": "string", "data_type": "string", "is_required": false}, "employee_type": {"display_name": "人员类别", "field_type": "string", "data_type": "string", "is_required": false}, "position_salary_2025": {"display_name": "2025年岗位工资", "field_type": "string", "data_type": "string", "is_required": false}, "grade_salary_2025": {"display_name": "2025年薪级工资", "field_type": "string", "data_type": "string", "is_required": false}, "allowance": {"display_name": "津贴", "field_type": "string", "data_type": "string", "is_required": false}, "balance_allowance": {"display_name": "结余津贴", "field_type": "string", "data_type": "string", "is_required": false}, "basic_performance_2025": {"display_name": "2025年基础性绩效", "field_type": "string", "data_type": "string", "is_required": false}, "health_fee": {"display_name": "卫生费", "field_type": "string", "data_type": "string", "is_required": false}, "transport_allowance": {"display_name": "交通补贴", "field_type": "string", "data_type": "string", "is_required": false}, "property_allowance": {"display_name": "物业补贴", "field_type": "string", "data_type": "string", "is_required": false}, "housing_allowance": {"display_name": "住房补贴", "field_type": "string", "data_type": "string", "is_required": false}, "car_allowance": {"display_name": "车补", "field_type": "string", "data_type": "string", "is_required": false}, "communication_allowance": {"display_name": "通讯补贴", "field_type": "string", "data_type": "string", "is_required": false}, "performance_bonus_2025": {"display_name": "2025年奖励性绩效预发", "field_type": "string", "data_type": "string", "is_required": false}, "supplement": {"display_name": "补发", "field_type": "string", "data_type": "string", "is_required": false}, "advance": {"display_name": "借支", "field_type": "string", "data_type": "string", "is_required": false}, "total_salary": {"display_name": "应发工资", "field_type": "string", "data_type": "string", "is_required": false}, "provident_fund_2025": {"display_name": "2025公积金", "field_type": "string", "data_type": "string", "is_required": false}, "pension_insurance": {"display_name": "代扣代存养老保险", "field_type": "string", "data_type": "string", "is_required": false}, "id": {"display_name": "自增主键", "field_type": "string", "data_type": "string", "is_required": false}, "month": {"display_name": "月份", "field_type": "string", "data_type": "string", "is_required": false}, "year": {"display_name": "年份", "field_type": "string", "data_type": "string", "is_required": false}, "created_at": {"display_name": "创建时间", "field_type": "string", "data_type": "string", "is_required": false}, "updated_at": {"display_name": "更新时间", "field_type": "string", "data_type": "string", "is_required": false}, "field_mappings": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "field_types": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "field_descriptions": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "data_types": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "is_required": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "metadata": {"display_name": {"created_at": "2025-09-16T14:30:23.348793", "updated_at": "2025-09-16T14:30:23.348793", "version": "1.0"}, "field_type": "text_string", "data_type": "text_string", "is_required": false}}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-09-16T14:30:23.348793", "sheet_name": "全部在职人员工资表", "has_chinese_headers": true}}, "salary_data_2025_05_a_grade_employees": {"inherit_from": "global", "specific_fields": {"sequence_number": {"display_name": "序号", "field_type": "string", "data_type": "string", "is_required": false}, "employee_id": {"display_name": "工号", "field_type": "string", "data_type": "string", "is_required": false}, "employee_name": {"display_name": "姓名", "field_type": "string", "data_type": "string", "is_required": false}, "department": {"display_name": "部门名称", "field_type": "string", "data_type": "string", "is_required": false}, "employee_type": {"display_name": "人员类别", "field_type": "string", "data_type": "string", "is_required": false}, "employee_type_code": {"display_name": "人员类别代码", "field_type": "string", "data_type": "string", "is_required": false}, "position_salary_2025": {"display_name": "2025年岗位工资", "field_type": "string", "data_type": "string", "is_required": false}, "seniority_salary_2025": {"display_name": "2025年校龄工资", "field_type": "string", "data_type": "string", "is_required": false}, "allowance": {"display_name": "津贴", "field_type": "string", "data_type": "string", "is_required": false}, "balance_allowance": {"display_name": "结余津贴", "field_type": "string", "data_type": "string", "is_required": false}, "basic_performance_2025": {"display_name": "2025年基础性绩效", "field_type": "string", "data_type": "string", "is_required": false}, "health_fee": {"display_name": "卫生费", "field_type": "string", "data_type": "string", "is_required": false}, "living_allowance_2025": {"display_name": "2025年生活补贴", "field_type": "string", "data_type": "string", "is_required": false}, "car_allowance": {"display_name": "车补", "field_type": "string", "data_type": "string", "is_required": false}, "performance_bonus_2025": {"display_name": "2025年奖励性绩效预发", "field_type": "string", "data_type": "string", "is_required": false}, "supplement": {"display_name": "补发", "field_type": "string", "data_type": "string", "is_required": false}, "advance": {"display_name": "借支", "field_type": "string", "data_type": "string", "is_required": false}, "total_salary": {"display_name": "应发工资", "field_type": "string", "data_type": "string", "is_required": false}, "provident_fund_2025": {"display_name": "2025公积金", "field_type": "string", "data_type": "string", "is_required": false}, "insurance_deduction": {"display_name": "保险扣款", "field_type": "string", "data_type": "string", "is_required": false}, "pension_insurance": {"display_name": "代扣代存养老保险", "field_type": "string", "data_type": "string", "is_required": false}, "id": {"display_name": "自增主键", "field_type": "string", "data_type": "string", "is_required": false}, "month": {"display_name": "月份", "field_type": "string", "data_type": "string", "is_required": false}, "year": {"display_name": "年份", "field_type": "string", "data_type": "string", "is_required": false}, "created_at": {"display_name": "创建时间", "field_type": "string", "data_type": "string", "is_required": false}, "updated_at": {"display_name": "更新时间", "field_type": "string", "data_type": "string", "is_required": false}, "field_mappings": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "field_types": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "field_descriptions": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "data_types": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "is_required": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "metadata": {"display_name": {"created_at": "2025-09-16T14:30:23.615214", "updated_at": "2025-09-16T14:30:23.615214", "version": "1.0"}, "field_type": "text_string", "data_type": "text_string", "is_required": false}}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-09-16T14:30:23.615214", "sheet_name": "A岗职工", "has_chinese_headers": true}}, "mapping_config_4ee9c648_A岗职工": {"inherit_from": "global", "specific_fields": {"employee_id": {"display_name": "工号", "field_type": "string", "data_type": "string", "is_required": false}, "employee_name": {"display_name": "姓名", "field_type": "string", "data_type": "string", "is_required": false}, "department": {"display_name": "部门名称", "field_type": "string", "data_type": "string", "is_required": false}, "employee_type": {"display_name": "人员类别", "field_type": "string", "data_type": "string", "is_required": false}, "position_salary_2025": {"display_name": "2025年岗位工资", "field_type": "float", "data_type": "float", "is_required": false}, "grade_salary_2025": {"display_name": "2025年薪级工资", "field_type": "float", "data_type": "float", "is_required": false}, "total_salary": {"display_name": "应发工资", "field_type": "float", "data_type": "float", "is_required": false}, "year": {"display_name": "年份", "field_type": "string", "data_type": "string", "is_required": false}, "month": {"display_name": "月份", "field_type": "string", "data_type": "string", "is_required": false}, "created_at": {"display_name": "创建时间", "field_type": "datetime", "data_type": "datetime", "is_required": false}, "updated_at": {"display_name": "更新时间", "field_type": "datetime", "data_type": "datetime", "is_required": false}, "2025年奖励性绩效预发": {"display_name": "2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "salary_float", "is_required": false}, "卫生费": {"display_name": "卫生费", "field_type": "salary_float", "data_type": "salary_float", "is_required": false}, "2025公积金": {"display_name": "2025公积金", "field_type": "salary_float", "data_type": "salary_float", "is_required": false}, "保险扣款": {"display_name": "保险扣款", "field_type": "salary_float", "data_type": "salary_float", "is_required": false}, "代扣代存养老保险": {"display_name": "代扣代存养老保险", "field_type": "salary_float", "data_type": "salary_float", "is_required": false}, "2025年基础性绩效": {"display_name": "2025年基础性绩效", "field_type": "salary_float", "data_type": "salary_float", "is_required": false}, "补发": {"display_name": "补发", "field_type": "salary_float", "data_type": "salary_float", "is_required": false}, "借支": {"display_name": "借支", "field_type": "salary_float", "data_type": "salary_float", "is_required": false}}}, "change_data_2025_12_retired_employees": {"inherit_from": "global", "specific_fields": {"序号": {"display_name": "序号", "field_type": "string", "data_type": "string", "is_required": false}, "人员代码": {"display_name": "人员代码", "field_type": "string", "data_type": "string", "is_required": false}, "姓名": {"display_name": "姓名", "field_type": "string", "data_type": "string", "is_required": false}, "部门名称": {"display_name": "部门名称", "field_type": "string", "data_type": "string", "is_required": false}, "基本\n离休费": {"display_name": "基本\n离休费", "field_type": "string", "data_type": "string", "is_required": false}, "结余\n津贴": {"display_name": "结余\n津贴", "field_type": "string", "data_type": "string", "is_required": false}, "生活\n补贴": {"display_name": "生活\n补贴", "field_type": "string", "data_type": "string", "is_required": false}, "住房\n补贴": {"display_name": "住房\n补贴", "field_type": "string", "data_type": "string", "is_required": false}, "物业\n补贴": {"display_name": "物业\n补贴", "field_type": "string", "data_type": "string", "is_required": false}, "离休\n补贴": {"display_name": "离休\n补贴", "field_type": "string", "data_type": "string", "is_required": false}, "护理费": {"display_name": "护理费", "field_type": "string", "data_type": "string", "is_required": false}, "增发一次\n性生活补贴": {"display_name": "增发一次\n性生活补贴", "field_type": "string", "data_type": "string", "is_required": false}, "补发": {"display_name": "补发", "field_type": "string", "data_type": "string", "is_required": false}, "合计": {"display_name": "合计", "field_type": "string", "data_type": "string", "is_required": false}, "借支": {"display_name": "借支", "field_type": "string", "data_type": "string", "is_required": false}, "备注": {"display_name": "备注", "field_type": "string", "data_type": "string", "is_required": false}, "field_mappings": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "field_types": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "field_descriptions": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "data_types": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "is_required": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "metadata": {"display_name": {"created_at": "2025-09-16T14:30:51.969225", "updated_at": "2025-09-16T14:30:51.969225", "version": "1.0"}, "field_type": "text_string", "data_type": "text_string", "is_required": false}}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-09-16T14:30:51.969225", "sheet_name": "离休人员工资表", "has_chinese_headers": true}}, "change_data_2025_12_pension_employees": {"inherit_from": "global", "specific_fields": {"序号": {"display_name": "序号", "field_type": "string", "data_type": "string", "is_required": false}, "人员代码": {"display_name": "人员代码", "field_type": "string", "data_type": "string", "is_required": false}, "姓名": {"display_name": "姓名", "field_type": "string", "data_type": "string", "is_required": false}, "部门名称": {"display_name": "部门名称", "field_type": "string", "data_type": "string", "is_required": false}, "人员类别代码": {"display_name": "人员类别代码", "field_type": "string", "data_type": "string", "is_required": false}, "基本退休费": {"display_name": "基本退休费", "field_type": "string", "data_type": "string", "is_required": false}, "津贴": {"display_name": "津贴", "field_type": "string", "data_type": "string", "is_required": false}, "结余津贴": {"display_name": "结余津贴", "field_type": "string", "data_type": "string", "is_required": false}, "离退休生活补贴": {"display_name": "离退休生活补贴", "field_type": "string", "data_type": "string", "is_required": false}, "护理费": {"display_name": "护理费", "field_type": "string", "data_type": "string", "is_required": false}, "物业补贴": {"display_name": "物业补贴", "field_type": "string", "data_type": "string", "is_required": false}, "住房补贴": {"display_name": "住房补贴", "field_type": "string", "data_type": "string", "is_required": false}, "增资预付": {"display_name": "增资预付", "field_type": "string", "data_type": "string", "is_required": false}, "2016待遇调整": {"display_name": "2016待遇调整", "field_type": "string", "data_type": "string", "is_required": false}, "2017待遇调整": {"display_name": "2017待遇调整", "field_type": "string", "data_type": "string", "is_required": false}, "2018待遇调整": {"display_name": "2018待遇调整", "field_type": "string", "data_type": "string", "is_required": false}, "2019待遇调整": {"display_name": "2019待遇调整", "field_type": "string", "data_type": "string", "is_required": false}, "2020待遇调整": {"display_name": "2020待遇调整", "field_type": "string", "data_type": "string", "is_required": false}, "2021待遇调整": {"display_name": "2021待遇调整", "field_type": "string", "data_type": "string", "is_required": false}, "2022待遇调整": {"display_name": "2022待遇调整", "field_type": "string", "data_type": "string", "is_required": false}, "2023待遇调整": {"display_name": "2023待遇调整", "field_type": "string", "data_type": "string", "is_required": false}, "补发": {"display_name": "补发", "field_type": "string", "data_type": "string", "is_required": false}, "借支": {"display_name": "借支", "field_type": "string", "data_type": "string", "is_required": false}, "应发工资": {"display_name": "应发工资", "field_type": "string", "data_type": "string", "is_required": false}, "公积": {"display_name": "公积", "field_type": "string", "data_type": "string", "is_required": false}, "保险扣款": {"display_name": "保险扣款", "field_type": "string", "data_type": "string", "is_required": false}, "备注": {"display_name": "备注", "field_type": "string", "data_type": "string", "is_required": false}, "field_mappings": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "field_types": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "field_descriptions": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "data_types": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "is_required": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "metadata": {"display_name": {"created_at": "2025-09-16T14:30:52.141037", "updated_at": "2025-09-16T14:30:52.141037", "version": "1.0"}, "field_type": "text_string", "data_type": "text_string", "is_required": false}}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-09-16T14:30:52.141037", "sheet_name": "退休人员工资表", "has_chinese_headers": true}}, "change_data_2025_12_active_employees": {"inherit_from": "global", "specific_fields": {"序号": {"display_name": "序号", "field_type": "string", "data_type": "string", "is_required": false}, "工号": {"display_name": "工号", "field_type": "string", "data_type": "string", "is_required": false}, "姓名": {"display_name": "姓名", "field_type": "string", "data_type": "string", "is_required": false}, "部门名称": {"display_name": "部门名称", "field_type": "string", "data_type": "string", "is_required": false}, "人员类别代码": {"display_name": "人员类别代码", "field_type": "string", "data_type": "string", "is_required": false}, "人员类别": {"display_name": "人员类别", "field_type": "string", "data_type": "string", "is_required": false}, "2025年岗位工资": {"display_name": "2025年岗位工资", "field_type": "string", "data_type": "string", "is_required": false}, "2025年薪级工资": {"display_name": "2025年薪级工资", "field_type": "string", "data_type": "string", "is_required": false}, "津贴": {"display_name": "津贴", "field_type": "string", "data_type": "string", "is_required": false}, "结余津贴": {"display_name": "结余津贴", "field_type": "string", "data_type": "string", "is_required": false}, "2025年基础性绩效": {"display_name": "2025年基础性绩效", "field_type": "string", "data_type": "string", "is_required": false}, "卫生费": {"display_name": "卫生费", "field_type": "string", "data_type": "string", "is_required": false}, "交通补贴": {"display_name": "交通补贴", "field_type": "string", "data_type": "string", "is_required": false}, "物业补贴": {"display_name": "物业补贴", "field_type": "string", "data_type": "string", "is_required": false}, "住房补贴": {"display_name": "住房补贴", "field_type": "string", "data_type": "string", "is_required": false}, "车补": {"display_name": "车补", "field_type": "string", "data_type": "string", "is_required": false}, "通讯补贴": {"display_name": "通讯补贴", "field_type": "string", "data_type": "string", "is_required": false}, "2025年奖励性绩效预发": {"display_name": "2025年奖励性绩效预发", "field_type": "string", "data_type": "string", "is_required": false}, "补发": {"display_name": "补发", "field_type": "string", "data_type": "string", "is_required": false}, "借支": {"display_name": "借支", "field_type": "string", "data_type": "string", "is_required": false}, "应发工资": {"display_name": "应发工资", "field_type": "string", "data_type": "string", "is_required": false}, "2025公积金": {"display_name": "2025公积金", "field_type": "string", "data_type": "string", "is_required": false}, "代扣代存养老保险": {"display_name": "代扣代存养老保险", "field_type": "string", "data_type": "string", "is_required": false}, "field_mappings": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "field_types": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "field_descriptions": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "data_types": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "is_required": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "metadata": {"display_name": {"created_at": "2025-09-16T14:30:52.344197", "updated_at": "2025-09-16T14:30:52.344197", "version": "1.0"}, "field_type": "text_string", "data_type": "text_string", "is_required": false}}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-09-16T14:30:52.344197", "sheet_name": "全部在职人员工资表", "has_chinese_headers": true}}, "change_data_2025_12_a_grade_employees": {"inherit_from": "global", "specific_fields": {"序号": {"display_name": "序号", "field_type": "string", "data_type": "string", "is_required": false}, "工号": {"display_name": "工号", "field_type": "string", "data_type": "string", "is_required": false}, "姓名": {"display_name": "姓名", "field_type": "string", "data_type": "string", "is_required": false}, "部门名称": {"display_name": "部门名称", "field_type": "string", "data_type": "string", "is_required": false}, "人员类别": {"display_name": "人员类别", "field_type": "string", "data_type": "string", "is_required": false}, "人员类别代码": {"display_name": "人员类别代码", "field_type": "string", "data_type": "string", "is_required": false}, "2025年岗位工资": {"display_name": "2025年岗位工资", "field_type": "string", "data_type": "string", "is_required": false}, "2025年校龄工资": {"display_name": "2025年校龄工资", "field_type": "string", "data_type": "string", "is_required": false}, "津贴": {"display_name": "津贴", "field_type": "string", "data_type": "string", "is_required": false}, "结余津贴": {"display_name": "结余津贴", "field_type": "string", "data_type": "string", "is_required": false}, "2025年基础性绩效": {"display_name": "2025年基础性绩效", "field_type": "string", "data_type": "string", "is_required": false}, "卫生费": {"display_name": "卫生费", "field_type": "string", "data_type": "string", "is_required": false}, "2025年生活补贴": {"display_name": "2025年生活补贴", "field_type": "string", "data_type": "string", "is_required": false}, "车补": {"display_name": "车补", "field_type": "string", "data_type": "string", "is_required": false}, "2025年奖励性绩效预发": {"display_name": "2025年奖励性绩效预发", "field_type": "string", "data_type": "string", "is_required": false}, "补发": {"display_name": "补发", "field_type": "string", "data_type": "string", "is_required": false}, "借支": {"display_name": "借支", "field_type": "string", "data_type": "string", "is_required": false}, "应发工资": {"display_name": "应发工资", "field_type": "string", "data_type": "string", "is_required": false}, "2025公积金": {"display_name": "2025公积金", "field_type": "string", "data_type": "string", "is_required": false}, "保险扣款": {"display_name": "保险扣款", "field_type": "string", "data_type": "string", "is_required": false}, "代扣代存养老保险": {"display_name": "代扣代存养老保险", "field_type": "string", "data_type": "string", "is_required": false}, "field_mappings": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "field_types": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "field_descriptions": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "data_types": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "is_required": {"display_name": {}, "field_type": "text_string", "data_type": "text_string", "is_required": false}, "metadata": {"display_name": {"created_at": "2025-09-16T14:30:52.641436", "updated_at": "2025-09-16T14:30:52.641436", "version": "1.0"}, "field_type": "text_string", "data_type": "text_string", "is_required": false}}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-09-16T14:30:52.641436", "sheet_name": "A岗职工", "has_chinese_headers": true}}}, "templates": {"standard_salary": {"name": "标准工资表模板", "description": "适用于标准格式的工资表", "field_definitions": {"employee_id": {"display_name": "工号", "field_type": "employee_id_string", "data_type": "string", "is_required": true, "validation_pattern": "^\\d{4,10}$"}, "employee_name": {"display_name": "姓名", "field_type": "name_string", "data_type": "string", "is_required": true}, "department": {"display_name": "部门名称", "field_type": "text_string", "data_type": "string", "is_required": false}, "employee_type": {"display_name": "人员类别", "field_type": "text_string", "data_type": "string", "is_required": false}, "position_salary_2025": {"display_name": "2025年岗位工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "format_rules": {"decimal_places": 2, "thousands_separator": true, "trim": true}}, "grade_salary_2025": {"display_name": "2025年薪级工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "format_rules": {"decimal_places": 2, "thousands_separator": true, "trim": true}}, "total_salary": {"display_name": "应发工资", "field_type": "salary_float", "data_type": "float", "is_required": true, "format_rules": {"decimal_places": 2, "thousands_separator": true, "trim": true}}, "year": {"display_name": "年份", "field_type": "year_string", "data_type": "string", "is_required": false}, "month": {"display_name": "月份", "field_type": "month_string", "data_type": "string", "is_required": false}, "created_at": {"display_name": "创建时间", "field_type": "date", "data_type": "datetime", "is_required": false}, "updated_at": {"display_name": "更新时间", "field_type": "date", "data_type": "datetime", "is_required": false}}, "display_order": ["employee_id", "employee_name", "department", "position_salary_2025", "grade_salary_2025", "total_salary"]}, "standard_change": {"name": "标准异动表模板", "description": "适用于标准格式的异动表", "field_definitions": {"employee_id": {"display_name": "工号", "field_type": "employee_id_string", "data_type": "string", "is_required": true, "validation_pattern": "^\\d{4,10}$"}, "employee_name": {"display_name": "姓名", "field_type": "name_string", "data_type": "string", "is_required": true}, "department": {"display_name": "部门名称", "field_type": "text_string", "data_type": "string", "is_required": false}, "employee_type": {"display_name": "人员类别", "field_type": "text_string", "data_type": "string", "is_required": false}, "position_salary_2025": {"display_name": "2025年岗位工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "format_rules": {"decimal_places": 2, "thousands_separator": true, "trim": true}}, "grade_salary_2025": {"display_name": "2025年薪级工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "format_rules": {"decimal_places": 2, "thousands_separator": true, "trim": true}}, "total_salary": {"display_name": "应发工资", "field_type": "salary_float", "data_type": "float", "is_required": true, "format_rules": {"decimal_places": 2, "thousands_separator": true, "trim": true}}, "year": {"display_name": "年份", "field_type": "year_string", "data_type": "string", "is_required": false}, "month": {"display_name": "月份", "field_type": "month_string", "data_type": "string", "is_required": false}, "created_at": {"display_name": "创建时间", "field_type": "date", "data_type": "datetime", "is_required": false}, "updated_at": {"display_name": "更新时间", "field_type": "date", "data_type": "datetime", "is_required": false}, "change_type": {"display_name": "异动类型", "field_type": "text_string", "data_type": "string", "is_required": false}, "change_date": {"display_name": "异动日期", "field_type": "date", "data_type": "date", "is_required": false}}, "display_order": ["employee_id", "employee_name", "department", "change_type", "change_date", "total_salary"]}}, "last_updated": "2025-09-16T14:30:52.641436"}