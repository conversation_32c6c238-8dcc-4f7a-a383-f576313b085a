{"version": "2.0", "last_updated": "2025-09-16T14:31:19.801072", "global_settings": {"auto_generate_mappings": true, "enable_smart_suggestions": true, "save_edit_history": true, "preserve_chinese_headers": true}, "user_import_configs": {}, "table_mappings": {"salary_data_2025_05_all_active": {"field_mappings": {"employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "grade_salary_2025": "2025年薪级工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "transport_allowance": "交通补贴", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "car_allowance": "车补", "communication_allowance": "通讯补贴", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "pension_insurance": "代扣代存养老保险", "year": "年份", "month": "月份", "created_at": "创建时间", "updated_at": "更新时间", "id": "自增主键", "sequence": "序号", "sequence_number": "序号", "row_number": "行号"}, "field_types": {"employee_id": "string", "employee_name": "string", "department": "string", "employee_type": "string", "employee_type_code": "string", "position_salary_2025": "float", "grade_salary_2025": "float", "allowance": "float", "balance_allowance": "float", "basic_performance_2025": "float", "performance_bonus_2025": "float", "total_salary": "float", "health_fee": "float", "transport_allowance": "float", "property_allowance": "float", "housing_allowance": "float", "car_allowance": "float", "communication_allowance": "float", "supplement": "float", "advance": "float", "provident_fund_2025": "float", "pension_insurance": "float", "year": "year_string", "month": "month_string", "created_at": "date", "updated_at": "date", "id": "integer", "sequence": "integer", "sequence_number": "integer", "row_number": "integer"}, "hidden_fields": ["id", "created_at", "updated_at", "sequence_number", "row_number", "sequence"], "display_order": [], "metadata": {"created_at": "2025-09-16T14:30:25.208155", "table_type": "active_employees"}}, "salary_data_2025_05_retired": {"field_mappings": {"employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "grade_salary_2025": "2025年薪级工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "transport_allowance": "交通补贴", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "car_allowance": "车补", "communication_allowance": "通讯补贴", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "pension_insurance": "代扣代存养老保险", "year": "年份", "month": "月份", "created_at": "创建时间", "updated_at": "更新时间", "id": "自增主键", "sequence": "序号", "sequence_number": "序号", "row_number": "行号"}, "field_types": {"employee_id": "string", "employee_name": "string", "department": "string", "employee_type": "string", "employee_type_code": "string", "position_salary_2025": "float", "grade_salary_2025": "float", "allowance": "float", "balance_allowance": "float", "basic_performance_2025": "float", "performance_bonus_2025": "float", "total_salary": "float", "health_fee": "float", "transport_allowance": "float", "property_allowance": "float", "housing_allowance": "float", "car_allowance": "float", "communication_allowance": "float", "supplement": "float", "advance": "float", "provident_fund_2025": "float", "pension_insurance": "float", "year": "year_string", "month": "month_string", "created_at": "date", "updated_at": "date", "id": "integer", "sequence": "integer", "sequence_number": "integer", "row_number": "integer"}, "hidden_fields": ["id", "created_at", "updated_at", "sequence_number", "row_number", "sequence"], "display_order": [], "metadata": {"created_at": "2025-09-16T14:30:25.910204", "table_type": "retired_employees"}}, "change_data_2025_12_retired": {"field_mappings": {"employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "grade_salary_2025": "2025年薪级工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "transport_allowance": "交通补贴", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "car_allowance": "车补", "communication_allowance": "通讯补贴", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "pension_insurance": "代扣代存养老保险", "year": "年份", "month": "月份", "created_at": "创建时间", "updated_at": "更新时间", "id": "自增主键", "sequence": "序号", "sequence_number": "序号", "row_number": "行号"}, "field_types": {"employee_id": "string", "employee_name": "string", "department": "string", "employee_type": "string", "employee_type_code": "string", "position_salary_2025": "float", "grade_salary_2025": "float", "allowance": "float", "balance_allowance": "float", "basic_performance_2025": "float", "performance_bonus_2025": "float", "total_salary": "float", "health_fee": "float", "transport_allowance": "float", "property_allowance": "float", "housing_allowance": "float", "car_allowance": "float", "communication_allowance": "float", "supplement": "float", "advance": "float", "provident_fund_2025": "float", "pension_insurance": "float", "year": "year_string", "month": "month_string", "created_at": "date", "updated_at": "date", "id": "integer", "sequence": "integer", "sequence_number": "integer", "row_number": "integer"}, "hidden_fields": ["id", "created_at", "updated_at", "sequence_number", "row_number", "sequence"], "display_order": [], "metadata": {"created_at": "2025-09-16T14:30:56.297740", "table_type": "change_table"}}, "change_data_2025_12_a_grade": {"field_mappings": {"employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "grade_salary_2025": "2025年薪级工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "transport_allowance": "交通补贴", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "car_allowance": "车补", "communication_allowance": "通讯补贴", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "pension_insurance": "代扣代存养老保险", "year": "年份", "month": "月份", "created_at": "创建时间", "updated_at": "更新时间", "id": "自增主键", "sequence": "序号", "sequence_number": "序号", "row_number": "行号"}, "field_types": {"employee_id": "string", "employee_name": "string", "department": "string", "employee_type": "string", "employee_type_code": "string", "position_salary_2025": "float", "grade_salary_2025": "float", "allowance": "float", "balance_allowance": "float", "basic_performance_2025": "float", "performance_bonus_2025": "float", "total_salary": "float", "health_fee": "float", "transport_allowance": "float", "property_allowance": "float", "housing_allowance": "float", "car_allowance": "float", "communication_allowance": "float", "supplement": "float", "advance": "float", "provident_fund_2025": "float", "pension_insurance": "float", "year": "year_string", "month": "month_string", "created_at": "date", "updated_at": "date", "id": "integer", "sequence": "integer", "sequence_number": "integer", "row_number": "integer"}, "hidden_fields": ["id", "created_at", "updated_at", "sequence_number", "row_number", "sequence"], "display_order": [], "metadata": {"created_at": "2025-09-16T14:30:56.581005", "table_type": "change_table"}}, "change_data_2025_12_all_active": {"field_mappings": {"employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "grade_salary_2025": "2025年薪级工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "transport_allowance": "交通补贴", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "car_allowance": "车补", "communication_allowance": "通讯补贴", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "pension_insurance": "代扣代存养老保险", "year": "年份", "month": "月份", "created_at": "创建时间", "updated_at": "更新时间", "id": "自增主键", "sequence": "序号", "sequence_number": "序号", "row_number": "行号"}, "field_types": {"employee_id": "string", "employee_name": "string", "department": "string", "employee_type": "string", "employee_type_code": "string", "position_salary_2025": "float", "grade_salary_2025": "float", "allowance": "float", "balance_allowance": "float", "basic_performance_2025": "float", "performance_bonus_2025": "float", "total_salary": "float", "health_fee": "float", "transport_allowance": "float", "property_allowance": "float", "housing_allowance": "float", "car_allowance": "float", "communication_allowance": "float", "supplement": "float", "advance": "float", "provident_fund_2025": "float", "pension_insurance": "float", "year": "year_string", "month": "month_string", "created_at": "date", "updated_at": "date", "id": "integer", "sequence": "integer", "sequence_number": "integer", "row_number": "integer"}, "hidden_fields": ["id", "created_at", "updated_at", "sequence_number", "row_number", "sequence"], "display_order": [], "metadata": {"created_at": "2025-09-16T14:31:02.537023", "table_type": "change_table"}}, "change_data_2025_12_pension": {"field_mappings": {"employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "grade_salary_2025": "2025年薪级工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "transport_allowance": "交通补贴", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "car_allowance": "车补", "communication_allowance": "通讯补贴", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "pension_insurance": "代扣代存养老保险", "year": "年份", "month": "月份", "created_at": "创建时间", "updated_at": "更新时间", "id": "自增主键", "sequence": "序号", "sequence_number": "序号", "row_number": "行号"}, "field_types": {"employee_id": "string", "employee_name": "string", "department": "string", "employee_type": "string", "employee_type_code": "string", "position_salary_2025": "float", "grade_salary_2025": "float", "allowance": "float", "balance_allowance": "float", "basic_performance_2025": "float", "performance_bonus_2025": "float", "total_salary": "float", "health_fee": "float", "transport_allowance": "float", "property_allowance": "float", "housing_allowance": "float", "car_allowance": "float", "communication_allowance": "float", "supplement": "float", "advance": "float", "provident_fund_2025": "float", "pension_insurance": "float", "year": "year_string", "month": "month_string", "created_at": "date", "updated_at": "date", "id": "integer", "sequence": "integer", "sequence_number": "integer", "row_number": "integer"}, "hidden_fields": ["id", "created_at", "updated_at", "sequence_number", "row_number", "sequence"], "display_order": [], "metadata": {"created_at": "2025-09-16T14:31:08.327748", "table_type": "change_table"}}, "salary_data_2025_05_pension": {"field_mappings": {"employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "grade_salary_2025": "2025年薪级工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "transport_allowance": "交通补贴", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "car_allowance": "车补", "communication_allowance": "通讯补贴", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "pension_insurance": "代扣代存养老保险", "year": "年份", "month": "月份", "created_at": "创建时间", "updated_at": "更新时间", "id": "自增主键", "sequence": "序号", "sequence_number": "序号", "row_number": "行号"}, "field_types": {"employee_id": "string", "employee_name": "string", "department": "string", "employee_type": "string", "employee_type_code": "string", "position_salary_2025": "float", "grade_salary_2025": "float", "allowance": "float", "balance_allowance": "float", "basic_performance_2025": "float", "performance_bonus_2025": "float", "total_salary": "float", "health_fee": "float", "transport_allowance": "float", "property_allowance": "float", "housing_allowance": "float", "car_allowance": "float", "communication_allowance": "float", "supplement": "float", "advance": "float", "provident_fund_2025": "float", "pension_insurance": "float", "year": "year_string", "month": "month_string", "created_at": "date", "updated_at": "date", "id": "integer", "sequence": "integer", "sequence_number": "integer", "row_number": "integer"}, "hidden_fields": ["id", "created_at", "updated_at", "sequence_number", "row_number", "sequence"], "display_order": [], "metadata": {"created_at": "2025-09-16T14:31:18.337831", "table_type": "pension_employees"}}, "salary_data_2025_05_a_grade": {"field_mappings": {"employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "grade_salary_2025": "2025年薪级工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "transport_allowance": "交通补贴", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "car_allowance": "车补", "communication_allowance": "通讯补贴", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "pension_insurance": "代扣代存养老保险", "year": "年份", "month": "月份", "created_at": "创建时间", "updated_at": "更新时间", "id": "自增主键", "sequence": "序号", "sequence_number": "序号", "row_number": "行号"}, "field_types": {"employee_id": "string", "employee_name": "string", "department": "string", "employee_type": "string", "employee_type_code": "string", "position_salary_2025": "float", "grade_salary_2025": "float", "allowance": "float", "balance_allowance": "float", "basic_performance_2025": "float", "performance_bonus_2025": "float", "total_salary": "float", "health_fee": "float", "transport_allowance": "float", "property_allowance": "float", "housing_allowance": "float", "car_allowance": "float", "communication_allowance": "float", "supplement": "float", "advance": "float", "provident_fund_2025": "float", "pension_insurance": "float", "year": "year_string", "month": "month_string", "created_at": "date", "updated_at": "date", "id": "integer", "sequence": "integer", "sequence_number": "integer", "row_number": "integer"}, "hidden_fields": ["id", "created_at", "updated_at", "sequence_number", "row_number", "sequence"], "display_order": [], "metadata": {"created_at": "2025-09-16T14:31:19.785380", "table_type": "unknown"}}}}