#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试高级配置对话框修复
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_advanced_config_dialog():
    """测试高级配置对话框是否能正常创建"""
    try:
        from PyQt5.QtWidgets import QApplication
        from src.gui.advanced_config_dialog import AdvancedConfigDialog
        
        print("🔧 开始测试高级配置对话框...")
        
        # 创建QApplication实例
        app = QApplication(sys.argv)
        
        print("✅ QApplication创建成功")
        
        # 尝试创建AdvancedConfigDialog实例
        dialog = AdvancedConfigDialog()
        
        print("✅ AdvancedConfigDialog创建成功")
        
        # 检查关键方法是否存在
        methods_to_check = [
            '_create_performance_tab',
            '_create_bottom_buttons',
            '_connect_signals',
            '_browse_import_path',
            '_on_performance_config_changed'
        ]
        
        for method_name in methods_to_check:
            if hasattr(dialog, method_name):
                print(f"✅ 方法 {method_name} 存在")
            else:
                print(f"❌ 方法 {method_name} 缺失")
        
        # 检查性能选项卡控件是否存在
        performance_widgets = [
            'max_memory_usage',
            'enable_caching', 
            'preload_data',
            'thread_count',
            'enable_async_processing',
            'progress_update_frequency'
        ]
        
        for widget_name in performance_widgets:
            if hasattr(dialog, widget_name):
                print(f"✅ 性能控件 {widget_name} 存在")
            else:
                print(f"❌ 性能控件 {widget_name} 缺失")
        
        # 检查底部按钮是否存在
        button_widgets = [
            'reset_btn',
            'import_btn',
            'export_btn',
            'cancel_btn',
            'save_close_btn'
        ]
        
        for button_name in button_widgets:
            if hasattr(dialog, button_name):
                print(f"✅ 底部按钮 {button_name} 存在")
            else:
                print(f"❌ 底部按钮 {button_name} 缺失")
        
        print("🎉 高级配置对话框测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_advanced_config_dialog()
    if success:
        print("\n✅ 修复验证成功！高级配置对话框可以正常创建。")
        sys.exit(0)
    else:
        print("\n❌ 修复验证失败！")
        sys.exit(1)
