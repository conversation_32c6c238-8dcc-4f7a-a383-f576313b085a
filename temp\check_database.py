#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的实际数据
"""

import sqlite3
import pandas as pd
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def check_database():
    """检查数据库中的实际数据"""
    try:
        # 连接数据库
        db_path = os.path.join(project_root, 'data', 'db', 'salary_system.db')
        conn = sqlite3.connect(db_path)
        
        # 查看所有表
        tables = pd.read_sql_query("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'salary_data%'", conn)
        print('数据库中的工资表:')
        print(tables)
        
        # 查看每个表的数据
        for table_name in tables['name']:
            print(f'\n=== {table_name} ===')
            # 查看表结构
            schema = pd.read_sql_query(f'PRAGMA table_info({table_name})', conn)
            print('表结构:')
            print(schema[['name', 'type', 'notnull']].to_string())
            
            # 查看前几行数据
            data = pd.read_sql_query(f'SELECT * FROM {table_name} LIMIT 3', conn)
            print(f'\n前3行数据:')
            if not data.empty:
                print(data.to_string())
            else:
                print('表为空')
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据库时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_database()
