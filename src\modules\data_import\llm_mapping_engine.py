"""
LLMMappingEngine - 与 SmartMappingEngine 并行的映射引擎（Phase 1 MVP）
- 接口与 SmartMappingEngine 对齐：generate_mapping / generate_smart_mapping
- 通过 LLMClient 获取建议；若失败则回退到 SmartMappingEngine
- 应用 NameNormalizer（匹配标准化）与 PostProcessor（结果约束）
- 可选 MappingCache
"""
from __future__ import annotations

from typing import Any, Dict, List, Optional

from src.modules.logging.setup_logger import setup_logger
from src.modules.llm.llm_client import LLMClient
from src.modules.data_import.mapping_cache import MappingCache
from src.modules.data_import.post_processor import PostProcessor
from src.modules.data_import.name_normalizer import NameNormalizer
from src.gui.core.smart_mapping_engine import (
    SmartMappingEngine,
    MappingResult,
    MappingConfidence,
)


class LLMMappingEngine:
    def __init__(self, *, config: Optional[Dict[str, Any]] = None):
        self.logger = setup_logger(__name__)
        self._config = config or {}
        self._confidence_threshold = 0.7

        # 组件
        self.normalizer = NameNormalizer()
        self.post = PostProcessor()
        self.cache = MappingCache()
        self.smart_fallback = SmartMappingEngine()

        # LLM 参数
        llm_cfg = (self._config or {}).get("llm", {})
        self.llm_client = LLMClient(
            provider=llm_cfg.get("provider", "anthropic"),
            api_base=llm_cfg.get("api_base", ""),
            model=llm_cfg.get("model", "glm-4.5"),
            timeout_seconds=llm_cfg.get("timeout_seconds", 15),
            max_retries=llm_cfg.get("max_retries", 1),
        )

    # 配置钩子，保持与 Smart 引擎一致
    def update_config(self, config: Dict[str, Any]):
        try:
            self._config = config or {}
            smart_cfg = (self._config or {}).get("smart_recommendations", {})
            thr = smart_cfg.get("confidence_threshold")
            if thr is not None:
                try:
                    val = float(thr)
                    if val > 1:
                        val = val / 100.0
                    self._confidence_threshold = max(0.0, min(1.0, val))
                except Exception:
                    pass
        except Exception:
            pass

    def set_confidence_threshold(self, threshold: float):
        try:
            val = float(threshold)
            if val > 1:
                val = val / 100.0
            self._confidence_threshold = max(0.0, min(1.0, val))
        except Exception:
            pass

    # 兼容接口：返回 {excel_field: {target_field, field_type, confidence, reason}}
    def generate_mapping(
        self,
        excel_headers: List[str],
        table_type: str,
        sample_data: Optional[Dict[str, List[Any]]] = None,
    ) -> Dict[str, Dict[str, Any]]:
        results = self.generate_smart_mapping(excel_headers, table_type, sample_data)

        # 将 MappingResult 转换为复杂映射字典
        try:
            complex_mapping: Dict[str, Dict[str, Any]] = {}
            for r in results:
                # 尝试使用 Smart 的推断逻辑获得 field_type
                try:
                    inferred = self.smart_fallback._infer_field_type_from_data_type_and_name(r.data_type, r.source_field)  # type: ignore[attr-defined]
                except Exception:
                    inferred = self.smart_fallback._infer_field_type_from_name(r.source_field)  # type: ignore[attr-defined]
                complex_mapping[r.source_field] = {
                    "target_field": r.target_field,
                    "field_type": inferred,
                    "confidence": r.confidence,
                    "reason": r.reasoning,
                }
            return complex_mapping
        except Exception as e:
            self.logger.warning(f"[LLMEngine] 结果转换失败，回退到Smart: {e}")
            return self.smart_fallback.generate_mapping(excel_headers, table_type, sample_data)

    # 完整接口：返回 List[MappingResult]
    def generate_smart_mapping(
        self,
        excel_headers: List[str],
        table_type: str,
        sample_data: Optional[Dict[str, List[Any]]] = None,
    ) -> List[MappingResult]:
        try:
            headers_norm = [self.normalizer.normalize_for_matching(h) for h in excel_headers]

            # 1) 缓存
            llm_cfg = (self._config or {}).get("llm", {})
            disable_cache = bool(llm_cfg.get("disable_cache", False))
            ttl_minutes = llm_cfg.get("cache_ttl_minutes", None)
            if not disable_cache:
                cached = self.cache.get(headers_norm, table_type, sample_data, ttl_minutes=ttl_minutes)
                if cached:
                    self.logger.info("[LLMEngine] 命中缓存，直接返回")
                    return self._deserialize_results(cached)

            # 2) 调用LLM（占位实现：返回 None 以触发回退）
            payload = {
                "table_type": table_type,
                "headers_norm": headers_norm,
                "sample_data": sample_data,
                "history_summary": None,
            }
            suggestions = self.llm_client.generate_mapping_suggestions(payload)

            if suggestions:
                results = self._deserialize_results(suggestions)
            else:
                # 3) 回退到 Smart
                results = self.smart_fallback.generate_smart_mapping(excel_headers, table_type, sample_data)

            # 4) 后处理（命名规范、唯一性等）
            results = self.post.apply(results)

            # 5) 写入缓存（尽量不包含敏感数据）
            if not disable_cache:
                try:
                    ser = self._serialize_results(results)
                    self.cache.set(headers_norm, table_type, sample_data, ser)
                except Exception:
                    pass

            return results
        except Exception as e:
            self.logger.warning(f"[LLMEngine] 失败，回退到 Smart: {e}")
            return self.smart_fallback.generate_smart_mapping(excel_headers, table_type, sample_data)

    # 序列化/反序列化简化为字典
    @staticmethod
    def _serialize_results(results: List[MappingResult]) -> List[Dict[str, Any]]:
        out: List[Dict[str, Any]] = []
        for r in results:
            out.append({
                "source_field": r.source_field,
                "target_field": r.target_field,
                "display_name": r.display_name,
                "data_type": r.data_type,
                "confidence": r.confidence,
                "confidence_level": r.confidence_level.value if hasattr(r.confidence_level, 'value') else str(r.confidence_level),
                "reasoning": r.reasoning,
                "is_required": r.is_required,
            })
        return out

    @staticmethod
    def _deserialize_results(items: List[Dict[str, Any]]) -> List[MappingResult]:
        results: List[MappingResult] = []
        for it in items or []:
            level = it.get("confidence_level", "medium")
            try:
                level_enum = MappingConfidence(level)
            except Exception:
                try:
                    level_enum = MappingConfidence(level.lower())
                except Exception:
                    level_enum = MappingConfidence.MEDIUM
            results.append(MappingResult(
                source_field=it.get("source_field", ""),
                target_field=it.get("target_field", it.get("source_field", "")),
                display_name=it.get("display_name", it.get("source_field", "")),
                data_type=it.get("data_type", "string"),
                confidence=float(it.get("confidence", 0.7)),
                confidence_level=level_enum,
                reasoning=it.get("reasoning", ""),
                is_required=bool(it.get("is_required", False)),
            ))
        return results
