# 2025-09-15｜“姓名/部门名称为空白”问题分析与修复策略

## 1. 现象
- 导入工资表后，UI 在以下子导航看到空白：
  - 全部在职人员、A岗职工：姓名为空
  - 退休人员、离休人员：姓名、部门名称为空

## 2. 时间线（按日志顺序）
- 08:20:46~54 应用启动完成，暂无 salary_data 表
- 08:20:59 打开“统一数据导入窗口”，导入器与映射器初始化
- 08:21:24 读取 Excel 多个 Sheet，保留原始中文表头
- 08:21:27~37 对四类工资表执行：
  - create_salary_data_table(动态模式，传入 df.columns)
  - save_dataframe_to_table：为“部门名称/姓名/人员代码”等写默认值（日志多次出现）
- 08:23:52~54 UI 加载数据：
  - DataFrame 列名为中文；重复“工号”被去重
  - 格式渲染 table_type 多次为 active_employees（即便显示退休/离休）
  - 最终中文显示列被选中，导致姓名/部门名称显示为空

## 3. 根因
- 工资表导入采用“动态模式建表”，将中文表头作为动态列写入表结构；
- 保存阶段对“表结构存在而 df 不含”的列写默认值，中文别名列被写空；
- 展示时优先选择中文显示列 → 出现空白。

## 4. 证据摘录
- 导入器对工资表分支使用动态模式：multi_sheet_importer → create_salary_data_table(..., columns=list(df.columns))
- 动态建表将传入列直接建为动态字段（DTM create_salary_data_table）
- 保存阶段默认值补齐命中中文别名列（DTM save_dataframe_to_table）
- 日志：多处“[FIX] 字段 部门名称/姓名 设置默认值”

## 5. 修复策略（分阶段）

### P0（短期止血：不改存储结构，保障显示）
- 渲染前字段合并（coalesce）：
  - employee_id = coalesce(employee_id, 工号, 人员代码)
  - employee_name = coalesce(employee_name, 姓名)
  - department = coalesce(department, 部门名称, 部门)
- 修正 table_type 识别：基于表名后缀映射到 {active/pension/retired/a_grade}
- 合并→去重→重排顺序：减少重复列与错配

### P1（中期修复：减少中文别名列与空值写入）
- 保存阶段：若英文规范列已存在且有值，不再对中文别名列写空；必要时同步为同值
- 动态建表前：先用 FieldRegistry 将可映射中文列规范化；能识别模板时优先专用模板建表

### P2（长期治理：单一真源与一致性）
- 全面采用专用模板（英文规范列唯一真源）
- 建立字段字典/别名中心，导入前统一列名
- FieldRegistry/Renderer 单例化与缓存

## 6. 变更清单（拟定，暂不执行）
- FormatRenderer/UnifiedFormatManager：新增“核心字段合并模块（P0）”；table_type 识别函数
- MultiSheetImporter：识别模板后优先 create_specialized_salary_table（P1）
- DynamicTableManager.save_dataframe_to_table：默认值补齐策略调整（P1）
- DynamicTableManager.create_salary_data_table：入参列名先规范化（P1）
- 文档：本问题记录与《数据流与字段规范》草案（已建立）

## 7. 验证计划
- 场景回放：对四类工资表导入；
- UI 断言：姓名/部门名称正常显示；金额列格式正确；
- 日志核对：不再出现“字段 姓名/部门名称 设置默认值”高频日志；
- 回归：分页、排序、表头宽度与用户偏好不回退。

## 8. 回滚预案
- P0 仅限读侧合并与类型识别，风险低；若异常可快速关闭合并逻辑开关（预留配置项），恢复当前版本行为。

## 9. 附：示意流程图

```mermaid
flowchart TD
  classDef node fill:#eef3f7,stroke:#888,color:#222;
  A[Excel中文表头]:::node --> B[导入器]
  B --> C[工资表-动态建表]
  C --> D[保存时默认值补齐]
  D --> E[中文列被写空]
  E --> F[UI使用中文显示列→空白]
```

