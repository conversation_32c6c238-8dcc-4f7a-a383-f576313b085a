# 大模型辅助字段映射方案梳理（最小改动接口与伪代码）

> 目的：在现有“智能映射”框架下，以最小改动引入大模型（LLM）能力，提升从 Excel 表头到数据库字段（清理/中文转英文/命名规范/去重消歧）的效果；同时保留模板/历史/规则引擎作为回退，确保可控与稳定。

- 原始文档：`docs/draft/20250905/大模型辅助字段映射方案.md`
- 梳理日期：2025-09-15

---

## 1. 现状与痛点小结

- 现有 `SmartMappingEngine`（模板/历史/语义）在缺乏样本数据、模板覆盖不足时，置信度普遍偏低（0.3~0.49）。
- 中文 → 英文归一与多列相似问题导致“数据库字段”重复严重，需 UI 侧兜底去重。
- 当前“智能映射”调用前未做统一的“匹配用温和标准化”，表头噪声影响匹配质量。

---

## 2. 设计目标

- 引入 `LLMMappingEngine`，作为 `SmartMappingEngine` 的并行策略；接口保持一致，支持灰度开关与回退。
- 在调用层增加：样本数据旁路、匹配用温和标准化、后处理强约束（命名规范/唯一性/保留字/长度）。
- 不改变“显示名称/数据库字段”的角色分离与 UI 一键去重修正兜底机制。

---

## 3. 新增与改动最小集合

- 新增模块（按目录结构）：
  - `src/modules/data_import/llm_mapping_engine.py`（LLMMappingEngine）
  - `src/modules/llm/llm_client.py`（统一 LLM 访问，支持云/本地、超时/重试）
  - `src/modules/data_import/name_normalizer.py`（`normalize_for_matching` 与 `enforce_db_field_naming`）
  - `src/modules/data_import/post_processor.py`（唯一性消歧/保留字/长度/冲突修正）
  - `src/modules/data_import/mapping_cache.py`（结果缓存）

- 轻改动（保持 UI 与现有引擎行为不破坏）：
  - `src/gui/unified_data_import_window.py` 中 `UnifiedMappingConfigWidget`：
    - 用工厂方法 `get_mapping_engine()` 替换直接构造，引入配置开关 `use_llm_mapping`。
    - 在 `_generate_smart_mapping()` 中采样前 N 行样本值，并通过 `sample_data` 传入 `generate_smart_mapping(...)`。

---

## 4. 接口草案（与现有保持对齐）

### 4.1 Mapping Engine 统一接口

```python
# 约定：与 SmartMappingEngine.generate_smart_mapping 对齐
class BaseMappingEngine(Protocol):
    def generate_smart_mapping(
        self,
        excel_headers: List[str],
        table_type: str,
        sample_data: Optional[Dict[str, List[Any]]] = None,
    ) -> List[MappingResult]:
        ...

@dataclass
class MappingResult:
    source_field: str
    target_field: str
    display_name: str
    data_type: str
    confidence: float
    confidence_level: str  # HIGH/MEDIUM/LOW
    reasoning: str
    is_required: bool
```

### 4.2 LLMMappingEngine（关键思路）

```python
class LLMMappingEngine(BaseMappingEngine):
    def __init__(self, llm_client: LLMClient, cache: MappingCache, post: PostProcessor,
                 normalizer: NameNormalizer, cfg: MappingConfig):
        ...

    def generate_smart_mapping(self, excel_headers, table_type, sample_data=None):
        # 1) 生成匹配用标准化副本（不改变原始 headers 含义）
        headers_norm = [self.normalizer.normalize_for_matching(h) for h in excel_headers]

        # 2) 查缓存（按 headers_norm + table_type + 可选样本摘要）
        cached = self.cache.get(headers_norm, table_type, sample_data)
        if cached:
            return cached

        # 3) 组织 Prompt（含表类型、字段头、每列样本摘要）并调用 LLMClient
        # 4) 解析结构化结果，转为 List[MappingResult]
        # 5) PostProcessor 统一后处理（命名规范、唯一性、长度/保留字校验）
        # 6) 写入缓存并返回
```

> 说明：以上 3)~6) 为实现建议步骤，确保可控、可回退、可缓存。

---

## 5. 约束与不改变的前提

- UI 侧保留“显示名称/数据库字段”分离，且继续支持一键去重兜底。
- 若 LLM 可用则优先使用；异常/超时/低置信度时回退至 `SmartMappingEngine`。
- 样本数据仅用于匹配与类型判断，建议脱敏/摘要化，防止数据泄露。

---

## 6. 实施要点（自检清单）

- 引擎选择：支持 `use_llm_mapping` 开关与工厂方法装配。
- 样本采样：每列 N 行（可配置）、必要的脱敏与统计摘要。
- 标准化策略：匹配用“温和标准化”与结果落地的“命名规范化”分离。
- 后处理：
  - 唯一性冲突处理（重命名策略）
  - 保留字与长度限制处理
  - 数据类型/字段类型一致性校验
- 缓存：按 `headers_norm + table_type + sample_signature` 键控；设置 TTL。
- 回退：遇到错误/低置信度自动回退，保证稳定与可控。

---

## 7. 与现有框架的对齐点

- 与 `SmartMappingEngine` 保持一致的输出结构与接口，减少对 UI 的影响。
- `UnifiedMappingConfigWidget._generate_smart_mapping()` 增强但不改变交互路径：
  - 采样 → 调用引擎 → 批量应用到 UI → 现有保存与校验机制接管。
- 继续复用模板匹配、历史学习与 UI 侧兜底去重，形成“LLM 优先、规则增强、可回退”的组合策略。

---

## 8. 参考

- 原始方案文档：`docs/draft/20250905/大模型辅助字段映射方案.md`
- 现有映射引擎：`src/gui/core/smart_mapping_engine.py`
- 主要调用点：`src/gui/unified_data_import_window.py` 中 `UnifiedMappingConfigWidget._generate_smart_mapping()`
