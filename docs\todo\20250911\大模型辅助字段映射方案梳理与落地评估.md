# 大模型辅助字段映射方案梳理与落地评估

> 目标：在不立即实施代码变更的前提下，基于现有代码与《大模型辅助字段映射方案》的设计，进行系统化梳理、差距评估、可行路径与分阶段计划输出，指导后续实施。

## 1. 现状总览（代码与流程）
- 技术栈：Python 3.8+，PyQt5。
- 核心模块：
  - `src/gui/core/smart_mapping_engine.py`：当前智能映射核心（模板/语义/历史融合），`generate_smart_mapping(headers, table_type, sample_data=None)` 返回 `MappingResult` 列表，同时提供 `generate_mapping` 兼容封装。
  - `src/gui/unified_data_import_window.py`：统一导入 UI，含 `UnifiedMappingConfigWidget`，内部实例化 `SmartMappingEngine()` 并调用 `generate_mapping` 于 `_generate_smart_mapping()`。
  - `src/core/field_mapping_manager.py` 与 `src/core/unified_mapping_service.py`：字段映射的持久化、校验、缓存与门面服务。
  - `src/core/unified_config/config_schema.py` 与 `src/core/config_manager.py`：统一配置的类型与系统配置读取/校验。
- 关键调用路径：
  - UI 中 `UnifiedMappingConfigWidget` 里 `self.mapping_engine = SmartMappingEngine()`，调用点在 `_generate_smart_mapping()` 使用 `generate_mapping(excel_fields, self.current_table_type)`。
  - UI 的 `load_excel_headers*` 系列方法负责表头加载与配置应用，已具备 `sample_data` 注入潜力（可由导入管理器获取样例数据）。

## 2. 方案要点回顾（来自《大模型辅助字段映射方案》）
- 并行新引擎：`LLMMappingEngine` 与现有 `SmartMappingEngine` 并存，接口一致（建议抽象 `BaseMappingEngine`）。
- 工厂选择：由配置项（如 `use_llm_mapping`）决定选择 LLM 或 Smart 引擎。
- 辅助模块：`NameNormalizer`、`PostProcessor`、`LLMClient`、`MappingCache`。
- 数据输入：传入 `excel_headers`、`table_type`、`sample_data`、上下文约束（命名规范、唯一性要求、保留字等）。
- 约束与后处理：统一命名规范（snake_case/或现行清洗规则）、唯一性、字段类型/数据类型一致性；对 LLM 输出进行 JSON Schema 校验与修剪。
- 回退链：LLM -> Smart -> 模板/历史 -> 规则默认。
- 配置与观测：超时重试、提示词策略、缓存策略、KPI（命中率、覆盖率、人工修正率、耗时）。

## 3. 差距分析（方案 vs 现有代码）
- 引擎可插拔：
  - 现状：UI 直接 `SmartMappingEngine()`，未通过工厂或接口抽象。
  - 方案：新增 `MappingEngineFactory`，依据配置决定返回 `Smart` 或 `LLM`；保留相同的 `generate_smart_mapping`/`generate_mapping` 签名。
- 样例数据注入：
  - 现状：`SmartMappingEngine.generate_smart_mapping` 已支持 `sample_data` 参数，但 UI 调用 `generate_mapping` 未传递。
  - 方案：在 `_generate_smart_mapping()` 中取当前 Sheet 的若干行样本（如前 50 行）传入。
- 一致性与后处理：
  - 现状：UI 已有严格清洗 `_sanitize_mapping_value`、类型一致性同步逻辑 `_sync_*`，并在加载/恢复时有校验与纠偏。
  - 方案：在引擎层增加统一 `post_processor`，确保输出即合规；UI 只需最小调整。
- 缓存与回退：
  - 现状：`unified_mapping_service` 与 `field_mapping_manager` 已有缓存/历史能力；UI 有模板/历史加载入口但未完全实现。
  - 方案：LLM 引擎内部先查 `MappingCache`；失败/超时回退 Smart；Smart 失败回退模板/默认。
- 配置开关与参数：
  - 现状：`config_manager` 可扩展；暂无 `use_llm_mapping`、`provider` 等项。
  - 方案：在系统配置中新增开关与策略项，默认关闭 LLM。

## 4. 接口契约（建议）
- `BaseMappingEngine`：
  - `generate_smart_mapping(headers: List[str], table_type: str, sample_data: Optional[List[Dict[str, Any]]] = None, constraints: Optional[Dict[str, Any]] = None) -> List[MappingResult]`
  - `generate_mapping(...) -> Dict[str, Any]`（向后兼容包装）。
- `MappingResult` 保持现有字段：`excel_field`、`target_field`、`field_type`、`confidence`、`reasoning`、可选 `display_name`、`data_type`。
- 约束 `constraints`：`naming_policy`、`reserved_words`、`unique_required`、`field_type_map`、`max_fields`、`table_context`。

## 5. 分阶段实施计划（最小侵入）
- Phase 0：准备与开关
  - 在 `config` 增加 `mapping.use_llm`（默认 false）、`mapping.provider`、`mapping.timeout_ms`、`mapping.cache_ttl_min` 等。
  - 新增 `MappingEngineFactory`，UI 改为 `self.mapping_engine = MappingEngineFactory.create(config)`。
- Phase 1：数据流与回退
  - 在 `_generate_smart_mapping()` 获取样本数据并传入；在工厂选择 LLM 时传 `constraints` 与 `sample_data`。
  - LLM 引擎内部：先查缓存 → 调用 LLM → 失败回退 Smart → 产出统一结果 → `post_processor` 标准化。
- Phase 2：观测与度量
  - 增加埋点：建议率、命中率、人工修正率、平均耗时；写入日志或轻量统计文件。
- Phase 3：模板/历史增强
  - 完成 UI 模板保存/加载；在失败回退链中纳入模板历史建议。
- Phase 4：治理与灰度
  - 分表类型灰度开关，黑白名单；冷启动场景的默认策略优化。

## 6. 风险与缓解
- LLM 不稳定/超时：超时→回退 Smart；调用失败不影响 UI 正常使用。
- 命名规范与唯一性冲突：在 `post_processor` 层做强校验与去重；必要时附加序号或回退原字段。
- 类型推断偏差：保持 UI 端 `_get_compatible_data_type` 与同步逻辑；建议值低置信度时不自动应用，仅标注理由。
- 性能与合规：缓存与速率限制；不在日志中记录敏感原文；可配置的匿名化处理。

## 7. 里程碑与验收
- M1（P0-P1）：
  - 工厂接入、样本注入、LLM 引擎骨架、回退到 Smart；不破坏现有 `generate_mapping` 流程。
  - 验收：UI 智能映射可用，LLM 关闭时行为与当前一致；开启时能返回建议（即便回退）。
- M2（P2）：
  - 观测指标完备、简易可视化或日志统计；模板/历史接口打通。
  - 验收：可导出统计；模板/历史命中率达预期。
- M3（P3-P4）：
  - 灰度、黑白名单、策略优化；问题字段诊断报告。

## 8. 实施建议（落地贴合点）
- 注入点已定位：`UnifiedMappingConfigWidget` 的 `__init__` 与 `_generate_smart_mapping()`。
- 保持现有 UI 行为，先在引擎层做“尽可能合规”的输出，再由 UI 最小化调整。
- 首选“最小改动”策略：
  - 新增 `MappingEngineFactory` 与 `LLMMappingEngine` 文件；
  - UI 两处改动：实例化改为工厂、调用时补充 `sample_data`；
  - 配置项新增但默认关闭。

## 9. 后续工作清单（用于实施阶段）
- [ ] 新建：`src/gui/core/mapping_engine_factory.py`（返回 `Smart` 或 `LLM`）。
- [ ] 新建：`src/gui/core/llm_mapping_engine.py`（含缓存、客户端、后处理骨架）。
- [ ] 新建：`src/gui/core/name_normalizer.py`、`post_processor.py`、`mapping_cache.py`、`llm_client.py`（可按模块化/子包收敛）。
- [ ] 修改：`UnifiedMappingConfigWidget` 构造与 `_generate_smart_mapping()`，注入 `sample_data` 与 `constraints`。
- [ ] 修改：`config/*.json` 与 `ConfigManager`，加入 `mapping` 段配置与默认值。
- [ ] 增加：日志埋点与指标聚合脚本（可放 `scripts/`）。

---
更新时间：2025-09-15
负责人：——
