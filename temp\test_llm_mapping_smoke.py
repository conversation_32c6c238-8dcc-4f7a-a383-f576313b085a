import json
import os

from src.gui.core.mapping_engine_factory import get_mapping_engine

# Load config and force-enable LLM for this smoke test
with open('config/advanced_settings.json', 'r', encoding='utf-8') as f:
    cfg = json.load(f)

cfg.setdefault('llm', {})
cfg['llm']['use_llm_mapping'] = True
cfg['llm'].setdefault('provider', 'anthropic')
cfg['llm'].setdefault('api_base', 'https://open.bigmodel.cn/api/anthropic')
cfg['llm'].setdefault('model', 'glm-4.5')
cfg['llm']['timeout_seconds'] = 30
cfg['llm']['max_retries'] = 2
cfg['llm']['disable_cache'] = True

headers = ["姓名", "工号", "基本工资", "津贴", "入职日期", "实发合计"]

sample_data = {
    "姓名": ["NAME", "NAME"],
    "工号": ["ID12345"],
    "基本工资": ["NUM", "NUM"],
    "津贴": ["NUM"],
    "入职日期": ["DATE"],
    "实发合计": ["NUM"],
}

engine = get_mapping_engine(cfg)

# Try to call mapping with sample data
result = engine.generate_mapping(headers, "💰 工资表", sample_data=sample_data)

print("=== LLM Mapping Smoke Result ===")
print(json.dumps(result, ensure_ascii=False, indent=2))

# Show provider and env key presence for diagnostics
print("Provider:", cfg['llm']['provider'])
print("API Base:", cfg['llm']['api_base'])
print("Model:", cfg['llm']['model'])
print("Has API Key:", bool(os.getenv('ANTHROPIC_API_KEY')))
print("Disable Cache:", cfg['llm']['disable_cache'])
