"""
映射引擎工厂
按高级配置选择合适的映射引擎（当前阶段：仅返回 SmartMappingEngine，占位支持 LLM 开关）。
"""
from __future__ import annotations

from src.modules.config.advanced_config_manager import AdvancedConfigManager, ConfigLevel
import json
import os
from typing import Any, Dict, Optional

from .smart_mapping_engine import SmartMappingEngine
try:
    from src.modules.data_import.llm_mapping_engine import LLMMappingEngine  # type: ignore
except Exception:  # pragma: no cover
    LLMMappingEngine = None  # type: ignore

# 预留：后续可引入实际的 LLM 引擎实现
# try:
#     from src.modules.ai.llm_mapping_engine import LLMMappingEngine  # noqa: F401
# except Exception:
#     LLMMappingEngine = None  # type: ignore


def _load_advanced_config_from_manager() -> Dict[str, Any]:
    """优先从 AdvancedConfigManager (config_advanced/) 读取高级配置（USER层）。失败则返回空字典。"""
    try:
        mgr = AdvancedConfigManager(base_dir="config_advanced")
        cfg = mgr.get_config("advanced_settings", level=ConfigLevel.USER, use_cache=True) or {}
        return cfg
    except Exception:
        return {}

def _load_advanced_config() -> Dict[str, Any]:
    cfg_path = os.path.join("config", "advanced_settings.json")
    try:
        if os.path.exists(cfg_path):
            with open(cfg_path, "r", encoding="utf-8") as f:
                return json.load(f)
    except Exception:
        pass
    return {}


def get_mapping_engine(config: Optional[Dict[str, Any]] = None):
    """
    获取映射引擎实例。
    当前阶段：总是返回 SmartMappingEngine；保留 LLM 开关判断以便后续接入。
    """
    # 优先通过配置管理器读取，失败再回退到旧文件
    cfg = config or _load_advanced_config_from_manager() or _load_advanced_config()
    llm_cfg = (cfg or {}).get("llm", {})
    use_llm = bool(llm_cfg.get("use_llm_mapping", False))

    # 如果启用LLM映射，优先返回LLM引擎（失败则回退）
    if use_llm and LLMMappingEngine is not None:
        try:
            return LLMMappingEngine(config=cfg or {})
        except Exception:
            # 回退到Smart
            pass

    engine = SmartMappingEngine()

    # 将部分配置注入（即使 Smart 阶段不完全使用，也避免属性缺失）
    smart_cfg = (cfg or {}).get("smart_recommendations", {})
    try:
        if hasattr(engine, "update_config"):
            engine.update_config({
                "smart_recommendations": smart_cfg,
                "llm": llm_cfg,
            })
        if hasattr(engine, "set_confidence_threshold"):
            thr = float(smart_cfg.get("confidence_threshold", 70)) / 100.0
            engine.set_confidence_threshold(thr)
    except Exception:
        pass

    return engine

__all__ = ["get_mapping_engine"]
