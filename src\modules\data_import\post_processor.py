"""
PostProcessor - LLM与Smart映射结果后处理
- 目标：命名规范化、唯一性冲突、保留字/长度限制（简单版）
"""
from __future__ import annotations

from typing import List

from src.modules.logging.setup_logger import setup_logger
from src.modules.data_import.name_normalizer import NameNormalizer
from src.gui.core.smart_mapping_engine import MappingResult


class PostProcessor:
    def __init__(self):
        self.logger = setup_logger(__name__)
        # 简单保留字列表（可扩展）
        self.reserved = {"select", "from", "where", "group", "order", "table"}

    def apply(self, results: List[MappingResult]) -> List[MappingResult]:
        """对 target_field 与 display_name 做规范化并确保唯一性"""
        seen = {}
        processed: List[MappingResult] = []
        for r in results:
            tf = r.target_field or r.source_field
            tf_norm = NameNormalizer.enforce_db_field_naming(tf)
            if tf_norm in self.reserved:
                tf_norm = f"f_{tf_norm}"
            # 唯一化
            base = tf_norm
            i = 2
            while tf_norm in seen:
                tf_norm = f"{base}_{i}"
                i += 1
            seen[tf_norm] = True

            # display_name 保持原语义，轻度清洗
            disp = r.display_name or r.source_field
            # 写回
            processed.append(MappingResult(
                source_field=r.source_field,
                target_field=tf_norm,
                display_name=disp,
                data_type=r.data_type,
                confidence=r.confidence,
                confidence_level=r.confidence_level,
                reasoning=r.reasoning,
                is_required=r.is_required,
            ))
        return processed
