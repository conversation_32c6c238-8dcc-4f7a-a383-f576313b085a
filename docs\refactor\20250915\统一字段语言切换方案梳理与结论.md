# 统一字段语言（中文/英文）切换方案梳理与结论

更新时间：2025-09-15
适用范围：工资表与异动表的导入、建表、保存、读取、渲染全链路

---

## 1. 背景与目标
- 目标：在“统一数据导入配置”窗口的“字段映射”中，统一配置数据库字段语言（中文或英文）。
  - 选择中文：数据库物理列名落地为中文，读取与显示全流程“中文化”。
  - 选择英文：数据库物理列名落地为英文，读取与显示全流程“英文化”。
- 设计约束：尽量复用现有“英文字段作为规范标识（field_id）”的生态（字段类型、显示顺序、模板、渲染等），在“物理列名”层做可配置切换。

---

## 2. 现状证据与约束（基于代码与日志）
- FieldMappingManager（state/data/field_mappings.json）
  - 默认结构为 {db_field(英文id): display_name(中文)}；存在 global_settings.preserve_chinese_headers，但缺少“db字段语言”选项。
  - 接口多以“英文字段名”为核心键值组织映射、类型、显示顺序。
- FieldRegistry + FormatRenderer
  - 构建“中文显示名 → 英文字段id”的反向映射，渲染时以英文字段id为规范（类型与格式规则绑定英文id）。
- MultiSheetImporter + DynamicTableManager（DTM）
  - 工资表导入：走 create_salary_data_table（动态模式），把 DataFrame 列（多为中文）直接作为动态列写入表结构。
  - 专用模板：create_specialized_salary_table 以模板字段（英文name+中文描述）规范建表。
  - 保存：save_dataframe_to_table 的默认值补齐显式针对“英文字段id”（employee_name/department/remarks 等），同时也可能为“中文别名列”写默认值。
- 结论：系统“以英文字段id为内在规范标识”的事实较强，中文主要作为显示名或输入列别名；要支持“物理列名中/英可切换”，宜在“库表物理层”加一层映射，而不改变内部id体系。

---

## 3. 总体设计思想：field_id 为核心 + NameResolver 名称解析层
- 保持“英文字段名 = field_id = 规范标识”（类型、显示顺序、规则、模板均围绕它）。
- 新增 NameResolver：在“存储（SQL/建表/保存）”与“读取（查询/DataFrame）”边界做“field_id ⇄ 物理列名（中文/英文）”的双向映射。
- 配置驱动：在 FieldMappingManager 配置中引入“db_field_language（zh|en）”与 per-table 的“db_column_map: {field_id: db_column_name}”。
- 动态/模板一致化：
  - 模板建表：模板字段id经 NameResolver 生成物理列名（随语言切换）。
  - 动态列：可识别的中文列先标准化为 field_id，再经 NameResolver 落地；不可识别“自由列”依据语言策略生成稳定的物理列名，并登记映射（display_name 保留原中文）。

---

## 4. 参考数据流（加入 NameResolver）
```mermaid
flowchart TD
  classDef node fill:#f5f7fa,stroke:#999,color:#222;
  A[Excel列(多为中文)]:::node --> B[导入器: 列映射→field_id]:::node
  B --> C{NameResolver\n(field_id⇄物理列)}:::node
  C -->|zh| D[物理列=中文]:::node
  C -->|en| E[物理列=英文]:::node
  D --> F[DTM建表/保存(默认值按 field_id)]:::node --> G[SQLite]:::node
  E --> F
  G --> H[读取DataFrame(物理列)]:::node --> I[反向映射→field_id]:::node --> J[格式渲染/显示]:::node
```

---

## 5. 关键接口与配置设计（草案）
- 全局配置（FieldMappingManager.global_settings）新增：
  - db_field_language: 'zh' | 'en'
  - display_language: 'zh' | 'en'（可选，将来支持英文显示）
- 表级配置（table_mappings[table_name]）新增：
  - field_mappings: { field_id: display_name }
  - db_column_map: { field_id: db_column_name }  ← 新增，受 db_field_language 驱动
  - field_types/hidden_fields/display_order 仍以 field_id 为键
- NameResolver 能力：
  - to_db_column(field_id, table_name) -> db_column_name
  - to_field_id(db_column_name, table_name) -> field_id
  - 处理自由列：确保唯一性（冲突检测、自动标准化/拼音/哈希后缀）、可逆映射
- 导入器：
  - 在保存前尽量将 DataFrame 列规范化为 field_id（借助 FieldRegistry 反向映射）；未识别列以“自由列策略”处理。
- DTM：
  - 建表/索引/SQL 生成统一通过 NameResolver 产出物理列名；
  - 默认值补齐按 field_id 逻辑判断，再转为物理列名执行；
  - 专用模板与动态模式统一走 NameResolver。

---

## 6. 分阶段实施计划
- P0（配置化止血，优先支持“新导入表”）：
  1) 扩展 FieldMappingManager 配置：db_field_language 与 db_column_map
  2) 导入器保存前规范化列为 field_id；
  3) DTM 引入 NameResolver，建表/保存/默认值/索引全部经 NameResolver 生成物理列；
  4) 渲染链路保持不变（已有中文列→英文字段id反向映射能力）。
- P1（双向兼容与统一）：
  1) 统一动态/模板建表的名称解析；
  2) 完善自由列命名策略与映射持久化；
  3) TableMetadata/SQL/统计等模块去硬编码列名，一律通过 NameResolver；
  4) 导入窗口“字段映射”页显示语言模式与校验提示。
- P2（库级治理与迁移）：
  1) 提供列名迁移工具（中文⇄英文）：预案生成→人工确认→执行→回滚；
  2) 建议长期采用“专用模板建表 + 动态列标准化”，减少动态中文列增殖。

---

## 7. 风险与对策
- 风险：
  - 模块硬编码英文字段名（employee_id 等）→ 需统一经 NameResolver；
  - 自由列命名冲突与可逆性 → 需唯一化算法与映射固化；
  - 旧表兼容（已混用中英文列）→ 短期靠读取映射与字段合并；中期迁移。
- 对策：
  - 引入单元测试覆盖导入/建表/保存/读取/渲染在 zh/en 模式下的一致性；
  - 为 NameResolver 增加小缓存与冲突检测；
  - 迁移工具生成报告到 docs/problems，并将脚本置于 temp/，不放根目录。

---

## 8. 时序图（一次导入与读取）
```mermaid
sequenceDiagram
  autonumber
  participant UI as 导入窗口
  participant FMM as 映射配置
  participant MSI as 导入器
  participant DTM as 表管理(NameResolver)
  UI->>FMM: 设置 db_field_language = zh/en
  UI->>MSI: Excel列映射→field_id
  MSI->>DTM: 建表/保存(field_id 集)
  DTM->>DTM: field_id→物理列 + 默认值按field_id
  DTM->>DB: 提交
  UI->>DTM: 读取表
  DTM->>UI: DataFrame(物理列) → 反向映射→field_id → 渲染
```

---

## 9. 结论
- 通过“field_id为核心 + NameResolver 名称解析 + 配置化 db_field_language”，可在不破坏既有英文字段id生态的前提下，实现“数据库物理列名中文/英文”全链路切换。
- 建议先落地 P0（成本低、收益高），验证可用性与风险面；随后推进 P1 统一与 P2 迁移治理，最终实现“中文化/英文化”可配置与可迁移的完整能力。

